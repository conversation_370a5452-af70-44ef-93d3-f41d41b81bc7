<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		$link='&my=search&column='.$get['column'].'&value='.$get['value'];
		}
	}else{
	$sql=" 1";
	}
}else{
	$sql=" 1";
}
$numrows=$DB->getColumn("SELECT count(*) from account WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>账号</th>
                	<th>所属代理</th>
                	<th>账号状态</th>
					<th>上次登陆设备</th>
					<th>上次登陆IP</th>
					<th>上次登陆地点</th>
				
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM account WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	$agentcheck = $DB->query("SELECT * FROM `admin` WHERE `id` = '".$res['agentid']."' ")->fetch();
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['username'].'</td>
<td>'.$agentcheck['username'].'</a></td>
<td>'.($res['status']==1?'<span class="label label-danger">正常</span>':'<span class="label label-dark">封禁</span>').'</td>
<td><span class="label label-secondary">'.$res['device'].'</span></td>
<td>'.$res['ip'].'</td>
<td>'.$res['city'].'</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
