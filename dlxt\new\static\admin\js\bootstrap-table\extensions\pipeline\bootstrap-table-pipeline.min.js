/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTablePipeline={exports:{}}.exports}})(this,function(){'use strict';(function(a){var b=a.fn.bootstrapTable.utils;a.extend(a.fn.bootstrapTable.defaults,{usePipeline:!1,pipelineSize:1e3,onCachedDataHit:function(){return!1},onCachedDataReset:function(){return!1}}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"cached-data-hit.bs.table":'onCachedDataHit',"cached-data-reset.bs.table":'onCachedDataReset'});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.init,e=c.prototype.initServer,f=c.prototype.onSearch,g=c.prototype.onSort,h=c.prototype.onPageListChange;c.prototype.init=function(){this.initPipeline(),d.apply(this,Array.prototype.slice.apply(arguments))},c.prototype.initPipeline=function(){this.cacheRequestJSON={},this.cacheWindows=[],this.currWindow=0,this.resetCache=!0},c.prototype.onSearch=function(){this.options.usePipeline&&(this.resetCache=!0),f.apply(this,Array.prototype.slice.apply(arguments))},c.prototype.onSort=function(){this.options.usePipeline&&(this.resetCache=!0),g.apply(this,Array.prototype.slice.apply(arguments))},c.prototype.onPageListChange=function(b){var c=a(b.currentTarget),d=parseInt(c.text());this.options.pipelineSize=this.calculatePipelineSize(this.options.pipelineSize,d),this.resetCache=!0,h.apply(this,Array.prototype.slice.apply(arguments))},c.prototype.calculatePipelineSize=function(a,b){return 0==b?0:Math.ceil(a/b)*b},c.prototype.setCacheWindows=function(){this.cacheWindows=[];for(var a,c=this.options.totalRows/this.options.pipelineSize,d=0;d<=c;d++)a=d*this.options.pipelineSize,this.cacheWindows[d]={lower:a,upper:a+this.options.pipelineSize-1}},c.prototype.setCurrWindow=function(a){this.currWindow=0;for(var b=0;b<this.cacheWindows.length;b++)if(this.cacheWindows[b].lower<=a&&a<=this.cacheWindows[b].upper){this.currWindow=b;break}},c.prototype.drawFromCache=function(b,c){var d=a.extend(!0,{},this.cacheRequestJSON),e=b-this.cacheWindows[this.currWindow].lower;return d.rows=d.rows.slice(e,e+c),d},c.prototype.initServer=function(c,d,e){var f={},g=this.header.fields.indexOf(this.options.sortName),h={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder},i=null;if(this.header.sortNames[g]&&(h.sortName=this.header.sortNames[g]),this.options.pagination&&'server'===this.options.sidePagination&&(h.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,h.pageNumber=this.options.pageNumber),e||this.options.url||this.options.ajax){var j=!0;if('limit'===this.options.queryParamsType&&(h={searchText:h.searchText,sortName:h.sortName,sortOrder:h.sortOrder},this.options.pagination&&'server'===this.options.sidePagination))if(h.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,h.offset=(this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize)*(this.options.pageNumber-1),!this.options.usePipeline)0===h.limit&&delete h.limit;else if(!this.cacheWindows.length)j=!0,h.drawOffset=h.offset;else{var k=this.cacheWindows[this.currWindow];this.resetCache||h.offset<k.lower||h.offset>k.upper?(j=!0,this.setCurrWindow(h.offset),h.drawOffset=h.offset,h.offset=this.cacheWindows[this.currWindow].lower):j=!1}if(this.resetCache&&(j=!0,this.resetCache=!1),this.options.usePipeline&&j&&(h.drawLimit=h.limit,h.limit=this.options.pipelineSize),!j){var l=this.drawFromCache(h.offset,h.limit);return this.load(l),this.trigger('load-success',l),void this.trigger('cached-data-hit',l)}if(a.isEmptyObject(this.filterColumnsPartial)||(h.filter=JSON.stringify(this.filterColumnsPartial,null)),f=b.calculateObjectValue(this.options,this.options.queryParams,[h],f),a.extend(f,d||{}),!1!==f){c||this.$tableLoading.show();var m=this;i=a.extend({},b.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:e||this.options.url,data:'application/json'===this.options.contentType&&'post'===this.options.method?JSON.stringify(f):f,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(d){d=b.calculateObjectValue(m.options,m.options.responseHandler,[d],d),m.options.usePipeline&&(m.cacheRequestJSON=a.extend(!0,{},d),m.options.totalRows=d[m.options.totalField],m.setCacheWindows(),m.setCurrWindow(h.drawOffset),d=m.drawFromCache(h.drawOffset,h.drawLimit),m.trigger('cached-data-reset',d)),m.load(d),m.trigger('load-success',d),c||m.$tableLoading.hide()},error:function(a){var b=[];'server'===m.options.sidePagination&&(b={},b[m.options.totalField]=0,b[m.options.dataField]=[]),m.load(b),m.trigger('load-error',a.status,a),c||m.$tableLoading.hide()}}),this.options.ajax?b.calculateObjectValue(this,this.options.ajax,[i],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=a.ajax(i))}}},a.fn.bootstrapTable.methods.push()})(jQuery)});