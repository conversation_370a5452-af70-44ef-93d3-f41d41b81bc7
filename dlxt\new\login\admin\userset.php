<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='addAgent'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addAgent(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="" placeholder="请输入6-18位账号" />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="请输入6-18位密码" />
            </div>
			<div class="form-group col-md-12">
				<label>上级代理</label>
				<select name="lastuid" class="form-control select2" id="lastuid" >
				<?php
				$rs=$DB->query("SELECT * FROM admin order by id");
				while($res = $rs->fetch())
				{
				$lastuid = explode(';',$res['lastuid']);
				$agents = explode('-',$lastuid[1]);
				$agentlv = count($agents)-1;
				$agentlv = $agentlv==0?'管理员':$agentlv.'级代理';
				echo '<option value="'.$res['id'].'">　'.$agentlv.'-'.$res['username'].'-'.$res['fencheng'].'%</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#lastuid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>邀请码</label>
              <input type="text" class="form-control" name="invite" value="" placeholder="请输入8位以内邀请码" />
            </div>
            <div class="form-group col-md-12">
              <label>分成信息</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="fencheng" placeholder="请输入分成信息" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">%</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='edituser'){
	$id = intval($get['id']);
	$checkuser = $Admin->getUserId($id);
	if(!$checkuser){echo "<script>layer.ready(function(){layer.msg('该账号信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return edituser(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="<?php echo $checkuser['username']; ?>" placeholder="请输入6-18位账号" readonly />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="不修改请留空" />
            </div>
			<div class="form-group col-md-12">
				<label>账号状态</label>
				<select name="status" class="form-control">
				<?php
				 $option = $checkuser['status']==1?'<option value="1"  selected = "selected">正常</option><option value="0" >封禁</option>':'<option value="0"  selected = "selected">封禁</option><option value="1" >正常</option>';
				echo $option;
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>所属代理</label>
				<select name="agentid" class="form-control select2" id="agentid" >
				<?php
				$rs=$DB->query("SELECT * FROM admin order by id");
				while($res = $rs->fetch())
				{
					if( $checkuser['agentid'] == $res['id']){
						echo '<option value="'.$res['id'].'" selected = "selected">'.$res['username'].'</option>';	
					}else{
						echo '<option value="'.$res['id'].'">'.$res['username'].'</option>';	
					}
				}
				?> 
				</select>
				<script>var selectorx = $('#agentid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='bindEdit'){
	$id = intval($get['id']);
	$bindcheck = $DB->query("SELECT * FROM `binds` WHERE `id` = '".$id."' ")->fetch();
	if(!$bindcheck){echo "<script>layer.ready(function(){layer.msg('该绑定信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	if($bindcheck['lastday'] == $date){
		$daycharge = $bindcheck['daycharge'];
	}else{
		$daycharge = 0;
	}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return bindEdit(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>角色名称</label>
              <input type="text" class="form-control" name="name" value="<?php echo $bindcheck['name']; ?>" placeholder="请填写角色名称" />
              <input type="text" class="form-control"  name="id" value="<?php echo $id; ?>" style="display:none"/>
            </div>
            <div class="form-group col-md-12">
              <label>角色ID</label>
              <input type="number" class="form-control" name="roleid" value="<?php echo $bindcheck['roleid']; ?>" placeholder="请填写角色ID" />
            </div>
            <div class="form-group col-md-12">
              <label>所属大区</label>
				<select name="serverid" class="form-control">
				<?php
				$serverscheck = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindcheck['serverid']."' ")->fetch();
				echo '<option value="'.$bindcheck['serverid'].'"  selected = "selected">当前所属：'.$serverscheck['name'].'</option>';
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
            </div>
            <div class="form-group col-md-12">
              <label>平台币</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="money" value="<?php echo $bindcheck['money']; ?>" placeholder="请输入平台币信息">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>角色累计</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="charge" value="<?php echo $bindcheck['charge']; ?>" placeholder="请输入角色累计信息">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>今日累计</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="daycharge" value="<?php echo $daycharge; ?>" placeholder="请输入今日累计信息">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function addAgent(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addAgent',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function edituser(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=edituser',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function bindEdit(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=bindEdit',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>