/**
 * 重复一个字符串
 * @param  {string}  str 字符串
 * @param  {integer} num 重复次数
 * @return {string}
 */
function str_repeat(str, num) {
	var str = str ? str : '',
		num = num > 0 ? num : 0,
		res = '';
	if (num < 1) return '';
	for(var i = 0; i < num; i++) {
		res += str;
	}
	return res;
}

/**
 * 获取随机字符串
 * @param  {int}    len 长度
 * @return {string}     字符串
 */
function randChar(len) {
    var str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz',
        max = str.length - 1,
        arr = str.split(''),
        rstr = Array();
    var len = len ? len : 6;
    for (var i = 0; i < len; i++) {
        var num = Math.ceil(Math.random() * max);
        rstr.push(arr[num]);
    }
    return rstr.join('');
}

/**
 * 对象转数组
 * @param  {object} arr 对象
 * @return {array}
 */
function obj_array(arr) {
	if (typeof(arr) != 'object') return arr;
	var newarr = [];
	for (var x in arr) {
		newarr.push(arr[x]);
	}
	return newarr;
}

/**
 * 判断字符串是否在一个数组里
 * @param  {string} str 字符串
 * @param  {array}  arr 数组
 * @return {true|false}
 */
function in_array(str, arr) {
    if (typeof(arr) !== 'object') return false;
    if (str == '' || str.length < 1) return false;
    if (typeof(str) === 'object') {
        var state = true;
        for (var j = 0; j < str.length; j++) {
            for (var i = 0; i < arr.length; i++) {
                if (arr[i] != str[j]) {
                    state = false;
                    break;
                }
            }
            if (!state) break;
        }
        return state;
    }else {
        var state = false;
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == str) {
                state = true;
                break;
            }
        }
        return state;
    }
}

/**
 * 去除空格
 * @param  {string} str 字符串
 * @return {string}     字符串
 */
function trim(str){
    return $.trim(str); 
}

/**
 * URL操作函数
 * @param  {String} type 类型
 * @param  {String} url  地址
 * @param  {string} name 名称
 * @return string
 */
function uri_param(type, url, name) {
    if (!type) return false;
    switch(type) {
        case 'get'://获取一个参数值
            if (url == '' || url.indexOf('?') == -1) return false;
            var str = false;
            var tmp = url.split('?');
            tmp = tmp[1];
            if (tmp.indexOf('&') == -1) {
                //如果不存在&，则表示只有一个
                tmp = tmp.split('=');
                if (tmp && tmp[0] == name) {
                    str = tmp[1];
                }
            }else {
                tmp = tmp.split('&');
                for (var i = 0; i < tmp.length; i++) {
                    var item = tmp[i].split('=');
                    if (item && item[0] == name) {
                        str = item[1];
                        break;
                    }
                }
            }
            return str;
            break;
        case 'set'://设置增加参数
            if (typeof(name) != 'object') return url;
            //判断参数是否存在，存在则剔除
            var res = Array();
            for (x in name) {
                if (url.indexOf(x+'=') === -1) {
                    res.push( x+'='+name[x] );
                }
            }
            if (res.length < 1) return url;
            res = res.join('&');
            //判断当前地址有没有?问号
            var Joiner = url.indexOf('?') == -1 ? '?' : '&';
            return url + Joiner + res;
            break;
        case 'delete'://删除一个参数
            if (url == '' || url.indexOf('?') === -1) return url;
            var tmp = '', tmp_url = '', tmp_param = Array();
            tmp = url.split('?');
            tmp_url = tmp[0];
            if (tmp[1].indexOf('&') === -1) {
                //如果不存在&，则表示只有一个
                tmp = tmp[1].split('=');
                if (tmp && tmp[0] != name) {
                    tmp_param.push(tmp[0]+'='+tmp[1]);
                }
            }else {
                tmp = tmp[1].split('&');
                for (i = 0; i < tmp.length; i++) {
                    var tmp_item = tmp[i].split('=');
                    if (tmp_item && tmp_item[0] != name) {
                        tmp_param.push( tmp_item[0]+'='+tmp_item[1] );
                    }
                }
            }
            var _param = false;
            if (tmp_param.length > 0) {
                _param = tmp_param.join('&');
            }
            tmp_url = _param ? tmp_url+'?'+_param : tmp_url;
            return tmp_url;
            break;
    }
}

/**
 * 判断设备类型，移动端和PC端
 * @return string
 */
function browserRedirect() {
    var sUserAgent = navigator.userAgent.toLowerCase();
    var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
    var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
    var bIsMidp = sUserAgent.match(/midp/i) == "midp";
    var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
    var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
    var bIsAndroid = sUserAgent.match(/android/i) == "android";
    var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
    var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";
    if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
        return 'mobile';
    } else {
        return 'pc';
    }
}

/**
 * 倒计时
 * @param  {integer}   stime    开始时间戳
 * @param  {integer}   etime    结束时间戳
 * @param  {Function} callback  回调函数
 */
function countdown(stime, etime, callback) {
    var dtime = new Date();
    dtime = dtime.getTime() / 1000;
    if (stime > dtime) {
        if (typeof(callback) == 'function') {
            callback(3);//未开始
        }
        setTimeout(function() {
            countdown(stime, etime, callback);
        }, 1000);
        return false;
    }
    if (etime > dtime) {
        var ntime = etime - dtime;
        var res = {
            second : Math.floor(ntime % 60),
            minite : Math.floor(ntime / 60 % 60),
            hour : Math.floor(ntime / 3600 % 24),
            day : Math.floor(ntime / 3600 / 24)
        };
        res.second = res.second < 10 ? '0'+res.second : res.second;
        res.minite = res.minite < 10 ? '0'+res.minite : res.minite;
        res.hour = res.hour < 10 ? '0'+res.hour : res.hour;
        if (typeof(callback) == 'function') {
            callback(1, res);//进行中
        }
        setTimeout(function() {
            countdown(stime, etime, callback);
        }, 1000);
    }else {
        if (typeof(callback) == 'function') {
            callback(2);//已结束
        }
        return false;
    }
}

/**
 * 倒计时
 * @param  {integer} second 秒
 * @param  {string}  ele    元素
 */
function countSecondDown(second, ele) {
    if (second > 0) {
        $(ele).text('('+second+')后重新获取').prop('disabled', true);
        second -= 1;
        setTimeout(function() {
            countSecondDown(second, ele);
        }, 1000);
    }else {
        $(ele).text('获取验证码').prop('disabled', false);
    }
}

var cookies = {
    /**
     * 创建cookie
     * @param  {string} c_name     cookie名称
     * @param  {string} value      值
     * @param  {int}    expiredays 过期时间
     */
    add : function(c_name, value, expiredays) {
        var exdate = new Date();
        exdate.setDate(exdate.getDate()+expiredays);
        document.cookie = c_name+'='+escape(value)+((expiredays==null) ? "" : "; expires="+exdate.toGMTString())+"; path=/";
    },

    /**
     * 删除cookie
     * @param  {string} c_name 名称
     */
    del : function(c_name) {
        var date = new Date();
        date.setTime(date.getTime() - date.getTime());
        document.cookie = c_name + "=a; expires=" + date.toGMTString()+"; path=/";
    },

    /**
     * 获取cookie
     * @param  {string} c_name 名称
     * @return {string}        值
     */
    get : function(c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name+"=");
            if (c_start != -1) {
                c_start = c_start + c_name.length+1;
                c_end = document.cookie.indexOf(";",c_start);
                if (c_end==-1) c_end = document.cookie.length;
                return unescape(document.cookie.substring(c_start,c_end));
            }else {
                return null;
            }
        }else {
            return null;
        }
    }
};

/**
 * 身份证合法性验证
 * @param  {string}  idCard 身份证号码
 * @return {Boolean}
 */
function isValidIdCard(idCard){
    if (idCard == '') return false;
    var ret = false;
    var w = [ 7 , 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ];
    //身份证号码长度必须为18，只要校验位正确就算合法
    var crc = idCard.substring(17);
    var a = new Array();
    var sum=0;
    for (var i = 0; i < 17; i++){
        a.push(idCard.substring(i, i+1));
        sum += parseInt(a[i], 10) * parseInt(w[i], 10);
    }
    sum %= 11;
    var res = "-1";
    switch (sum){
        case 0 : res = "1"; break;
        case 1 : res = "0"; break;
        case 2 : res = "X"; break;
        case 3 : res = "9"; break;
        case 4 : res = "8"; break;
        case 5 : res = "7"; break;
        case 6 : res = "6"; break;
        case 7 : res = "5"; break;
        case 8 : res = "4"; break;
        case 9 : res = "3"; break;
        case 10 : res = "2"; break;
    }
    if (crc.toLowerCase() == res.toLowerCase()) {
        ret = true;
    }
    return ret;
}

/**
 * 验证组织机构代码是否合法：组织机构代码为8位数字或者拉丁字母+“-”+1位校验码。
 * @param  {string}  orgCode 代码证
 * @return {Boolean}
 */
function isValidOrgCode(value) {
    if (value != ""){
        var values = value.split("-");
        var ws = [3, 7, 9, 10, 5, 8, 4, 2];  
        var str = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';  
        var reg = /^([0-9A-Z]){8}$/;
        var code = values[0]; 
        if (!reg.test(code)) {  
            return false;
        }  
        var sum = 0;  
        for (var i = 0; i < 8; i++) {  
            sum += str.indexOf(code.charAt(i)) * ws[i];  
        }  
        var C9 = 11 - (sum % 11);
        if (C9 == 11) {  
            C9 = '0';  
        } else if (C9 == 10) {  
            C9 = 'X';
        }
        if (values[1].length != 1) return false;
        return C9 != code.charAt(9);
    }
    return false;
}

/**
 * 验证营业执照是否合法：营业执照长度须为15位数字
 * @param  {string}  Code 营业执照号
 * @return {Boolean}
 */
function isValidBusCode(Code) { 
    var patrn = /^[0-9A-Z]+$/;
    var ret = true;
    //18位校验及大写校验
    if ((Code.length != 18) || (patrn.test(Code) == false)) { 
        ret = false;
    }else { 
        var Ancode;//统一社会信用代码的每一个值
        var Ancodevalue;//统一社会信用代码每一个值的权重 
        var total = 0; 
        var weightedfactors = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];//加权因子 
        var str = '0123456789ABCDEFGHJKLMNPQRTUWXY';
        //不用I、O、S、V、Z 
        for (var i = 0; i < Code.length - 1; i++) {
            Ancode = Code.substring(i, i + 1);
            Ancodevalue = str.indexOf(Ancode);
            total = total + Ancodevalue * weightedfactors[i];
            //权重与加权因子相乘之和 
        }
        var logiccheckcode = 31 - total % 31;
        if (logiccheckcode == 31) {
            logiccheckcode = 0;
        }
        var Str = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,T,U,W,X,Y";
        var Array_Str = Str.split(',');
        logiccheckcode = Array_Str[logiccheckcode];
        var checkcode = Code.substring(17, 18);
        if (logiccheckcode != checkcode) {
            ret = false;
        }
    } 
    return ret;
}

/**
 * 验证国税税务登记号是否合法:税务登记证是6位区域代码+组织机构代码
 * @param  {string}  taxCode 税务登记证
 * @return {Boolean}
 */
function isValidTaxCode(taxCode){
    if (taxCode == '' || taxCode == 'undefined') return false;
    var ret = true, patt = /^(\d{15})$/;
    if (!patt.test(taxCode)) {
        ret = false;
    }
    return ret;
}