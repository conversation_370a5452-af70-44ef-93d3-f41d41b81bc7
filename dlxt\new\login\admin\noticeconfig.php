<?php
include('./auth.php');
//游戏内公告
$servernotice=$DB->getRow("select * from `config` where `keys`='servernotice' limit 1");
//代理后台首页右下角公告
$dailinotice=$DB->getRow("select * from `config` where `keys`='dailinotice' limit 1");
//滚动公告
$gundongnotice=$DB->getRow("select * from `config` where `keys`='gundongnotice' limit 1");

?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>公告及秘钥设置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>公告设置</legend>
			<div class="form-group">
              <label class="col-xs-12">游戏内公告设置（支持html标签）</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="servernotice" rows="6" placeholder="请输入游戏内登陆账号后的公告内容"><?php echo $servernotice['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">代理后台首页右下角公告（支持html标签）</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="dailinotice" rows="6" placeholder="请输入代理后台首页右下角公告"><?php echo $dailinotice['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">网页后台滚动公告</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="gundongnotice" rows="6" placeholder="请输入网页后台滚动公告"><?php echo $gundongnotice['values']; ?></textarea>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=noticeset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>