<?php
include('./auth.php');
//功能开关设置
$openings=$DB->getRow("select * from `config` where `keys`='opening' limit 1");
$opening = explode(';', $openings['values']);


?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>玩家页面功能开关设置</h4>
		</div>
        <div class="tab-content">
          <div class="tab-pane active">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="edit-form">
              <div class="form-group">
                <label for="wipe_cache_type">头部</label>
                <small class="help-block">请谨慎设置！！！</small>
                <div class="controls-box">
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_0" <?php echo ($opening[0]=='on'?'checked':''); ?>><span>今日累计</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_1" <?php echo ($opening[1]=='on'?'checked':''); ?>><span>角色累计</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_3" <?php echo ($opening[3]=='on'?'checked':''); ?>><span>切换角色</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_2" <?php echo ($opening[2]=='on'?'checked':''); ?>><span>网页背包</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_4" <?php echo ($opening[4]=='on'?'checked':''); ?>><span>充值余额</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_20" <?php echo ($opening[20]=='on'?'checked':''); ?>><span>滚动公告</span>
                  </label>
				  <br>
				  <br>
                </div>
                <label for="wipe_cache_type">首页快捷菜单</label>
                <small class="help-block">请谨慎设置！！！</small>
                <div class="controls-box">
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_5" <?php echo ($opening[5]=='on'?'checked':''); ?>><span>新手福利</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_6" <?php echo ($opening[6]=='on'?'checked':''); ?>><span>关联手机</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_7" <?php echo ($opening[7]=='on'?'checked':''); ?>><span>CDK兑换</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_8" <?php echo ($opening[8]=='on'?'checked':''); ?>><span>余额商城</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_9" <?php echo ($opening[9]=='on'?'checked':''); ?>><span>会员中心</span>
                  </label>
				  <br>
				  <br>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_10" <?php echo ($opening[10]=='on'?'checked':''); ?>><span>装备定制</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_11" <?php echo ($opening[11]=='on'?'checked':''); ?>><span>宠物资质</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_12" <?php echo ($opening[12]=='on'?'checked':''); ?>><span>宠物技能</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_13" <?php echo ($opening[13]=='on'?'checked':''); ?>><span>充值订单</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_14" <?php echo ($opening[14]=='on'?'checked':''); ?>><span>操作日志</span>
                  </label>
				  <br>
				  <br>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_15" <?php echo ($opening[15]=='on'?'checked':''); ?>><span>退出登录</span>
                  </label>
				  <br>
				  <br>
                </div>
                <label for="wipe_cache_type">底部</label>
                <small class="help-block">请谨慎设置！！！</small>
                <div class="controls-box">
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_16" <?php echo ($opening[16]=='on'?'checked':''); ?>><span>首页</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_17" <?php echo ($opening[17]=='on'?'checked':''); ?>><span>现金商城</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_18" <?php echo ($opening[18]=='on'?'checked':''); ?>><span>抽奖</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="gongneng_19" <?php echo ($opening[19]=='on'?'checked':''); ?>><span>背包</span>
                  </label>
                </div>
				  <br>
				  <br>
              </div>
              </div>
              <div class="form-group">
                <button type="submit" class="btn btn-primary m-r-5">确 定</button>
                <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
              </div>
          </form>
				  <br>
				  <br>
		  </div> 
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=openingset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>