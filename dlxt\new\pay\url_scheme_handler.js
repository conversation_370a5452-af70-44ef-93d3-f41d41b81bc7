/**
 * URL Scheme 处理器
 * 专门处理 weixin:// 和 alipays:// 等移动端支付链接
 * 解决 ERR_UNKNOWN_URL_SCHEME 错误
 */

class URLSchemeHandler {
    constructor() {
        this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        this.isAndroid = /Android/.test(navigator.userAgent);
        this.isMobile = this.isIOS || this.isAndroid;
        this.isWeChat = /MicroMessenger/i.test(navigator.userAgent);
        this.isQQ = /QQ\//i.test(navigator.userAgent);
    }

    /**
     * 处理支付链接跳转
     * @param {string} url 支付链接
     * @param {string} payType 支付类型 (alipay/wxpay)
     * @param {object} orderInfo 订单信息
     */
    handlePayment(url, payType, orderInfo) {
        console.log('URLSchemeHandler: 处理支付链接', { url, payType, orderInfo });

        // 检测URL类型
        if (url.startsWith('weixin://')) {
            return this.handleWeChatPay(url, orderInfo);
        } else if (url.startsWith('alipays://')) {
            return this.handleAlipay(url, orderInfo);
        } else if (url.startsWith('http://') || url.startsWith('https://')) {
            return this.handleWebPay(url, orderInfo);
        } else {
            return this.handleUnknownScheme(url, payType, orderInfo);
        }
    }

    /**
     * 处理微信支付
     */
    handleWeChatPay(url, orderInfo) {
        console.log('URLSchemeHandler: 处理微信支付', url);

        if (this.isWeChat) {
            // 在微信内，直接跳转
            this.directJump(url);
        } else if (this.isMobile) {
            // 移动端，尝试打开微信
            this.openWithFallback(url, '微信', orderInfo);
        } else {
            // PC端，显示二维码
            this.showQRCode(url, '微信支付', orderInfo);
        }
    }

    /**
     * 处理支付宝支付
     */
    handleAlipay(url, orderInfo) {
        console.log('URLSchemeHandler: 处理支付宝支付', url);

        if (this.isMobile) {
            // 移动端，尝试打开支付宝
            this.openWithFallback(url, '支付宝', orderInfo);
        } else {
            // PC端，显示二维码
            this.showQRCode(url, '支付宝支付', orderInfo);
        }
    }

    /**
     * 处理普通网页支付
     */
    handleWebPay(url, orderInfo) {
        console.log('URLSchemeHandler: 处理网页支付', url);
        
        // 直接跳转
        setTimeout(() => {
            window.location.href = url;
        }, 1000);
    }

    /**
     * 处理未知协议
     */
    handleUnknownScheme(url, payType, orderInfo) {
        console.log('URLSchemeHandler: 处理未知协议', url);
        
        this.showManualOptions(url, payType, orderInfo);
    }

    /**
     * 直接跳转
     */
    directJump(url) {
        try {
            window.location.href = url;
        } catch (e) {
            console.error('URLSchemeHandler: 直接跳转失败', e);
            this.showError('跳转失败，请手动打开支付应用');
        }
    }

    /**
     * 带回退的打开方式
     */
    openWithFallback(url, appName, orderInfo) {
        const startTime = Date.now();
        
        // 尝试打开APP
        window.location.href = url;
        
        // 设置超时检测
        setTimeout(() => {
            const endTime = Date.now();
            const elapsed = endTime - startTime;
            
            // 如果时间很短，说明可能没有成功打开APP
            if (elapsed < 2000) {
                this.showFallbackOptions(url, appName, orderInfo);
            }
        }, 2500);

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('URLSchemeHandler: 页面隐藏，可能成功打开了APP');
            }
        });
    }

    /**
     * 显示回退选项
     */
    showFallbackOptions(url, appName, orderInfo) {
        const modal = this.createModal(`
            <div style="text-align: center; padding: 20px;">
                <h3>📱 无法自动打开${appName}</h3>
                <p>请选择以下方式完成支付：</p>
                
                <div style="margin: 20px 0;">
                    <button onclick="urlSchemeHandler.retryOpen('${url}')" 
                            style="padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 5px; margin: 5px;">
                        重试打开${appName}
                    </button>
                </div>
                
                <div style="margin: 20px 0;">
                    <button onclick="urlSchemeHandler.copyToClipboard('${url}')" 
                            style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 5px; margin: 5px;">
                        复制链接
                    </button>
                </div>
                
                <div style="margin: 20px 0;">
                    <button onclick="urlSchemeHandler.showQRCode('${url}', '${appName}支付', ${JSON.stringify(orderInfo)})" 
                            style="padding: 12px 24px; background: #6f42c1; color: white; border: none; border-radius: 5px; margin: 5px;">
                        显示二维码
                    </button>
                </div>
                
                <p style="font-size: 12px; color: #666; margin-top: 20px;">
                    如果仍无法打开，请确保已安装${appName}APP
                </p>
                
                <button onclick="urlSchemeHandler.closeModal()" 
                        style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 3px; margin-top: 10px;">
                    关闭
                </button>
            </div>
        `);
        
        document.body.appendChild(modal);
    }

    /**
     * 显示二维码
     */
    showQRCode(url, title, orderInfo) {
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
        
        const modal = this.createModal(`
            <div style="text-align: center; padding: 20px;">
                <h3>📱 ${title}</h3>
                <p>请使用手机扫描下方二维码完成支付</p>
                
                <div style="margin: 20px 0;">
                    <img src="${qrUrl}" alt="支付二维码" style="width: 200px; height: 200px; border: 1px solid #ddd;">
                </div>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
                    <div><strong>订单号：</strong>${orderInfo.out_trade_no}</div>
                    <div><strong>支付金额：</strong>￥${orderInfo.money}</div>
                    <div><strong>商品名称：</strong>${orderInfo.name}</div>
                </div>
                
                <button onclick="urlSchemeHandler.closeModal()" 
                        style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">
                    关闭
                </button>
            </div>
        `);
        
        document.body.appendChild(modal);
    }

    /**
     * 显示手动选项
     */
    showManualOptions(url, payType, orderInfo) {
        const appName = payType === 'alipay' ? '支付宝' : '微信';
        
        const modal = this.createModal(`
            <div style="text-align: center; padding: 20px;">
                <h3>⚠️ 需要手动操作</h3>
                <p>检测到特殊支付链接，请手动完成以下操作：</p>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left;">
                    <strong>操作步骤：</strong><br>
                    1. 复制下方链接<br>
                    2. 打开${appName}APP<br>
                    3. 在APP中粘贴链接并打开
                </div>
                
                <div style="background: #e9ecef; padding: 10px; border-radius: 5px; word-break: break-all; font-size: 12px; margin: 15px 0;">
                    ${url}
                </div>
                
                <button onclick="urlSchemeHandler.copyToClipboard('${url}')" 
                        style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 5px; margin: 5px;">
                    复制链接
                </button>
                
                <button onclick="urlSchemeHandler.closeModal()" 
                        style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; margin: 5px;">
                    关闭
                </button>
            </div>
        `);
        
        document.body.appendChild(modal);
    }

    /**
     * 重试打开
     */
    retryOpen(url) {
        this.closeModal();
        window.location.href = url;
    }

    /**
     * 复制到剪贴板
     */
    copyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        
        try {
            document.execCommand('copy');
            alert('链接已复制到剪贴板');
        } catch (err) {
            alert('复制失败，请手动复制链接');
        }
        
        document.body.removeChild(textArea);
    }

    /**
     * 创建模态框
     */
    createModal(content) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
        `;
        modalContent.innerHTML = content;
        
        modal.appendChild(modalContent);
        modal.id = 'urlSchemeModal';
        
        return modal;
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('urlSchemeModal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        alert(message);
    }
}

// 创建全局实例
window.urlSchemeHandler = new URLSchemeHandler();

// 监听全局错误
window.addEventListener('error', function(e) {
    if (e.message && e.message.indexOf('ERR_UNKNOWN_URL_SCHEME') !== -1) {
        console.log('URLSchemeHandler: 检测到URL scheme错误');
    }
});

console.log('URLSchemeHandler: 已加载完成');
