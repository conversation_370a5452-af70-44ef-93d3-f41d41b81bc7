<?php
include('auth.php');
?>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<!-- 加载 下拉图片 -->
<link rel="stylesheet" type="text/css" href="/static/admin/image-picker/image-picker.css">
<script type="text/javascript" src="/static/admin/image-picker/image-picker.js"></script>
<style>
 .container {
  height: 60px;
  width: 90px;
  margin: 0 auto;
} 
 </style>
</head>
<body>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'" >'.$res['value'].'</option>';
				
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
</body>