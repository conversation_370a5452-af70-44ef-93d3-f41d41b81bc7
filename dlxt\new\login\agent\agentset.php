<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='addAgent'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addAgent(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="" placeholder="请输入6-18位账号" />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="请输入6-18位密码" />
            </div>
            
            <div class="form-group col-md-12">
              <label>分成信息</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="fencheng" placeholder="请输入分成信息" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">%</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editmy'){
	$adminUser = $_SESSION['adminUser'];
	$adminData = $Admin->getAdmin($username);
	if(!$adminData){echo "<script>layer.ready(function(){layer.msg('该账户信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editmy(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="<?php echo $adminData['username']; ?>" placeholder="请输入6-18位账号" readonly />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="不修改请留空，请输入6-18位密码" />
            </div>
            
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='paymoney'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return paymoney(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>可用额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="moneys" value="<?php echo $adminData['money']; ?>" placeholder="请输入充值额度" readonly>
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12">
				<label>充值代理</label>
				<select name="agentid" class="form-control select2" id="agentid" >
				<?php
				$rs=$DB->query("SELECT * FROM `admin` where `lastuid`like'%".$adminData['id']."%' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#agentid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>充值额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" value="" placeholder="请输入充值额度" >
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='payusermoney'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return payusermoney(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>可用额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="moneys" value="<?php echo $adminData['money']; ?>" placeholder="请输入充值额度" readonly>
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12">
				<label>充值玩家</label>
				<select name="roleid" class="form-control select2" id="roleid" >
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid` in (SELECT `id` FROM `account` where `agentid`='".$adminData['id']."')");
				while($res = $rs->fetch())
				{
						$serData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "' limit 1");
						$accountsData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $res['userid'] . "' limit 1");
						echo '<option value="'.$res['id'].'">角色名称:'.$res['name'].'，角色ID:'.$res['roleid'].'，所属大区:'.$serData['name'].'，所属账号:'.$accountsData['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#roleid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>充值金额</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" value="" placeholder="请输入充值金额" >
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='payrmbshop'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return payrmbshop(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>可用额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="moneys" value="<?php echo $adminData['money']; ?>" placeholder="请输入充值额度" readonly>
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12">
				<label>充值玩家</label>
				<select name="roleid" class="form-control select2" id="roleid" >
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid` in (SELECT `id` FROM `account` where `agentid`='".$adminData['id']."')");
				while($res = $rs->fetch())
				{
						$serData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "' limit 1");
						$accountsData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $res['userid'] . "' limit 1");
						echo '<option value="'.$res['id'].'">角色名称:'.$res['name'].'，角色ID:'.$res['roleid'].'，所属大区:'.$serData['name'].'，所属账号:'.$accountsData['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#roleid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>充值物品</label>
				<select name="rmbshopid" class="form-control select2" id="rmbshopid" >
				<?php
				$rs=$DB->query("SELECT * FROM `rmbshops` where `status`='1' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'*'.$res['num'].'【扣除：'.$res['price'].'额度】</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#rmbshopid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>发送数量</label>
              <input type="number" class="form-control" name="number" value="1" placeholder="请输入发送数量" />
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='zqsqdl'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form action="agentset.php?act=zqsq" method="post" class="row">
			<div class="form-group col-md-12">
				<label>玩家账号</label>
				<select name="username" class="form-control select2" id="username" >
				<?php
				$rs=$DB->query("SELECT * FROM `account` where `agentid`='".$adminData['id']."' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['username'].'">'.$res['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#username').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='zqsq'){
	$username = $post['username'];
	$userData = $Admin->getUser($username);
	if(!$userData){echo "<script>layer.ready(function(){layer.msg('该账户信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	if($userData['agentid'] != $adminData['id'] ){echo "<script>layer.ready(function(){layer.msg('该玩家不属于你', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return zqsq(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>当前可用额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="moneys" value="<?php echo $adminData['money']; ?>" placeholder="请输入充值额度" readonly>
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12">
				<label>被转区角色</label>
				<select name="oldroleid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid`='".$userData['id']."' order by id");
				while($res = $rs->fetch())
				{
				$serverData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "'  limit 1");
				echo '<option value="'.$res['id'].'">【'.$serverData['name'].'】'.$res['name'].' - ['.$res['roleid'].']</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>预转区角色</label>
				<select name="newroleid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid`='".$userData['id']."' order by id");
				while($res = $rs->fetch())
				{
				$serverData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "'  limit 1");
				echo '<option value="'.$res['id'].'">【'.$serverData['name'].'】'.$res['name'].' - ['.$res['roleid'].']</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>转区方案(手续费用代理额度扣除)</label>
				<select name="fangan" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `zqfa` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">提交申请</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editAgent'){
	$id = intval($get['id']);
	$checkadmin = $Admin->getAdminId($id);
	if($id==1){
	echo "<script>layer.ready(function(){layer.msg('总后台账户请在右上角头像处修改', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();
		
	}
	if(!$checkadmin){echo "<script>layer.ready(function(){layer.msg('该代理信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editAgent(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="<?php echo $checkadmin['username']; ?>" placeholder="请输入6-18位账号" readonly />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="不修改请留空" />
            </div>
            <div class="form-group col-md-12">
              <label>邀请码</label>
              <input type="text" class="form-control" name="invite" value="<?php echo $checkadmin['invite']; ?>" placeholder="请输入8位以内邀请码" />
            </div>
            <div class="form-group col-md-12">
              <label>分成信息</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="fencheng" value="<?php echo $checkadmin['fencheng']; ?>" placeholder="请输入分成信息" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">%</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='payuservip'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return payuservip(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>可用额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="moneys" value="<?php echo $adminData['money']; ?>" placeholder="请输入充值额度" readonly>
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12">
				<label>充值玩家</label>
				<select name="roleid" class="form-control select2" id="roleid" >
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid` in (SELECT `id` FROM `account` where `agentid`='".$adminData['id']."')");
				while($res = $rs->fetch())
				{
						$serData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "' limit 1");
						$accountsData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $res['userid'] . "' limit 1");
						echo '<option value="'.$res['id'].'">角色名称:'.$res['name'].'，角色ID:'.$res['roleid'].'，所属大区:'.$serData['name'].'，所属账号:'.$accountsData['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#roleid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>选择类型</label>
				<select name="vipid" class="form-control select2" id="vipid" >
				<?php
				//月卡
				$yueka=$DB->getRow("select * from `config` where `keys`='yueka' limit 1");
				//周卡
				$zhouka=$DB->getRow("select * from `config` where `keys`='zhouka' limit 1");
				echo '<option value="1">周卡【'.$zhouka['values'].'元】</option>';
				echo '<option value="2">月卡【'.$yueka['values'].'元】</option>';
				?> 
				</select>
				<script>var selectorx = $('#vipid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
  
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function payuservip(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=payuservip',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function zqsq(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=zqsq',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function payrmbshop(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=payrmbshop',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function payusermoney(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=payusermoney',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function paymoney(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=paymoney',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editmy(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editmy',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addAgent(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addAgent',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editAgent(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editAgent',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>