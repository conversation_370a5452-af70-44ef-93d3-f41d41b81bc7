<?php
include './common/main.php';

// 检查用户是否登录
if(!isset($_COOKIE['user_token'])){
    header('Location: login.php');
    exit;
}

// 验证用户token
$token = $_COOKIE['user_token'];
$userinfo = authcode($token, 'DECODE', SYS_KEY);
if(!$userinfo){
    header('Location: login.php');
    exit;
}

list($username, $session) = explode("\t", $userinfo);
$userData = $Admin->getUser($username);
if(!$userData){
    header('Location: login.php');
    exit;
}

// 获取用户绑定的角色
$bindData = $DB->query("SELECT * FROM `binds` WHERE `userid` = '".$userData['id']."'")->fetchAll();

// 获取代理信息
$agentData = $Admin->getAdminId($userData['agentid']);

//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
?>
<html lang="en">
<head>
	<title>用户中心 - <?php echo $title['values'];?></title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
<!--===============================================================================================-->
	<link rel="icon" type="image/png" href="/static/login/images/icons/favicon.ico"/>
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/bootstrap/css/bootstrap.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/animate/animate.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/css-hamburgers/hamburgers.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/select2/select2.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/css/util.css">
	<link rel="stylesheet" type="text/css" href="/static/login/css/main.css">
<!--===============================================================================================-->
</head>
<body>

	<div class="limiter">
		<div class="container-login100">
			<div class="wrap-login100">
				<div class="login100-pic js-tilt" data-tilt>
					<img src="/static/login/images/img-01.png" alt="IMG">
				</div>
				<div class="login100-form">
					<span class="login100-form-title">
						用户中心
					</span>

					<div class="wrap-input100">
						<span class="label-input100">用户名：</span>
						<span class="input100"><?php echo $userData['username']; ?></span>
					</div>

					<div class="wrap-input100">
						<span class="label-input100">所属代理：</span>
						<span class="input100"><?php echo $agentData ? $agentData['username'] : '未知'; ?></span>
					</div>

					<div class="wrap-input100">
						<span class="label-input100">账号状态：</span>
						<span class="input100"><?php echo $userData['status'] == 1 ? '正常' : '封禁'; ?></span>
					</div>

					<div class="wrap-input100">
						<span class="label-input100">绑定角色：</span>
						<div class="input100">
							<?php if(empty($bindData)): ?>
								<span style="color: red;">暂无绑定角色，请先进游戏绑定！</span>
							<?php else: ?>
								<?php foreach($bindData as $bind): ?>
									<div><?php echo $bind['name']; ?> (角色ID: <?php echo $bind['roleid']; ?>)</div>
								<?php endforeach; ?>
							<?php endif; ?>
						</div>
					</div>

					<div class="container-login100-form-btn">
						<a href="ajax.php?act=logout" class="login100-form-btn">
							退出登录
						</a>
					</div>

					<div class="text-center p-t-12">
						<a class="txt2" href="index.php">
							返回首页
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>

<!--===============================================================================================-->
	<script src="/static/login/vendor/jquery/jquery-3.2.1.min.js"></script>
<!--===============================================================================================-->
	<script src="/static/login/vendor/bootstrap/js/popper.js"></script>
	<script src="/static/login/vendor/bootstrap/js/bootstrap.min.js"></script>
<!--===============================================================================================-->
	<script src="/static/login/vendor/select2/select2.min.js"></script>
<!--===============================================================================================-->
	<script src="/static/login/vendor/tilt/tilt.jquery.min.js"></script>
	<script >
		$('.js-tilt').tilt({
			scale: 1.1
		})
	</script>
<!--===============================================================================================-->
	<script src="/static/login/js/main.js"></script>

</body>
</html>