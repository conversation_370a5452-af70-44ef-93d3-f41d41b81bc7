<?php
include "config.php";

//网站信息
$title=$DBDL->getRow("select * from `config` where `keys`='title' limit 1");
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值中心 - <?php echo $title['values'];?></title>
    <link rel="stylesheet" type="text/css" href="/static/login/vendor/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/login/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .account-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .form-group label {
            font-weight: bold;
            color: #333;
        }
        .form-control {
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            padding: 10px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .products {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .product-card {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
        }
        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        .product-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .product-card input[type="radio"] {
            display: none;
        }
        .product-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-top: 10px;
            display: block;
        }
        .product-price {
            font-size: 24px;
            color: #667eea;
            font-weight: bold;
            margin: 10px 0;
        }
        .payment-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
        }
        .btn-payment {
            padding: 15px 40px;
            font-size: 18px;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        .btn-alipay {
            background: #1677ff;
            color: white;
        }
        .btn-alipay:hover {
            background: #0958d9;
        }
        .btn-wxpay {
            background: #07c160;
            color: white;
        }
        .btn-wxpay:hover {
            background: #059748;
        }
        .notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            font-size: 16px;
        }
        .back-link:hover {
            color: #ddd;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <a href="../index.php" class="back-link">
        <i class="fa fa-arrow-left"></i> 返回首页
    </a>
    
    <div class="container">
        <div class="header">
            <h1><i class="fa fa-credit-card"></i> 充值中心</h1>
            <p>安全便捷的充值服务</p>
        </div>

        <form method="post" action="api.php" id="payForm">
            <div class="form-card">
                <h3><i class="fa fa-user"></i> 账号信息</h3>
                <div class="account-info">
                    <div class="form-group">
                        <label for="account">游戏账号</label>
                        <input type="text" class="form-control" name="account" id="account" 
                               value="<?php echo isset($get['account']) ? $get['account'] : ''; ?>" 
                               placeholder="请输入游戏账号" required>
                    </div>
                    <div class="form-group">
                        <label for="role">角色名称</label>
                        <input type="text" class="form-control" name="role" id="role" 
                               value="<?php echo isset($get['role']) ? $get['role'] : ''; ?>" 
                               placeholder="请输入角色名称" required>
                    </div>
                    <div class="form-group">
                        <label for="server">服务器</label>
                        <input type="text" class="form-control" name="server" id="server" 
                               value="<?php echo isset($get['server']) ? $get['server'] : ''; ?>" 
                               placeholder="请输入服务器名称" required>
                    </div>
                    <div class="form-group">
                        <label for="roleid">角色ID</label>
                        <input type="number" class="form-control" name="roleid" id="roleid" 
                               value="<?php echo isset($get['roleid']) ? $get['roleid'] : ''; ?>" 
                               placeholder="请输入角色ID" required>
                    </div>
                </div>
            </div>

            <div class="form-card">
                <h3><i class="fa fa-shopping-cart"></i> 选择充值面额</h3>
                <div class="notice">
                    <i class="fa fa-info-circle"></i> <?php echo $czgg; ?>
                </div>
                
                <div class="products">
                    <?php foreach($goods_info as $key => $val): ?>
                    <div class="product-card" onclick="selectProduct(<?php echo $key; ?>)">
                        <input type="radio" name="goods" value="<?php echo $key; ?>" id="goods-<?php echo $key; ?>">
                        <i class="fa <?php echo $val[4]; ?>" style="font-size: 40px; color: #667eea;"></i>
                        <div class="product-title"><?php echo $val[0]; ?></div>
                        <div class="product-price">￥<?php echo $val[2]; ?></div>
                        <div style="color: #666;">获得 <?php echo $val[3]; ?> 仙玉</div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="form-card">
                <h3><i class="fa fa-credit-card"></i> 选择支付方式</h3>
                <div class="payment-buttons">
                    <button type="submit" name="type" value="alipay" class="btn-payment btn-alipay">
                        <i class="fa fa-alipay"></i> 支付宝支付
                    </button>
                    <button type="submit" name="type" value="wxpay" class="btn-payment btn-wxpay">
                        <i class="fa fa-wechat"></i> 微信支付
                    </button>
                </div>
            </div>
        </form>
    </div>

    <script>
        function selectProduct(id) {
            // 移除所有选中状态
            document.querySelectorAll('.product-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 选中当前产品
            document.getElementById('goods-' + id).checked = true;
            document.getElementById('goods-' + id).closest('.product-card').classList.add('selected');
        }

        // 表单验证
        document.getElementById('payForm').addEventListener('submit', function(e) {
            const goods = document.querySelector('input[name="goods"]:checked');
            if (!goods) {
                e.preventDefault();
                alert('请选择充值面额');
                return false;
            }
            
            const account = document.getElementById('account').value.trim();
            const role = document.getElementById('role').value.trim();
            const server = document.getElementById('server').value.trim();
            const roleid = document.getElementById('roleid').value.trim();
            
            if (!account || !role || !server || !roleid) {
                e.preventDefault();
                alert('请填写完整的账号信息');
                return false;
            }
        });
    </script>
</body>
</html>
