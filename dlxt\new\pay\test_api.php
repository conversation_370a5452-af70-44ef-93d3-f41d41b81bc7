<?php
require_once("config.php");

// 测试API接口调用
function testAPICall($type = 'alipay', $device = 'mobile') {
    global $epay_config;
    
    // 模拟订单数据
    $out_trade_no = 'TEST_' . date('YmdHis') . rand(1000, 9999);
    $money = '0.01';
    $name = 'API测试商品';
    $notify_url = 'http://test.com/notify.php';
    $return_url = 'http://test.com/return.php';
    $clientip = '127.0.0.1';
    
    // 构造参数
    $parameter = array(
        "pid" => $epay_config['pid'],
        "type" => $type,
        "notify_url" => $notify_url,
        "return_url" => $return_url,
        "out_trade_no" => $out_trade_no,
        "name" => $name,
        "money" => $money,
        "clientip" => $clientip,
        "device" => $device,
    );
    
    // 计算签名
    ksort($parameter);
    $signstr = '';
    foreach($parameter as $k => $v){
        if($k != "sign" && $k != "sign_type" && $v!=''){
            $signstr .= $k.'='.$v.'&';
        }
    }
    $signstr = substr($signstr,0,-1);
    $signstr .= $epay_config['key'];
    $sign = md5($signstr);
    
    $parameter['sign'] = $sign;
    $parameter['sign_type'] = 'MD5';
    
    // 调用API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $epay_config['apiurl'].'mapi.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parameter));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return array(
        'request' => $parameter,
        'response' => $response,
        'httpCode' => $httpCode,
        'error' => $error,
        'signstr' => $signstr
    );
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .container { max-width: 1000px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .title { font-size: 24px; color: #333; margin-bottom: 20px; text-align: center; }
        .test-section { margin-bottom: 30px; }
        .test-section h3 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .result-box { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #007bff; }
        .result-box.success { border-left-color: #28a745; }
        .result-box.error { border-left-color: #dc3545; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 14px; }
        .json-key { color: #68d391; }
        .json-string { color: #fbb6ce; }
        .json-number { color: #90cdf4; }
        .json-boolean { color: #f6ad55; }
        .config-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">API接口测试工具</h1>
        
        <div class="config-info">
            <h4>当前配置信息</h4>
            <p><strong>API地址：</strong><?php echo htmlspecialchars($epay_config['apiurl']); ?></p>
            <p><strong>商户ID：</strong><?php echo htmlspecialchars($epay_config['pid']); ?></p>
            <p><strong>商户密钥：</strong><?php echo htmlspecialchars(substr($epay_config['key'], 0, 8) . '...'); ?></p>
        </div>
        
        <div class="test-section">
            <h3>快速测试</h3>
            <p>点击下方按钮测试不同的支付接口调用：</p>
            
            <form method="GET" style="display: inline;">
                <input type="hidden" name="test" value="1">
                <button type="submit" name="type" value="alipay" class="btn btn-success">测试支付宝API</button>
                <button type="submit" name="type" value="wxpay" class="btn btn-warning">测试微信API</button>
            </form>
        </div>
        
        <?php if (isset($_GET['test']) && isset($_GET['type'])): ?>
        <div class="test-section">
            <h3>测试结果</h3>
            
            <?php
            $testType = $_GET['type'];
            $result = testAPICall($testType, 'mobile');
            
            echo '<div class="result-box">';
            echo '<h4>请求参数</h4>';
            echo '<div class="code-block">';
            echo '<pre>' . json_encode($result['request'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            echo '</div>';
            echo '</div>';
            
            echo '<div class="result-box">';
            echo '<h4>签名字符串</h4>';
            echo '<div class="code-block">';
            echo '<pre>' . htmlspecialchars($result['signstr']) . '</pre>';
            echo '</div>';
            echo '</div>';
            
            if ($result['error']) {
                echo '<div class="result-box error">';
                echo '<h4>请求错误</h4>';
                echo '<p style="color: #dc3545;">CURL错误: ' . htmlspecialchars($result['error']) . '</p>';
                echo '</div>';
            } else {
                $responseData = json_decode($result['response'], true);
                
                if ($responseData) {
                    $isSuccess = isset($responseData['code']) && $responseData['code'] == 1;
                    echo '<div class="result-box ' . ($isSuccess ? 'success' : 'error') . '">';
                    echo '<h4>API响应 (HTTP ' . $result['httpCode'] . ')</h4>';
                    echo '<div class="code-block">';
                    echo '<pre>' . json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
                    echo '</div>';
                    
                    if ($isSuccess) {
                        echo '<h5>解析结果：</h5>';
                        echo '<ul>';
                        if (isset($responseData['trade_no'])) {
                            echo '<li><strong>订单号：</strong>' . htmlspecialchars($responseData['trade_no']) . '</li>';
                        }
                        if (isset($responseData['payurl'])) {
                            echo '<li><strong>支付跳转URL：</strong><a href="' . htmlspecialchars($responseData['payurl']) . '" target="_blank">点击跳转</a></li>';
                        }
                        if (isset($responseData['qrcode'])) {
                            echo '<li><strong>二维码内容：</strong>' . htmlspecialchars($responseData['qrcode']) . '</li>';
                        }
                        if (isset($responseData['urlscheme'])) {
                            echo '<li><strong>小程序跳转：</strong>' . htmlspecialchars($responseData['urlscheme']) . '</li>';
                        }
                        echo '</ul>';
                    } else {
                        echo '<p style="color: #dc3545;"><strong>错误信息：</strong>' . htmlspecialchars($responseData['msg'] ?? '未知错误') . '</p>';
                    }
                    echo '</div>';
                } else {
                    echo '<div class="result-box error">';
                    echo '<h4>响应解析失败</h4>';
                    echo '<p>原始响应:</p>';
                    echo '<div class="code-block">';
                    echo '<pre>' . htmlspecialchars($result['response']) . '</pre>';
                    echo '</div>';
                    echo '</div>';
                }
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="test-section">
            <h3>接口文档参考</h3>
            <p>本测试基于官方接口文档：<a href="https://sheng.nachengweb.com/doc.html" target="_blank">https://sheng.nachengweb.com/doc.html</a></p>
            
            <h4>API接口支付参数说明：</h4>
            <ul>
                <li><strong>pid</strong> - 商户ID</li>
                <li><strong>type</strong> - 支付方式 (alipay/wxpay)</li>
                <li><strong>out_trade_no</strong> - 商户订单号</li>
                <li><strong>notify_url</strong> - 异步通知地址</li>
                <li><strong>return_url</strong> - 跳转通知地址</li>
                <li><strong>name</strong> - 商品名称</li>
                <li><strong>money</strong> - 商品金额</li>
                <li><strong>clientip</strong> - 用户IP地址</li>
                <li><strong>device</strong> - 设备类型 (pc/mobile/wechat/qq/alipay)</li>
                <li><strong>sign</strong> - 签名字符串</li>
                <li><strong>sign_type</strong> - 签名类型 (MD5)</li>
            </ul>
        </div>
    </div>
</body>
</html>
