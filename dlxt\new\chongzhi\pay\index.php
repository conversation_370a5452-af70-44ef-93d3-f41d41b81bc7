    <!-- 
    本pay只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
--
    Array ( [server] => 梦回大唐 [role] => 谭盼 [roleid] => 10003 [account] => cxy1234 )
    
    
    展示商品 -->

<?php
include "config.php";
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
<link type="text/css" rel="stylesheet" href="./img/css/style.css?<?php echo time()?>" />
<link type="text/css" rel="stylesheet" href="css/style-enhanced.css"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>梦回西游-赞助中心</title>
<style>

</style>

</head>
<body>
    <div class="background-image-box"> </div>

<form method="post" action="api.php" role="form">
<div class="fixed-nav">
    <div class="account-info">
        <p>账号 :</p> <input class="btnlist-1" name="account" value="<?php echo $get['account']; ?>" type="text" placeholder="输入账号" >
    </div>
</div>



<div class="text-center">

    <div class="role-card">
  
        <span>角色名称 :  <input class="btnlist" name="role" value="<?php echo $get['role']; ?>" type="text" placeholder="输入角色名" ></span>
    <span>区服名称 : <input class="btnlist" name="server" value="<?php echo $get['server']; ?>" type="text" placeholder="输入区服：" ></span>
    
    <span>角色id : <input class="btnlist" name="roleid" value="<?php echo $get['roleid']; ?>" type="number" placeholder="输入角色ID" ></span>
</div>
<div class="scrolling-ads">
    <div class="ad"><?php echo $czgg; ?></div>

</div>

<h1 style="margin: 30px 0 30px 30px;font-size:20px;font-weight: bold;">选择充值面额</h1>

    <!-- 展示商品 -->
  <div class="products">
    <?php foreach($goods_info as $key => $val): ?>
    <div class="product-card">
       <img src="<?php echo $val[4]; ?>" alt="Product Image" class="product-image">
        <div class="product-content">
            <input type="radio" name="goods" value="<?php echo $key; ?>" id="goods-<?php echo $key; ?>">
            <label for="goods-<?php echo $key; ?>">
                <span class="product-title"><?php echo $val[0]; ?> - ￥<?php echo $val[2]; ?></span>
            </label>
            
        </div>
    </div>
    <?php endforeach; ?>
</div>

</div>

<br/><br/>
<div class="bottom">
    <p><button type="submit" value="wxpay" name="type" class="btn wxpay btn-success my-3"></button></p>
    <p><button type="submit" value="alipay" name="type" class="btn alipay btn-success my-3"></button></p>
</div>
</form>
<div class="footer">
    <p><a class="url" href="">支付系统</a></p>
</div>
<script src="js/interaction.js"></script>
</body>
</html>
