<?php
// 调试回调功能
include "config.php";

echo "<h2>支付回调调试工具</h2>";

// 检查最近的订单
echo "<h3>最近的支付订单：</h3>";
$recentOrders = $DBDL->getAll("SELECT * FROM `pay_order` ORDER BY id DESC LIMIT 10");

if($recentOrders) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>订单号</th><th>用户</th><th>金额</th><th>状态</th><th>参数</th><th>时间</th></tr>";
    
    foreach($recentOrders as $order) {
        $statusText = $order['status'] == 1 ? '✅已支付' : '❌未支付';
        $statusColor = $order['status'] == 1 ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$order['id']}</td>";
        echo "<td>{$order['orderid']}</td>";
        echo "<td>{$order['user']}</td>";
        echo "<td>¥{$order['money']}</td>";
        echo "<td style='color: {$statusColor}'>{$statusText}</td>";
        echo "<td>{$order['param']}</td>";
        echo "<td>{$order['date']} {$order['time']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "没有找到订单记录。";
}

// 检查zhifu.txt文件
echo "<h3>zhifu.txt文件状态：</h3>";
if(file_exists('zhifu.txt')) {
    $content = file_get_contents('zhifu.txt');
    $lines = explode(';', trim($content, ';'));
    
    echo "文件存在，共有 " . count($lines) . " 条记录<br>";
    echo "<h4>最近的记录：</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>角色ID</th><th>点数</th><th>支付类型</th><th>状态</th><th>订单号</th></tr>";
    
    // 显示最后5条记录
    $recentLines = array_slice($lines, -5);
    foreach($recentLines as $line) {
        if(trim($line)) {
            $parts = explode(',', $line);
            if(count($parts) >= 5) {
                echo "<tr>";
                echo "<td>{$parts[0]}</td>";
                echo "<td>{$parts[1]}</td>";
                echo "<td>{$parts[2]}</td>";
                echo "<td>" . ($parts[3] == '0' ? '✅成功' : '❌失败') . "</td>";
                echo "<td>{$parts[4]}</td>";
                echo "</tr>";
            }
        }
    }
    echo "</table>";
    
    echo "<h4>完整文件内容：</h4>";
    echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($content) . "</textarea>";
} else {
    echo "❌ zhifu.txt文件不存在";
}

// 检查文件权限
echo "<h3>文件权限检查：</h3>";
$filePath = 'zhifu.txt';
if(file_exists($filePath)) {
    echo "文件存在: ✅<br>";
    echo "可读: " . (is_readable($filePath) ? '✅' : '❌') . "<br>";
    echo "可写: " . (is_writable($filePath) ? '✅' : '❌') . "<br>";
    echo "文件大小: " . filesize($filePath) . " 字节<br>";
    echo "最后修改时间: " . date('Y-m-d H:i:s', filemtime($filePath)) . "<br>";
} else {
    echo "文件不存在，尝试创建...<br>";
    if(file_put_contents($filePath, '') !== false) {
        echo "✅ 文件创建成功<br>";
    } else {
        echo "❌ 文件创建失败<br>";
    }
}

// 测试写入功能
echo "<h3>写入功能测试：</h3>";
$testContent = "TEST," . time() . ",test,0,TEST_ORDER;";
if(file_put_contents($filePath, $testContent, FILE_APPEND) !== false) {
    echo "✅ 写入测试成功<br>";
    echo "测试内容: {$testContent}<br>";
} else {
    echo "❌ 写入测试失败<br>";
}

echo "<br><a href='test_callback.php'>返回回调测试</a> | <a href='test.php'>返回主测试页面</a>";
?>
