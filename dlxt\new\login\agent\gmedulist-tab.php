<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%' and `username`='".$adminData['username']."'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}' and `username`='".$adminData['username']."'";
		}
	}else{
	$sql=" `username`='".$adminData['username']."'";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" `username`='".$adminData['username']."'";
}
$numrows=$DB->getColumn("SELECT count(*) from `gm_order` WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>操作账号</th>
					<th>操作内容</th>
					<th>变换额度</th>
					<th>变换前额度</th>
					<th>变换后额度</th>
					<th>操作IP</th>
					<th>操作地点</th>
					<th>时间</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM `gm_order` WHERE{$sql} order by id desc limit $offset,$pagesize");
while($res = $rs->fetch())
{
echo '<tr>
<td>'.$res['id'].'</td>
<td><b>'.$res['username'].'</b></td>
<td>'.$res['info'].'</td>
<td><b>'.($res['money']>0?'+'.$res['money']:$res['money']).'</b></td>
<td>'.$res['oldmoney'].'</td>
<td>'.$res['newmoney'].'</td>
<td>'.$res['ip'].'</td>
<td>'.$res['city'].'</td>
<td>'.$res['date'].' '.$res['time'].'</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
