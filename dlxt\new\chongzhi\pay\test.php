<?php
/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-05-29 13:29:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-29 20:32:27
 * @FilePath: \dlxt\chongzhi\pay\test.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 测试支付系统
include "config.php";
require_once("lib/EpayCore.class.php");

echo "<h2>支付系统测试</h2>";

// 测试数据库连接
try {
    echo "✅ 数据库连接成功<br>";

    // 测试查询pay_order表
    $orders = $DBDL->query("SELECT * FROM `pay_order` ORDER BY id DESC LIMIT 5")->fetchAll();
    echo "✅ pay_order表查询成功，共有 " . count($orders) . " 条最新记录<br>";

    if(count($orders) > 0) {
        echo "<h3>最新订单：</h3>";
        echo "<table border='1'>";
        echo "<tr><th>订单号</th><th>用户</th><th>金额</th><th>状态</th><th>时间</th></tr>";
        foreach($orders as $order) {
            $status = $order['status'] == 1 ? '已支付' : '未支付';
            echo "<tr>";
            echo "<td>{$order['orderid']}</td>";
            echo "<td>{$order['user']}</td>";
            echo "<td>{$order['money']}</td>";
            echo "<td>{$status}</td>";
            echo "<td>{$order['date']} {$order['time']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // 测试支付配置
    echo "<h3>支付配置：</h3>";
    echo "商户ID: {$epay_config['pid']}<br>";
    echo "API地址: {$epay_config['apiurl']}<br>";
    echo "商户KEY: " . substr($epay_config['key'], 0, 8) . "...<br>";

    // 测试回调URL
    echo "<h3>回调URL：</h3>";
    $notify_url = "http://".$_SERVER['HTTP_HOST']."/pay/notify_url.php";
    $return_url = "http://".$_SERVER['HTTP_HOST']."/pay/return_url.php";
    echo "异步回调: <a href='{$notify_url}' target='_blank'>{$notify_url}</a><br>";
    echo "同步回调: <a href='{$return_url}' target='_blank'>{$return_url}</a><br>";

} catch(Exception $e) {
    echo "❌ 错误：" . $e->getMessage();
}