<?php
// 调试微信支付API返回结果
include "config.php";

echo "<h2>微信支付API调试工具</h2>";

// 模拟微信支付请求
$type = 'wxpay';
$out_trade_no = 'DEBUG_' . time();
$money = '1';
$name = 'DEBUG测试商品';
$notify_url = 'http://************:168/pay/notify_url.php';
$return_url = 'http://************:168/pay/return_url.php';

// 构造请求参数
$params = array(
    'pid' => $epay_config['pid'],
    'type' => $type,
    'out_trade_no' => $out_trade_no,
    'notify_url' => $notify_url,
    'return_url' => $return_url,
    'name' => $name,
    'money' => $money,
    'clientip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
    'sitename' => $epay_config['sitename'] ?? '测试网站'
);

// 生成签名
require_once("lib/EpayCore.class.php");
$epay = new EpayCore($epay_config);
$sign = $epay->getSign($params);
$params['sign'] = $sign;
$params['sign_type'] = 'MD5';

// 发送请求 - 根据文档，API端点应该是 mapi.php
$api_url = $epay_config['apiurl'] . 'mapi.php';
$query_string = http_build_query($params);
$full_url = $api_url . '?' . $query_string;

echo "<h3>请求信息：</h3>";
echo "<p><strong>API URL:</strong> {$api_url}</p>";
echo "<p><strong>支付类型:</strong> {$type}</p>";
echo "<p><strong>订单号:</strong> {$out_trade_no}</p>";
echo "<p><strong>金额:</strong> ¥{$money}</p>";

echo "<h3>请求参数：</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
foreach($params as $key => $value) {
    echo "<tr><td>{$key}</td><td>{$value}</td></tr>";
}
echo "</table>";

echo "<h3>完整请求URL：</h3>";
echo "<textarea style='width: 100%; height: 100px;'>{$full_url}</textarea>";

// 发送HTTP请求获取结果
echo "<h3>API返回结果：</h3>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $full_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
if($error) {
    echo "<p><strong>CURL错误:</strong> {$error}</p>";
}

echo "<h4>原始返回内容：</h4>";
echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($response) . "</textarea>";

// 尝试解析JSON
$result = json_decode($response, true);
if($result) {
    echo "<h4>解析后的JSON：</h4>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    echo "<h4>返回字段分析：</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>字段名</th><th>值</th><th>说明</th></tr>";
    
    foreach($result as $key => $value) {
        $description = '';
        switch($key) {
            case 'code': $description = '状态码 (1=成功, 0=失败)'; break;
            case 'msg': $description = '返回消息'; break;
            case 'payurl': $description = '支付链接'; break;
            case 'qrcode': $description = '二维码内容'; break;
            case 'urlscheme': $description = 'URL Scheme跳转链接'; break;
            case 'trade_no': $description = '平台交易号'; break;
            default: $description = '其他字段';
        }
        
        $displayValue = is_array($value) ? json_encode($value) : $value;
        echo "<tr><td><strong>{$key}</strong></td><td>" . htmlspecialchars($displayValue) . "</td><td>{$description}</td></tr>";
    }
    echo "</table>";
    
    // 分析应该使用哪个处理逻辑
    echo "<h4>处理逻辑分析：</h4>";
    if(isset($result['payurl'])) {
        echo "<p>✅ 检测到 payurl 字段，应该使用支付链接处理</p>";
        echo "<p>payurl 内容: " . htmlspecialchars($result['payurl']) . "</p>";
        
        if(strpos($result['payurl'], 'weixin://') === 0) {
            echo "<p>🔍 这是微信URL scheme，应该显示二维码</p>";
        } elseif(strpos($result['payurl'], 'wxp://') === 0) {
            echo "<p>🔍 这是微信支付链接，应该显示二维码</p>";
        } else {
            echo "<p>🔍 这是普通HTTP链接</p>";
        }
    }
    
    if(isset($result['qrcode'])) {
        echo "<p>✅ 检测到 qrcode 字段，应该直接显示二维码</p>";
        echo "<p>qrcode 内容: " . htmlspecialchars($result['qrcode']) . "</p>";
    }
    
    if(isset($result['urlscheme'])) {
        echo "<p>✅ 检测到 urlscheme 字段，应该使用URL scheme处理</p>";
        echo "<p>urlscheme 内容: " . htmlspecialchars($result['urlscheme']) . "</p>";
    }
    
} else {
    echo "<p>❌ 无法解析为JSON格式</p>";
}

echo "<br><a href='test.php'>返回测试页面</a>";
?>
