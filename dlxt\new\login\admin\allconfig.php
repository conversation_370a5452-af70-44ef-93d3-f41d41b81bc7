<?php
include('./auth.php');
//福利设置
$fuliitems=$DB->getRow("select * from `config` where `keys`='fuliitems' limit 1");
$fulis = explode(';', $fuliitems['values']);
//赠送设置
$zengsongitems=$DB->getRow("select * from `config` where `keys`='zengsong' limit 1");
//IP限制
$limitAccount=$DB->getRow("select * from `config` where `keys`='limitAccount' limit 1");


?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>综合配置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>新手福利奖励</legend>
			<div class="form-group">
              <div class="col-xs-6">
				<label>选择奖励物品</label>
				<select name="itemid" id="itemid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($res['itemid'] == $fulis[0] ){ 
					echo '<option value="'.$res['itemid'].'" selected="selected">'.$res['name'].'</option>';
				}else{
					echo '<option value="'.$res['itemid'].'" >'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#itemid').select2( {placeholder: '请选择'} );</script>
              </div>
              <div class="col-xs-6">
              <label for="number">奖励物品数量</label>
                <input class="form-control" type="number" name="number" value="<?php echo $fulis[1]; ?>" placeholder="请输入物品数量">
              </div>
            </div>
			<legend>绑定新角色赠送</legend>
			<div class="form-group">
              <div class="col-xs-12">
              <label for="zengsong">赠送平台币数量</label>
                <input class="form-control" type="number" name="zengsong" value="<?php echo $zengsongitems['values']; ?>" placeholder="请输入赠送平台币数量">
              </div>
            </div>
			<legend>注册IP限制</legend>
			<div class="form-group">
              <div class="col-xs-12">
              <label for="limitAccount">每个IP限制注册账号数量（0为不限制）</label>
                <input class="form-control" type="number" name="limitAccount" value="<?php echo $limitAccount['values']; ?>" placeholder="请输入每个IP可以注册的账号数量">
              </div>
            </div>
			
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=allset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>