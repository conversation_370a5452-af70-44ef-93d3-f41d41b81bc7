<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='add'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return add(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>区组名称</label>
              <input type="text" class="form-control"  name="quzuname" value="" placeholder="请输入区组名称" />
            </div>
            <div class="form-group col-md-12">
              <label>大区名称</label>
              <input type="text" class="form-control"  name="name" value="" placeholder="请输入大区名称" />
            </div>
            <div class="form-group col-md-12">
              <label>开服日期</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="info" placeholder="请输入开服日期">
					<span class="input-group-addon">开服</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>大区ID</label>
              <input type="number" class="form-control"  name="serverid" value="" placeholder="请输入大区ID，例：1101961001" />
            </div>
            <div class="form-group col-md-12">
              <label>大区IP</label>
              <input type="text" class="form-control"  name="ip" value="" placeholder="请输入大区IP" />
            </div>
            <div class="form-group col-md-12">
              <label>大区端口</label>
              <input type="number" class="form-control"  name="quport" value="" placeholder="请输入大区端口，例：10003" />
            </div>
			<div class="form-group col-md-12">
				<label>区服状态</label>
				<select name="deng" class="form-control">
				<option value="1">繁忙(黄色)</option>
				<option value="2">流畅(绿色)</option>
				<option value="3">正常(棕色)</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>区服标志</label>
				<select name="biao" class="form-control">
				<option value="0">推荐</option>
				<option value="1">新服</option>
				<option value="2">推荐+新服</option>
				<option value="3">无标志</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <label>GM端口</label>
              <input type="number" class="form-control" name="port" value="" placeholder="请输入GM端口，例：10980" />
            </div>
            <div class="form-group col-md-12">
              <label>平台币充值比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="ptb" value="" placeholder="请输入平台币充值比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>累计充值比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="charge" value="" placeholder="请输入累计充值比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>充值赠送VIP经验比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="vip" value="" placeholder="请输入充值赠送VIP经验比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>充值赠送仙玉比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="xianyu" value="" placeholder="请输入充值赠送仙玉比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='edit'){
	$id = intval($get['id']);
	$checkservers=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $id . "' limit 1");
	if(!$checkservers){echo "<script>layer.ready(function(){layer.msg('该区服信息信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return edit(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>区组名称</label>
              <input type="text" class="form-control"  name="quzuname" value="<?php echo $checkservers['quzuname'] ;?>" placeholder="请输入区组名称" />
              <input type="text" class="form-control"  name="id" value="<?php echo $checkservers['id'] ;?>" style="display:none" />
            </div>
            <div class="form-group col-md-12">
              <label>大区名称</label>
              <input type="text" class="form-control"  name="name" value="<?php echo $checkservers['name'] ;?>" placeholder="请输入大区名称" />
            </div>
            <div class="form-group col-md-12">
              <label>开服日期</label>
				<div class="input-group m-b-10">
					<input type="text" class="form-control" name="info" value="<?php echo $checkservers['info'] ;?>" placeholder="请输入开服日期">
					<span class="input-group-addon">开服</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>大区ID</label>
              <input type="text" class="form-control"  name="serverid"  value="<?php echo $checkservers['serverid'] ;?>" placeholder="请输入大区ID，例：1101961001" />
            </div>
            <div class="form-group col-md-12">
              <label>大区IP</label>
              <input type="text" class="form-control"  name="ip"  value="<?php echo $checkservers['ip'] ;?>" placeholder="请输入大区IP" />
            </div>
            <div class="form-group col-md-12">
              <label>大区端口</label>
              <input type="text" class="form-control"  name="quport"  value="<?php echo $checkservers['quport'] ;?>" placeholder="请输入大区端口，例：10003" />
            </div>
			<div class="form-group col-md-12">
				<label>区服状态</label>
				<select name="deng" class="form-control">
				<?php 
				if($checkservers['deng']==1){
				$select1 = ' selected="selected"';	
				} 
				if($checkservers['deng']==2){
				$select2 = ' selected="selected"';	
				} 
				if($checkservers['deng']==3){
				$select3 = ' selected="selected"';	
				} 
				?>
				<option value="1" <?php echo $select1 ;?>>繁忙(黄色)</option>
				<option value="2" <?php echo $select2 ;?>>流畅(绿色)</option>
				<option value="3" <?php echo $select3 ;?>>正常(棕色)</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>区服标志</label>
				<select name="biao" class="form-control">
				<?php 
				if($checkservers['biao']==0){
				$select4 = ' selected="selected"';	
				} 
				if($checkservers['biao']==1){
				$select5 = ' selected="selected"';	
				} 
				if($checkservers['biao']==2){
				$select6 = ' selected="selected"';	
				} 
				if($checkservers['biao']==3){
				$select7 = ' selected="selected"';	
				} 
				?>
				<option value="0" <?php echo $select4 ;?>>推荐</option>
				<option value="1" <?php echo $select5 ;?>>新服</option>
				<option value="2" <?php echo $select6 ;?>>推荐+新服</option>
				<option value="3" <?php echo $select7 ;?>>无标志</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <label>GM端口</label>
              <input type="text" class="form-control" name="port"  value="<?php echo $checkservers['port'] ;?>" placeholder="请输入GM端口，例：10980" />
            </div>
            <div class="form-group col-md-12">
              <label>平台币充值比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="ptb" value="<?php echo $checkservers['ptb'] ;?>" placeholder="请输入平台币充值比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>累计充值比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="charge" value="<?php echo $checkservers['charge'] ;?>" placeholder="请输入累计充值比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>充值赠送VIP经验比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="vip" value="<?php echo $checkservers['vip'] ;?>" placeholder="请输入充值赠送VIP经验比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <label>充值赠送仙玉比例（不开启请输入0）</label>
				<div class="input-group m-b-10">
					<span class="input-group-addon">1：</span>
					<input type="number" class="form-control" name="xianyu" value="<?php echo $checkservers['xianyu'] ;?>" placeholder="请输入充值赠送仙玉比例" />
				</div>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
  
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function add(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addservers',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function edit(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editservers',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>