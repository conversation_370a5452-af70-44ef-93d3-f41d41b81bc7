<?php
/**
 * 支付API调试工具
 * 用于检查支付接口返回的实际内容
 */

require_once("config.php");
require_once("lib/EpayCore.class.php");

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>支付API调试</title>";
echo "<style>body{font-family:Arial;margin:20px;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;} .code{background:#f5f5f5;padding:10px;font-family:monospace;white-space:pre-wrap;}</style>";
echo "</head><body>";

echo "<h1>🔧 支付API调试工具</h1>";

// 显示当前配置
echo "<div class='section'>";
echo "<h2>📋 当前配置</h2>";
echo "<div class='code'>";
echo "API地址: " . $epay_config['apiurl'] . "\n";
echo "商户ID: " . $epay_config['pid'] . "\n";
echo "商户密钥: " . substr($epay_config['key'], 0, 8) . "..." . "\n";
echo "</div>";
echo "</div>";

// 测试参数
$test_params = array(
    'pid' => $epay_config['pid'],
    'type' => 'wxpay',  // 测试微信支付
    'out_trade_no' => 'TEST_' . time(),
    'notify_url' => 'http://test.com/notify',
    'return_url' => 'http://test.com/return',
    'name' => '测试商品',
    'money' => '0.01'
);

echo "<div class='section'>";
echo "<h2>🧪 测试参数</h2>";
echo "<div class='code'>" . json_encode($test_params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
echo "</div>";

// 创建EpayCore实例
$epay = new EpayCore($epay_config);

// 测试1: 页面跳转支付 (submit.php)
echo "<div class='section'>";
echo "<h2>🔗 测试1: 页面跳转支付 (submit.php)</h2>";

try {
    $payUrl = $epay->getPayLink($test_params);
    echo "<strong>生成的支付链接:</strong><br>";
    echo "<div class='code'>" . htmlspecialchars($payUrl) . "</div>";
    
    echo "<a href='" . htmlspecialchars($payUrl) . "' target='_blank' style='padding:10px 20px;background:#007bff;color:white;text-decoration:none;border-radius:5px;'>测试跳转</a>";
} catch (Exception $e) {
    echo "<div style='color:red;'>错误: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 测试2: API接口支付 (mapi.php)
echo "<div class='section'>";
echo "<h2>📱 测试2: API接口支付 (mapi.php)</h2>";

try {
    $apiResult = $epay->apiPay($test_params);
    echo "<strong>API返回结果:</strong><br>";
    echo "<div class='code'>" . json_encode($apiResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    
    if (isset($apiResult['payurl'])) {
        echo "<br><strong>支付链接:</strong><br>";
        echo "<div class='code'>" . htmlspecialchars($apiResult['payurl']) . "</div>";
        
        // 检查是否为URL scheme
        if (strpos($apiResult['payurl'], 'weixin://') === 0) {
            echo "<div style='color:orange;'>⚠️ 检测到微信URL scheme</div>";
        } elseif (strpos($apiResult['payurl'], 'alipays://') === 0) {
            echo "<div style='color:orange;'>⚠️ 检测到支付宝URL scheme</div>";
        } else {
            echo "<div style='color:green;'>✅ 普通HTTP链接</div>";
        }
        
        echo "<br><a href='" . htmlspecialchars($apiResult['payurl']) . "' target='_blank' style='padding:10px 20px;background:#28a745;color:white;text-decoration:none;border-radius:5px;'>测试API链接</a>";
    }
    
    if (isset($apiResult['qrcode'])) {
        echo "<br><strong>二维码链接:</strong><br>";
        echo "<div class='code'>" . htmlspecialchars($apiResult['qrcode']) . "</div>";
        echo "<br><img src='" . htmlspecialchars($apiResult['qrcode']) . "' alt='支付二维码' style='border:1px solid #ddd;'>";
    }
    
} catch (Exception $e) {
    echo "<div style='color:red;'>错误: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 测试3: 支付宝支付
$test_params_alipay = $test_params;
$test_params_alipay['type'] = 'alipay';
$test_params_alipay['out_trade_no'] = 'TEST_ALIPAY_' . time();

echo "<div class='section'>";
echo "<h2>💰 测试3: 支付宝API接口</h2>";

try {
    $alipayResult = $epay->apiPay($test_params_alipay);
    echo "<strong>支付宝API返回结果:</strong><br>";
    echo "<div class='code'>" . json_encode($alipayResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    
    if (isset($alipayResult['payurl'])) {
        echo "<br><strong>支付链接:</strong><br>";
        echo "<div class='code'>" . htmlspecialchars($alipayResult['payurl']) . "</div>";
        
        // 检查是否为URL scheme
        if (strpos($alipayResult['payurl'], 'alipays://') === 0) {
            echo "<div style='color:orange;'>⚠️ 检测到支付宝URL scheme</div>";
        } else {
            echo "<div style='color:green;'>✅ 普通HTTP链接</div>";
        }
        
        echo "<br><a href='" . htmlspecialchars($alipayResult['payurl']) . "' target='_blank' style='padding:10px 20px;background:#ff6600;color:white;text-decoration:none;border-radius:5px;'>测试支付宝链接</a>";
    }
    
} catch (Exception $e) {
    echo "<div style='color:red;'>错误: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 测试4: 直接请求API查看原始响应
echo "<div class='section'>";
echo "<h2>🔍 测试4: 原始API响应</h2>";

// 构建签名
ksort($test_params);
$signstr = '';
foreach($test_params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$sign = md5($signstr);

$test_params['sign'] = $sign;
$test_params['sign_type'] = 'MD5';

$mapi_url = $epay_config['apiurl'] . 'mapi.php';
$post_data = http_build_query($test_params);

echo "<strong>请求URL:</strong><br>";
echo "<div class='code'>" . htmlspecialchars($mapi_url) . "</div>";

echo "<strong>POST数据:</strong><br>";
echo "<div class='code'>" . htmlspecialchars($post_data) . "</div>";

// 发送请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $mapi_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<strong>HTTP状态码:</strong> " . $http_code . "<br>";
if ($error) {
    echo "<strong>CURL错误:</strong> " . $error . "<br>";
}

echo "<strong>原始响应:</strong><br>";
echo "<div class='code'>" . htmlspecialchars($response) . "</div>";

if ($response) {
    $json_data = json_decode($response, true);
    if ($json_data) {
        echo "<strong>解析后的JSON:</strong><br>";
        echo "<div class='code'>" . json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    }
}

echo "</div>";

// 设备检测
echo "<div class='section'>";
echo "<h2>📱 设备检测</h2>";
echo "<div class='code'>";
echo "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "\n";
echo "是否移动端: " . (preg_match('/(android|iphone|ipad|mobile)/i', $_SERVER['HTTP_USER_AGENT']) ? '是' : '否') . "\n";
echo "是否微信: " . (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false ? '是' : '否') . "\n";
echo "</div>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>💡 建议</h2>";
echo "<div style='line-height:1.6;'>";
echo "1. 如果API返回的是 <code>weixin://</code> 或 <code>alipays://</code> 链接，这就是导致 ERR_UNKNOWN_URL_SCHEME 错误的原因<br>";
echo "2. 移动端浏览器无法直接处理这些URL scheme，需要特殊处理<br>";
echo "3. 建议使用我们开发的 URL scheme 处理器来解决这个问题<br>";
echo "4. 也可以考虑在移动端使用二维码支付方式<br>";
echo "</div>";
echo "</div>";

echo "</body></html>";
?>
