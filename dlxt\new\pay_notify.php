<?php
include './common/main.php';

// 支付配置
$epay_config = array(
    'apiurl' => 'https://sheng.nachengweb.com/',
    'pid' => '1093',
    'key' => '72jJHMDjjYBqMmZjMjrR7mbt27Zu3Mjy'
);

// 获取回调参数
$pid = $_GET['pid'];
$trade_no = $_GET['trade_no'];
$out_trade_no = $_GET['out_trade_no'];
$type = $_GET['type'];
$name = $_GET['name'];
$money = $_GET['money'];
$trade_status = $_GET['trade_status'];
$sign = $_GET['sign'];

// 验证签名
$params = array(
    'pid' => $pid,
    'trade_no' => $trade_no,
    'out_trade_no' => $out_trade_no,
    'type' => $type,
    'name' => $name,
    'money' => $money,
    'trade_status' => $trade_status
);

ksort($params);
$signstr = '';
foreach($params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$verify_sign = md5($signstr);

if($verify_sign == $sign && $trade_status == 'TRADE_SUCCESS') {
    // 查询订单
    $payorderData = $DB->getRow("SELECT * FROM `pay_order` WHERE `orderid` = ?", [$out_trade_no]);
    
    if(!$payorderData) {
        exit('订单不存在');
    }
    
    if($payorderData['status'] != 0) {
        exit('success'); // 订单已处理
    }
    
    // 验证金额
    if($payorderData['money'] != $money) {
        exit('金额不匹配');
    }
    
    // 更新订单状态
    $DB->exec("UPDATE `pay_order` SET `status` = 1 WHERE `orderid` = ?", [$out_trade_no]);
    
    // 生成充值文件
    $content = $payorderData['user'] . ':' . $payorderData['roleid'] . ':' . $payorderData['param'] . ':' . $payorderData['money'];
    file_put_contents('zhifu.txt', $content);
    
    // 记录日志
    $log = date('Y-m-d H:i:s') . " - 支付成功 - 订单号: {$out_trade_no} - 用户: {$payorderData['user']} - 金额: {$money}\n";
    file_put_contents('pay_log.txt', $log, FILE_APPEND);
    
    exit('success');
} else {
    // 记录失败日志
    $log = date('Y-m-d H:i:s') . " - 支付验证失败 - 订单号: {$out_trade_no} - 签名: {$sign} - 验证签名: {$verify_sign}\n";
    file_put_contents('pay_error_log.txt', $log, FILE_APPEND);
    
    exit('fail');
}
?>
