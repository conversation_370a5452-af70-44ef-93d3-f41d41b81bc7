/**************关联账号start**************/
.bg {
    width: 100%;
    height: 100%;
}

.bg .add-header {
    background: #D7996E;
}

.bg .add-header {
    background: #D7996E;
}

.bg .add-content {
    background: none;
}

.bg .add-content .add-box {
    box-sizing: border-box;
    padding: 0 35px;
    margin-top: 190px;
}

.bg .add-content .add-box .add-circle {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 16px;
    width: 100%;
    height: 44px;
    border: 1px solid #fff;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 44px;
}

.bg .add-content .add-box .add-circle .add-user {
    position: relative;
    min-width: 35px;
}

.bg .add-content .add-box .add-circle .add-user:after {
    content: "";
    position: absolute;
    width: 1px;
    height: 15px;
    background: #fff;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}

.bg .add-content .add-box .add-circle .add-user .add-user-img {
    width: 20px;
    height: 20px;
}

.bg .add-content .add-box .add-circle .add-input {
    box-sizing: border-box;
    padding: 0 13px;
}

.bg .add-content .add-box .add-circle .add-input input {
    color: #fff;
}

.bg .add-content .add-box .add-circle .add-input input::-webkit-input-placeholder {
    color: #fff !important;
}

.bg .add-content .add-box .add-circle .add-input input:-moz-placeholder {
    color: #fff !important;

}

.bg .add-content .add-box .add-circle .add-input input::-moz-placeholder {
    color: #fff !important;
}

.bg .add-content .add-box .add-circle .add-input input:-ms-input-placeholder {
    color: #fff !important;
}

.bg .add-content .add-box .add-btn {
    display: block;
    width: 230px;
    height: 44px;
    margin: 50px auto 0 auto;
    border-radius: 0;
}

/*************关联账号end*************/


/*************账号管理 start*************/
.account-box {
    box-sizing: border-box;
    padding: 15px 15px 0 15px;
}

.account-box .account-top {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 62px;
    background: #fbb648;
    border-radius: 10px;
}

.account-box .account-top .account-font .account-mobile {
    text-align: center;
    color: #fff;
    font-size: 17px;
    font-weight: bold;
}

.account-box .account-top .account-font .account-name {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    font-size: 10px;
}

.account-box .account-list {
    width: 100%;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 10px;
    margin-top: 10px;
    box-sizing: border-box;
    padding: 15px 13px;
}

.account-box .account-list .account-list-name {
    font-weight: bold;
    font-size: 15px;
}

.account-box .account-list .account-list-name {
    font-weight: bold;
}

.account-box .account-list .account-list-bar {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 9px 0;
}

.account-box .account-list .account-list-bar:last-child {
    padding: 9px 0 0 0;
}

.account-box .account-list .account-list-bar .number {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.account-box .account-list .account-list-bar .login-btn {
    width: 47px;
    height: 20px;
    background: #E94B21;
    border-radius: 5px;
    margin-right: 8px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 20px;
}

.account-box .account-list .account-list-bar .del-btn {
    width: 47px;
    height: 20px;
    background: #D7996E;
    border-radius: 5px;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 20px;
}

.account-box .account-btn {
    display: block;
    width: 230px;
    height: 44px;
    margin: 40px auto 0 auto;
    border-radius: 0;
    text-align: center;
    line-height: 44px;
    color: #fff;
    font-size: 15px;
    background-color: #fbb648;
    border-radius: 1rem;
    -webkit-border-radius: 1rem;
    -moz-border-radius: 1rem;
    -ms-border-radius: 1rem;
    -o-border-radius: 1rem;
}

.account-box .account-margin {
    margin: 15px auto 0 auto;
}

/*************账号管理 end*************/


/*************个人中心 start*************/
.member-bg {
    box-sizing: border-box;
    height: 25vh;
    padding: 15px 15px 0 15px;
    /*overflow: hidden;*/
}

.member-bg .head {
    /*position: static;*/
    box-sizing: border-box;
    padding: 0;
    display: flex;
    justify-content: space-between;
}

.member-bg .income-box {
    background: #fff;
    height: 20vh;
    margin-top: 2vh;
    width: 100%;
    border-radius: 10px;
}

.member-bg .income-box a {
    text-decoration: none;
}

.person-body {
    box-sizing: border-box;
    padding: 0 8px;
    z-index: 999;

}

.person-body .person-main {
    border-radius: 5px;
}

.person-body .person-padd {
    box-sizing: border-box;
    /*padding: 0 10px 40px;*/
    display: flex;
    flex-wrap: wrap;
    /* padding-top: 10px; */
}

.person-body .person-main .person-item {
    text-decoration: none;
}

.person-body .person-main .person-item .person-ico {
    width: 35px;
    height: 35px;

}

.person-body .person-main .person-cell {
    display: flex;
    align-items: center;
    height: 50px;
    box-sizing: border-box;
    padding: 0;
    text-decoration: none;
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    color: #000;
    background-color: #fff;
    background-size: 100% 100%;
}

.person-body .person-main .person-cell:after {
    content: " ";
    display: inline-block;
    height: 8px;
    width: 8px;
    border-width: 2px 2px 0 0;
    border-color: #868484;
    border-style: solid;
    -webkit-transform: matrix(.71, .71, -.71, .71, 0, 0);
    transform: matrix(.71, .71, -.71, .71, 0, 0);
    position: relative;
    top: -2px;
    position: absolute;
    top: 50%;
    margin-top: -4px;
    right: 22px;
}

.person-body .person-main .person-cell .list-ico {
    width: 35px;
    height: 35px;
    /*margin-bottom: 8px;*/
    margin: 0 10px 0 15px;
}

.person-body .person-main .person-cell .person-cell-bd {
    color: #868484;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: content;
    justify-content: space-between;
    padding-right: 40px;
}

/*************个人中心 end*************/