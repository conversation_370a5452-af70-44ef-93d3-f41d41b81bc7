<?php
include "config.php";

echo "<h2>支付系统测试 - pay/ 目录版本</h2>";

// 测试数据库连接
try {
    echo "✅ 数据库连接成功<br>";
    
    // 测试查询pay_order表
    $orders = $DBDL->query("SELECT * FROM `pay_order` ORDER BY id DESC LIMIT 5")->fetchAll();
    echo "✅ pay_order表查询成功，共有 " . count($orders) . " 条最新记录<br>";
    
    if(count($orders) > 0) {
        echo "<h3>最新订单：</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>订单号</th><th>用户</th><th>金额</th><th>状态</th><th>时间</th></tr>";
        foreach($orders as $order) {
            $status = $order['status'] == 1 ? '<span style="color: green;">已支付</span>' : '<span style="color: red;">未支付</span>';
            echo "<tr>";
            echo "<td>{$order['orderid']}</td>";
            echo "<td>{$order['user']}</td>";
            echo "<td>￥{$order['money']}</td>";
            echo "<td>{$status}</td>";
            echo "<td>{$order['date']} {$order['time']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 测试支付配置
    echo "<h3>支付配置：</h3>";
    echo "商户ID: 1093<br>";
    echo "API地址: https://sheng.nachengweb.com/<br>";
    echo "商户KEY: 72jJHMDj...<br>";
    
    // 测试回调URL
    echo "<h3>回调URL：</h3>";
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $notify_url = $protocol."://".$host."/pay/notify_url.php";
    $return_url = $protocol."://".$host."/pay/return_url.php";
    echo "异步回调: <a href='{$notify_url}' target='_blank'>{$notify_url}</a><br>";
    echo "同步回调: <a href='{$return_url}' target='_blank'>{$return_url}</a><br>";
    
    echo "<h3>功能链接：</h3>";
    echo "<a href='/pay/' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>充值页面</a>";
    echo "<a href='/login/admin/' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>管理后台</a>";
    echo "<a href='/' style='display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>返回首页</a>";
    
    // 模拟支付测试
    if(isset($_GET['test_pay'])) {
        echo "<h3>创建测试订单：</h3>";
        
        // 生成测试订单
        $test_order_id = 'TEST' . time() . rand(1000, 9999);
        $test_user = 'test_user_' . rand(100, 999);
        $test_money = '6.00';
        $test_role = '测试角色';
        $test_server = '测试服务器';
        $test_roleid = rand(10000, 99999);
        
        $DBDL->exec("INSERT INTO `pay_order` (`orderid`,`ordertype`,`value`,`user`,`roleid`,`rolename`,`qu`,`agent`,`money`,`status`,`ip`,`city`,`date`,`time`,`param`)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $test_order_id,
                '1',
                '00',
                $test_user,
                $test_roleid,
                $test_role,
                $test_server,
                '[1];',
                $test_money,
                '0',
                $_SERVER['REMOTE_ADDR'],
                '测试',
                date('Y-m-d'),
                date('H:i:s'),
                $test_user . '-1-' . $test_roleid
            ]
        );
        
        echo "✅ 测试订单创建成功！<br>";
        echo "订单号: {$test_order_id}<br>";
        echo "用户: {$test_user}<br>";
        echo "金额: ￥{$test_money}<br>";
        echo "<a href='test.php'>刷新页面查看</a>";
    } else {
        echo "<br><a href='?test_pay=1' style='display: inline-block; padding: 10px 20px; background: #ffc107; color: black; text-decoration: none; border-radius: 5px; margin: 5px;'>创建测试订单</a>";
    }
    
} catch(Exception $e) {
    echo "❌ 错误：" . $e->getMessage();
}

echo "<br><br><hr>";
echo "<h3>系统状态：</h3>";
echo "当前端口: " . $_SERVER['SERVER_PORT'] . "<br>";
echo "当前域名: " . $_SERVER['HTTP_HOST'] . "<br>";
echo "当前路径: " . $_SERVER['REQUEST_URI'] . "<br>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "服务器时间: " . date('Y-m-d H:i:s') . "<br>";

echo "<h3>目录结构：</h3>";
echo "充值首页: /pay/<br>";
echo "支付API: /pay/api.php<br>";
echo "异步回调: /pay/notify_url.php<br>";
echo "同步回调: /pay/return_url.php<br>";
echo "测试页面: /pay/test.php<br>";
?>
