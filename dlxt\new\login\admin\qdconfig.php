<?php
include('./auth.php');
//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
$keywords=$DB->getRow("select * from `config` where `keys`='keywords' limit 1");
$description=$DB->getRow("select * from `config` where `keys`='description' limit 1");
//版权信息
$banquan=$DB->getRow("select * from `config` where `keys`='banquan' limit 1");
//版权信息
$logoimg=$DB->getRow("select * from `config` where `keys`='logoimg' limit 1");
//版权信息
$bjimg=$DB->getRow("select * from `config` where `keys`='bjimg' limit 1");

$qdmin=$DB->getRow("select * from `config` where `keys`='qdmin' limit 1");
$qdmax=$DB->getRow("select * from `config` where `keys`='qdmax' limit 1");
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>其他设置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>签到平台币数量</legend>
			<div class="form-group">
              <label class="col-xs-12">最小值</label>
              <div class="col-xs-12">
                <textarea class="form-control" text="text" name="qdmin" rows="1" placeholder="请输入数量"><?php echo $qdmin['values']; ?></textarea>
               
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">最大值</label>
              <div class="col-xs-12">
                <textarea class="form-control" text="text" name="qdmax" rows="1" placeholder="请输入数量"><?php echo $qdmax['values']; ?></textarea>
               
              </div>
            </div>
		
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=qdconfig',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>