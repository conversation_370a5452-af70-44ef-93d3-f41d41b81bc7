# 端口配置说明

## 当前支持的端口配置

系统已经配置为自动适配端口，支持以下几种访问方式：

### 1. 使用168端口
- 网站地址：`http://************:168/`
- 充值页面：`http://************:168/chongzhi.php`
- 管理后台：`http://************:168/login/admin/`

### 2. 使用默认80端口
- 网站地址：`http://************/`
- 充值页面：`http://************/chongzhi.php`
- 管理后台：`http://************/login/admin/`

### 3. 使用HTTPS (443端口)
- 网站地址：`https://************/`
- 充值页面：`https://************/chongzhi.php`
- 管理后台：`https://************/login/admin/`

## 自动端口检测

系统会自动检测当前访问的端口和协议：

```php
// 自动检测协议
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';

// 自动检测主机和端口
$host = $_SERVER['HTTP_HOST']; // 包含端口信息，如 ************:168

// 生成回调URL
$notify_url = $protocol."://".$host."/pay_notify.php";
$return_url = $protocol."://".$host."/pay_return.php";
```

## 配置步骤

### 方法1：修改为168端口
1. 修改Web服务器配置，将网站绑定到168端口
2. 访问 `http://************:168/` 即可

### 方法2：使用默认80端口
1. 修改Web服务器配置，将网站绑定到80端口
2. 访问 `http://************/` 即可

### 方法3：域名绑定
1. 将域名解析到服务器IP
2. 配置Web服务器虚拟主机
3. 访问 `http://yourdomain.com/` 即可

## 注意事项

1. **回调URL自动适配**：支付回调URL会根据当前访问的域名和端口自动生成
2. **数据库配置**：无需修改数据库配置，系统使用相同的数据库
3. **文件路径**：所有文件路径都是相对路径，无需修改
4. **SSL支持**：系统自动检测HTTPS协议

## 测试方法

访问测试页面验证配置：
- `http://************:168/pay_test.php` (168端口)
- `http://************/pay_test.php` (80端口)

测试页面会显示：
- 当前端口
- 回调URL
- 数据库连接状态
- 订单查询功能

## 常见问题

### Q: 如何修改端口？
A: 修改Web服务器（IIS/Apache/Nginx）的端口配置即可，代码无需修改。

### Q: 支持多端口同时访问吗？
A: 支持，可以同时配置多个端口，每个端口的回调URL会自动适配。

### Q: 域名访问需要修改代码吗？
A: 不需要，系统会自动检测当前访问的域名和端口。
