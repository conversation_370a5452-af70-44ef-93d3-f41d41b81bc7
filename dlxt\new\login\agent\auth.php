<?php
/*
本后台只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
----
*/
include '../../common/main.php';
if(!isset($_SESSION['type']) || $_SESSION['type'] != 2){
	header('Location:../ajax.php?act=logout');
	//var_dump($_SESSION['type']);
	exit;
}
if(isset($_COOKIE["admin_token"])){
	$token=authcode(daddslashes($_COOKIE['admin_token']), 'DECODE', SYS_KEY);
	list($username, $newpass) = explode("\t", $token);
	$adminData = $Admin->getAdmin($username);
	$session=md5($adminData['id'].$username.$adminData['id']);
	if($session != $newpass || $adminData['status'] != 1) {
			header('Location:../ajax.php?act=logout');
			//var_dump('2');
		exit;
	}else{
		if(isset($_SESSION['adminUser']) && isset($_SESSION['adminPass'])){
			$adminUser = $_SESSION['adminUser'];
			$adminPass = $_SESSION['adminPass'];
			if($adminUser !== $username || $adminPass !== $adminData['password']){
			header('Location:../ajax.php?act=logout');
			//var_dump('3');
			exit;
			}else{
			$_SESSION['adminLogin'] = 1;
			//网站信息
			$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
			$keywords=$DB->getRow("select * from `config` where `keys`='keywords' limit 1");
			$description=$DB->getRow("select * from `config` where `keys`='description' limit 1");
			//版权信息
			$banquan=$DB->getRow("select * from `config` where `keys`='banquan' limit 1");
			//版权信息
			$logoimg=$DB->getRow("select * from `config` where `keys`='logoimg' limit 1");
			}
		}else{
			header('Location:../ajax.php?act=logout');
			//var_dump('4');
			exit;
		}
	}
}else{
			header('Location:../ajax.php?act=logout');
			//var_dump('4');
			exit;
}
?>