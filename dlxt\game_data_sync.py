#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
游戏数据自动同步脚本
从 data/ 目录读取账号信息并导入到数据库
"""

import os
import sys
import pymysql
import configparser
from datetime import datetime
import hashlib
import time
import signal
import shutil

# 配置信息
CONFIG = {
    'DATA_DIR': 'C:/server/data',
    'BACKUP_DIR': 'C:/server/backup',
    'DB_HOST': '127.0.0.1',
    'DB_PORT': 3306,
    'DB_USER': 'root',
    'DB_PASS': 'gch19871004',
    'DB_NAME': 'daili'
}

# 全局控制变量
running = True
sync_interval = 300  # 5分钟 = 300秒
last_backup_date = None  # 记录最后备份日期

def signal_handler(signum, frame):
    """信号处理函数"""
    global running
    log_message("接收到停止信号，正在优雅退出...")
    running = False

# 注册信号处理
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

def log_message(message):
    """输出带时间戳的日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def backup_character_logs():
    """备份角色日志功能"""
    try:
        log_message("开始备份角色日志...")

        # 创建备份根目录
        backup_root = CONFIG['BACKUP_DIR']
        if not os.path.exists(backup_root):
            os.makedirs(backup_root)
            log_message(f"创建备份根目录: {backup_root}")

        # 创建今日备份目录
        today = datetime.now().strftime('%Y-%m-%d')
        today_backup_dir = os.path.join(backup_root, today)
        if not os.path.exists(today_backup_dir):
            os.makedirs(today_backup_dir)
            log_message(f"创建今日备份目录: {today_backup_dir}")

        backup_stats = {
            'accounts_processed': 0,
            'characters_processed': 0,
            'logs_copied': 0,
            'errors': 0
        }

        # 遍历所有账号文件夹
        data_dir = CONFIG['DATA_DIR']
        if not os.path.exists(data_dir):
            log_message(f"数据目录不存在: {data_dir}")
            return backup_stats

        for account_folder in os.listdir(data_dir):
            account_path = os.path.join(data_dir, account_folder)

            # 跳过非目录和排除的账号
            if not os.path.isdir(account_path) or account_folder in ['xiaoshendada']:
                continue

            backup_stats['accounts_processed'] += 1
            log_message(f"备份账号: {account_folder}")

            # 遍历账号下的角色文件夹
            for item in os.listdir(account_path):
                character_path = os.path.join(account_path, item)

                # 跳过非目录项
                if not os.path.isdir(character_path):
                    continue

                # 检查是否是角色文件夹（通常是数字）
                if not item.isdigit():
                    continue

                backup_stats['characters_processed'] += 1

                # 查找日志记录文件夹
                log_dir = os.path.join(character_path, '日志记录')
                if os.path.exists(log_dir) and os.path.isdir(log_dir):
                    try:
                        # 创建备份目标路径
                        backup_target = os.path.join(today_backup_dir, account_folder, item, '日志记录')

                        # 确保目标目录存在
                        os.makedirs(backup_target, exist_ok=True)

                        # 统计原始文件数量
                        original_files = os.listdir(log_dir)
                        file_count = len(original_files)

                        if file_count > 0:
                            # 移动日志记录文件夹内的所有文件
                            moved_count = 0
                            for log_file in original_files:
                                source_file = os.path.join(log_dir, log_file)
                                target_file = os.path.join(backup_target, log_file)

                                # 如果是文件，则移动
                                if os.path.isfile(source_file):
                                    # 如果目标文件已存在，先删除
                                    if os.path.exists(target_file):
                                        os.remove(target_file)

                                    shutil.move(source_file, target_file)
                                    moved_count += 1
                                # 如果是文件夹，则移动整个文件夹
                                elif os.path.isdir(source_file):
                                    if os.path.exists(target_file):
                                        shutil.rmtree(target_file)

                                    shutil.move(source_file, target_file)
                                    moved_count += 1

                            backup_stats['logs_copied'] += 1
                            log_message(f"移动日志: {account_folder}/{item}/日志记录 ({moved_count} 个项目)")
                        else:
                            log_message(f"跳过 {account_folder}/{item}: 日志记录文件夹为空")

                    except Exception as e:
                        backup_stats['errors'] += 1
                        log_message(f"移动失败 {account_folder}/{item}: {e}")

        # 输出备份统计
        log_message("角色日志备份完成")
        log_message(f"备份统计 - 账号: {backup_stats['accounts_processed']}, "
                   f"角色: {backup_stats['characters_processed']}, "
                   f"日志: {backup_stats['logs_copied']}, "
                   f"错误: {backup_stats['errors']}")

        return backup_stats

    except Exception as e:
        log_message(f"备份过程发生错误: {e}")
        return {'accounts_processed': 0, 'characters_processed': 0, 'logs_copied': 0, 'errors': 1}

def should_backup_today():
    """检查今天是否需要备份"""
    global last_backup_date

    today = datetime.now().date()
    current_hour = datetime.now().hour

    # 只在0点到1点之间执行备份
    if current_hour != 0:
        return False

    # 检查今天是否已经备份过
    if last_backup_date == today:
        return False

    return True

def check_and_backup():
    """检查并执行备份"""
    global last_backup_date

    if should_backup_today():
        log_message("检测到0点时间，开始执行日志备份...")
        backup_stats = backup_character_logs()

        # 更新最后备份日期
        last_backup_date = datetime.now().date()

        return backup_stats

    return None

def test_database_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(
            host=CONFIG['DB_HOST'],
            port=CONFIG['DB_PORT'],
            user=CONFIG['DB_USER'],
            password=CONFIG['DB_PASS'],
            database=CONFIG['DB_NAME'],
            charset='utf8mb4'
        )
        connection.close()
        return True
    except Exception as e:
        log_message(f"数据库连接失败: {e}")
        return False

def parse_account_info(file_path):
    """解析账号信息文件"""
    account_info = {
        'password': '123456',
        'invitation_code': '123',
        'login_ip': '127.0.0.1'
    }
    
    try:
        # 尝试不同的编码格式
        encodings = ['gbk', 'utf-8', 'gb2312']
        content = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            log_message(f"无法读取文件: {file_path}")
            return account_info
        
        # 解析配置
        for line in content.split('\n'):
            line = line.strip()
            if '=' in line and not line.startswith('['):
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                if key == '密码':
                    account_info['password'] = value
                elif key == '邀请码':
                    account_info['invitation_code'] = value
                elif key == '登录ip':
                    account_info['login_ip'] = value
        
        return account_info
        
    except Exception as e:
        log_message(f"解析账号信息失败 {file_path}: {e}")
        return account_info

def get_agent_id(invitation_code):
    """根据邀请码获取代理ID"""
    try:
        connection = pymysql.connect(
            host=CONFIG['DB_HOST'],
            port=CONFIG['DB_PORT'],
            user=CONFIG['DB_USER'],
            password=CONFIG['DB_PASS'],
            database=CONFIG['DB_NAME'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 查找代理
            sql = "SELECT id FROM admin WHERE username = %s"
            cursor.execute(sql, (invitation_code,))
            result = cursor.fetchone()
            
            if result:
                return result[0]
            else:
                # 返回默认代理ID=1
                return 1
                
    except Exception as e:
        log_message(f"查询代理ID失败: {e}")
        return 1
    finally:
        connection.close()

def account_exists(username):
    """检查账号是否已存在"""
    try:
        connection = pymysql.connect(
            host=CONFIG['DB_HOST'],
            port=CONFIG['DB_PORT'],
            user=CONFIG['DB_USER'],
            password=CONFIG['DB_PASS'],
            database=CONFIG['DB_NAME'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            sql = "SELECT COUNT(*) FROM account WHERE username = %s"
            cursor.execute(sql, (username,))
            result = cursor.fetchone()
            return result[0] > 0
            
    except Exception as e:
        log_message(f"检查账号存在性失败: {e}")
        return True  # 出错时假设存在，避免重复插入
    finally:
        connection.close()

def import_account(username, account_info):
    """导入账号到数据库"""
    try:
        connection = pymysql.connect(
            host=CONFIG['DB_HOST'],
            port=CONFIG['DB_PORT'],
            user=CONFIG['DB_USER'],
            password=CONFIG['DB_PASS'],
            database=CONFIG['DB_NAME'],
            charset='utf8mb4'
        )
        
        # 获取代理ID
        agent_id = get_agent_id(account_info['invitation_code'])
        
        # 生成盐值和加密密码
        salt = username + account_info['password']
        
        with connection.cursor() as cursor:
            # 先检查表结构，确定可用字段
            cursor.execute("SHOW COLUMNS FROM account")
            columns = [col[0] for col in cursor.fetchall()]

            # 根据实际字段构建SQL
            if 'regtime' in columns:
                sql = """
                INSERT INTO account (username, password, salt, agentid, ip, city, regtime)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """
                values = (username, account_info['password'], salt, agent_id, account_info['login_ip'], 'Unknown')
            elif 'addtime' in columns:
                sql = """
                INSERT INTO account (username, password, salt, agentid, ip, city, addtime)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """
                values = (username, account_info['password'], salt, agent_id, account_info['login_ip'], 'Unknown')
            elif 'create_time' in columns:
                sql = """
                INSERT INTO account (username, password, salt, agentid, ip, city, create_time)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """
                values = (username, account_info['password'], salt, agent_id, account_info['login_ip'], 'Unknown')
            else:
                # 只插入基本字段
                basic_fields = ['username', 'password', 'salt', 'agentid']
                available_fields = [f for f in basic_fields if f in columns]

                if 'ip' in columns:
                    available_fields.append('ip')
                if 'city' in columns:
                    available_fields.append('city')

                field_str = ', '.join(available_fields)
                placeholder_str = ', '.join(['%s'] * len(available_fields))

                sql = f"INSERT INTO account ({field_str}) VALUES ({placeholder_str})"

                values = [username, account_info['password'], salt, agent_id]
                if 'ip' in available_fields:
                    values.append(account_info['login_ip'])
                if 'city' in available_fields:
                    values.append('Unknown')

                values = tuple(values)

            cursor.execute(sql, values)

            # 记录日志（如果user_log表存在）
            try:
                cursor.execute("SHOW TABLES LIKE 'user_log'")
                if cursor.fetchone():
                    log_sql = """
                    INSERT INTO user_log (username, info, data, ip, city)
                    VALUES (%s, %s, NOW(), %s, %s)
                    """
                    log_info = f"Python脚本自动导入，原密码：{account_info['password']}，邀请码：{account_info['invitation_code']}"
                    cursor.execute(log_sql, (
                        username,
                        log_info,
                        account_info['login_ip'],
                        'Unknown'
                    ))
            except Exception as log_error:
                # 日志记录失败不影响主要功能
                log_message(f"记录日志失败: {log_error}")
            
            connection.commit()
            return True, agent_id
            
    except Exception as e:
        log_message(f"导入账号失败 {username}: {e}")
        return False, None
    finally:
        connection.close()

def sync_once():
    """执行一次同步"""
    print("=" * 60)
    log_message("游戏数据同步开始")

    # 排除的账号列表
    EXCLUDED_ACCOUNTS = ['xiaoshendada']

    # 检查数据目录
    if not os.path.exists(CONFIG['DATA_DIR']):
        log_message(f"错误: 数据目录不存在 {CONFIG['DATA_DIR']}")
        return

    log_message("数据目录检查通过")

    # 测试数据库连接
    if not test_database_connection():
        log_message("错误: 数据库连接失败")
        return

    log_message("数据库连接检查通过")

    # 统计变量
    processed = 0
    imported = 0
    skipped = 0
    errors = 0
    excluded = 0

    log_message("开始扫描账号文件夹...")

    # 遍历数据目录
    for folder_name in os.listdir(CONFIG['DATA_DIR']):
        # 在最开始就排除指定文件夹
        if folder_name in EXCLUDED_ACCOUNTS:
            excluded += 1
            log_message(f"跳过排除账号: {folder_name}")
            continue

        folder_path = os.path.join(CONFIG['DATA_DIR'], folder_name)

        if os.path.isdir(folder_path):
            username = folder_name
            info_file = os.path.join(folder_path, '账号信息.txt')

            if os.path.exists(info_file):
                processed += 1
                log_message(f"处理账号: {username}")

                # 检查账号是否已存在
                if account_exists(username):
                    skipped += 1
                    log_message(f"跳过已存在账号: {username}")
                    continue

                # 解析账号信息
                account_info = parse_account_info(info_file)

                # 导入账号
                success, agent_id = import_account(username, account_info)

                if success:
                    imported += 1
                    log_message(f"成功导入账号: {username} (代理ID: {agent_id})")
                else:
                    errors += 1
                    log_message(f"导入失败: {username}")
            else:
                log_message(f"警告: 账号信息文件不存在 {info_file}")

    # 输出统计结果
    log_message("同步完成")
    log_message(f"处理: {processed}, 导入: {imported}, 跳过: {skipped}, 排除: {excluded}, 错误: {errors}")
    print("=" * 60)

    return {
        'processed': processed,
        'imported': imported,
        'skipped': skipped,
        'excluded': excluded,
        'errors': errors
    }

def countdown_timer(seconds):
    """倒计时显示"""
    global running

    while seconds > 0 and running:
        mins, secs = divmod(seconds, 60)
        timer = f"{mins:02d}:{secs:02d}"
        print(f"\r下次同步倒计时: {timer} (按Ctrl+C停止)", end="", flush=True)
        time.sleep(1)
        seconds -= 1

    if running:
        print("\r" + " " * 50 + "\r", end="", flush=True)  # 清除倒计时显示

def main():
    """主函数 - 循环执行同步"""
    global running

    print("=" * 80)
    print("游戏数据自动同步服务启动")
    print(f"同步间隔: {sync_interval // 60} 分钟")
    print("按 Ctrl+C 停止服务")
    print("=" * 80)

    cycle_count = 0
    total_stats = {
        'total_processed': 0,
        'total_imported': 0,
        'total_skipped': 0,
        'total_excluded': 0,
        'total_errors': 0
    }

    try:
        while running:
            cycle_count += 1
            log_message(f"开始第 {cycle_count} 次同步循环")

            # 检查并执行备份（每日0点）
            try:
                backup_result = check_and_backup()
                if backup_result:
                    log_message("日志备份已完成")
            except Exception as e:
                log_message(f"备份过程中发生错误: {e}")

            # 执行一次同步
            try:
                stats = sync_once()

                # 累计统计
                total_stats['total_processed'] += stats['processed']
                total_stats['total_imported'] += stats['imported']
                total_stats['total_skipped'] += stats['skipped']
                total_stats['total_excluded'] += stats['excluded']
                total_stats['total_errors'] += stats['errors']

                log_message(f"第 {cycle_count} 次同步完成")
                log_message(f"累计统计 - 处理: {total_stats['total_processed']}, "
                          f"导入: {total_stats['total_imported']}, "
                          f"跳过: {total_stats['total_skipped']}, "
                          f"排除: {total_stats['total_excluded']}, "
                          f"错误: {total_stats['total_errors']}")

            except Exception as e:
                log_message(f"同步过程中发生错误: {e}")
                total_stats['total_errors'] += 1

            # 如果还要继续运行，则等待
            if running:
                log_message(f"等待 {sync_interval // 60} 分钟后进行下次同步...")
                countdown_timer(sync_interval)

    except KeyboardInterrupt:
        log_message("接收到键盘中断信号")

    finally:
        running = False
        print("\n" + "=" * 80)
        log_message("游戏数据自动同步服务已停止")
        log_message(f"总共执行了 {cycle_count} 次同步循环")
        log_message(f"最终统计 - 处理: {total_stats['total_processed']}, "
                  f"导入: {total_stats['total_imported']}, "
                  f"跳过: {total_stats['total_skipped']}, "
                  f"排除: {total_stats['total_excluded']}, "
                  f"错误: {total_stats['total_errors']}")
        print("=" * 80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("用户中断执行")
    except Exception as e:
        log_message(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
