<?php
include './auth.php';
$act=isset($get['act'])?daddslashes($get['act']):null;
switch($act){
    case 'addAgent':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$lastuid = $adminData['id'];
		$invite = addslashes($post['invite']);
		$fencheng = addslashes($post['fencheng']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
        if ($password=='') {
            exit('{"code":0,"msg":"密码不能为空"}');
        }
        if ($lastuid=='') {
            exit('{"code":0,"msg":"上级代理参数错误"}');
        }
        if ($invite=='') {
            exit('{"code":0,"msg":"邀请码不能为空"}');
        }
        if ($fencheng=='') {
            exit('{"code":0,"msg":"分成信息不能为空"}');
        }
        if ($fencheng < 0) {
            exit('{"code":0,"msg":"分成信息错误"}');
        }
		if( mb_strlen($username) < "6" ||  mb_strlen($username) > "18")exit('{"code":0,"msg":"添加失败,账号长度必须为6-18位!"}');
		if( mb_strlen($password) < "6" || mb_strlen($password) > "18" )exit('{"code":0,"msg":"添加失败,密码长度必须为6-18位!!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $username))exit('{"code":0,"msg":"添加失败,账号必须是大小写字母或者数字!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $password))exit('{"code":0,"msg":"添加失败,密码必须是大小写字母或者数字!"}');
		//if( mb_strlen($invite) < "1" ||  mb_strlen($invite) > "8")exit('{"code":0,"msg":"添加失败,邀请码必须长度必须为8位以内!"}');
		//if(!preg_match("/^[a-zA-Z0-9]*$/", $invite))exit('{"code":0,"msg":"添加失败,邀请码必须是大小写字母或者数字!"}');
		$adminData = $Admin->getAdmin($username);
        if(!empty($adminData))exit('{"code":0,"msg":"此账户已存在"}');
		$inviteData=$DB->getRow("SELECT * FROM `admin` WHERE `invite` ='" . $invite . "' limit 1");
        //if(!empty($inviteData))exit('{"code":0,"msg":"此邀请码已存在"}');
		$salt = $Admin->salt($username,$password);
		$pass = md5($password.$salt.$username);
		$lastuidData = $Admin->getAdminId($lastuid);
        if($fencheng >= $lastuidData['fencheng'])exit('{"code":0,"msg":"下级代理分成必须小于上级代理"}');
		$lastuids = explode(';',$lastuidData['lastuid']);
		$lastuidz = $lastuids[1].'-['.$lastuid.']';
		$lastuid = $lastuid.';'.$lastuidz;
		$agents = explode('-',$lastuidz);
		$lv = count($agents)-1;
		//exit('{"code":0,"msg":"'.$lastuid.'"}');
		$inviteData=$DB->getRow("SELECT * FROM `account` WHERE `username` ='" . $username . "' limit 1");
		
        if(empty($inviteData))exit('{"code":0,"msg":"该账号不存在无法升级为代理"}');
        
		$addAgent = $Admin->addAdmin($username,$pass,$salt,2,$lastuid,$fencheng,$username,$lv);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加代理，账号：".$username."，密码：".$password."', NOW(), '$ip', '$city')");
        
		
		exit('{"code":1,"msg":"添加成功<br>'.$username.'<br>'.$password.'"}');
    break;
    case 'editAgent':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$invite = addslashes($post['invite']);
		$fencheng = addslashes($post['fencheng']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
		$adminData = $Admin->getAdmin($username);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
        if($adminData['id']==1)exit('{"code":0,"msg":"总后台账号信息请在右上角头像处修改"}');
        if ($invite=='') {
            exit('{"code":0,"msg":"邀请码不能为空"}');
        }
        if ($fencheng=='') {
            exit('{"code":0,"msg":"分成信息不能为空"}');
        }
        if ($fencheng < 0) {
            exit('{"code":0,"msg":"分成信息错误"}');
        }
		if( mb_strlen($invite) < "1" ||  mb_strlen($invite) > "8")exit('{"code":0,"msg":"修改失败,邀请码必须长度必须为8位以内!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $invite))exit('{"code":0,"msg":"修改失败,邀请码必须是大小写字母或者数字!"}');
		$inviteData=$DB->getRow("SELECT * FROM `admin` WHERE `invite` ='" . $invite . "' limit 1");
        if(!empty($inviteData) && $inviteData['id'] != $adminData['id'] )exit('{"code":0,"msg":"此邀请码已存在"}');
		$lastuidData = $Admin->getAdmin($_SESSION['adminUser']);
        if($fencheng >= $lastuidData['fencheng'])exit('{"code":0,"msg":"下级代理分成必须小于'.$lastuidData['fencheng'].'%"}');
		if ($password=='') {
			$sql = "UPDATE `admin` SET `fencheng` = '$fencheng',`invite` = '$invite' WHERE `id` = '".$adminData['id']."'";
        }else{
		if( mb_strlen($password) < "6" || mb_strlen($password) > "18" )exit('{"code":0,"msg":"修改失败,密码长度必须为6-18位!!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $password))exit('{"code":0,"msg":"修改失败,密码必须是大小写字母或者数字!"}');
		$salt = $Admin->salt($username,$password);
		$pass = md5($password.$salt.$username);
		//exit('{"code":0,"msg":"'.$lastuid.'"}');
			$sql = "UPDATE `admin` SET `password` = '$pass',`salt` = '$salt',`fencheng` = '$fencheng',`invite` = '$invite' WHERE `id` = '".$adminData['id']."'";
		}
		$upAdminsql = $DB->exec($sql);
		if(!$upAdminsql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改代理信息，代理账号：".$username."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'chexiaozqsq':
		$id = addslashes($post['id']);
        if ($id=='') {
            exit('{"code":0,"msg":"信息有误"}');
        }
		$zqsqData=$DB->getRow("SELECT * FROM `zqsq_log` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zqsqData))exit('{"code":0,"msg":"此申请记录不存在"}');
        if($zqsqData['username'] != $_SESSION['adminUser'])exit('{"code":0,"msg":"此申请记录不属于你"}');
        if($zqsqData['status'] == 1 || $zqsqData['status'] == 2 || $zqsqData['status'] == 3)exit('{"code":0,"msg":"此申请记录已不可撤销"}');
		$sqla = "UPDATE `zqsq_log` SET `status` = '3' WHERE `id` = '".$id."'";
		$upzqsqsql = $DB->exec($sqla);
		if(!$upzqsqsql)exit('{"code":0,"msg":"撤销申请失败！"}');
		$sqlb = "UPDATE `admin` SET `money` = `money` + '".$zqsqData['shouxufei']."' WHERE `id` = '".$adminData['id']."'";
		$upadminsql = $DB->exec($sqlb);
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','撤销转区申请记录，退还代理额度：".$zqsqData['shouxufei']."，申请记录ID：".$zqsqData['id']."',  '".$zqsqData['shouxufei']."', '".$adminData['money']."', '".($adminData['money']-$zqsqData['shouxufei'])."', '$ip', '$city', '$date', '$time')");
		exit('{"code":1,"msg":"撤销成功！"}');
    break;
    case 'zqsq':
		$oldroleid = addslashes($post['oldroleid']);
		$newroleid = addslashes($post['newroleid']);
		$fangan = addslashes($post['fangan']);
        if ($oldroleid=='' || $newroleid=='' || $fangan=='') {
            exit('{"code":0,"msg":"所有参数不能为空"}');
        }
        if ($oldroleid == $newroleid) {
            exit('{"code":0,"msg":"被转区角色和预转区角色不能相同"}');
        }
		$oldroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $oldroleid . "' limit 1");
        if(empty($oldroleData))exit('{"code":0,"msg":"被转区角色不存在"}');
		$newroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $newroleid . "' limit 1");
        if(empty($newroleData))exit('{"code":0,"msg":"预转区角色不存在"}');
		//老区角色累计
		$oldcharge = $oldroleData['charge'];
		//查询方案
		$fanganData=$DB->getRow("SELECT * FROM `zqfa` WHERE `id` ='" . $fangan . "' limit 1");
        if(empty($fanganData))exit('{"code":0,"msg":"转区方案不存在"}');
		//转区手续费-需扣除代理额度
		$sxfkc = $oldcharge*($fanganData['sxfkc']/100);
		//新的累计额度
		$newcharge = $oldcharge+($oldcharge*$fanganData['ljedzj']/100);
		if($adminData['money'] < $sxfkc){
			exit('{"code":0,"msg":"代理额度不足抵扣手续费，所需手续费为：'.$sxfkc.'"}');
		}else{
			$sql = "UPDATE `admin` SET `money` = `money` - '$sxfkc' WHERE `id` = '".$adminData['id']."'";
			$upAdminsql = $DB->exec($sql);
			$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','提交转区申请，扣除代理额度：".$sxfkc."',  '".(0 - $sxfkc)."', '".$adminData['money']."', '".($adminData['money'] - $sxfkc)."', '$ip', '$city', '$date', '$time')");
		}
		$DB->query("insert into `zqsq_log` (`username`,`oldroleid`,`newroleid`,`zhuanqufangan`,`shouxufei`,`newcharge`,`date`,`status`) values ('".$_SESSION['adminUser']."','".$oldroleid."','".$newroleid."','".$fangan."','".$sxfkc."','".$newcharge."',NOW(),'0')");
		exit('{"code":1,"msg":"提交转区申请成功，请等待管理员审核"}');
    break;
    case 'editmy':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$invite = addslashes($post['invite']);
        if ($username=='' ) {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
		$adminData = $Admin->getAdmin($username);
        if(empty($adminData))exit('{"code":0,"msg":"此账户信息不存在"}');
        if ($invite=='') {
            exit('{"code":0,"msg":"邀请码不能为空"}');
        }
		if( mb_strlen($invite) < "1" ||  mb_strlen($invite) > "8")exit('{"code":0,"msg":"修改失败,邀请码必须长度必须为8位以内!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $invite))exit('{"code":0,"msg":"修改失败,邀请码必须是大小写字母或者数字!"}');
		$inviteData=$DB->getRow("SELECT * FROM `admin` WHERE `invite` ='" . $invite . "' limit 1");
        if(!empty($inviteData) && $inviteData['id'] != $adminData['id'] )exit('{"code":0,"msg":"此邀请码已存在"}');
		if ($adminData['username'] != $username) {
			if($password==''){
				exit('{"code":0,"msg":"修改账号必须同时重设密码"}');
			}else{
			$salt = $Admin->salt($username,$password);
			$pass = md5($password.$salt.$username);
			$sql = "UPDATE `admin` SET `username` = '$username',`password` = '$pass',`salt` = '$salt',`invite` = '$invite' WHERE `id` = '".$adminData['id']."'";
			}
        }else{
			if($password==''){
				$sql = "UPDATE `admin` SET `invite` = '$invite' WHERE `id` = '".$adminData['id']."'";
			}else{
			$salt = $Admin->salt($username,$password);
			$pass = md5($password.$salt.$username);
			$sql = "UPDATE `admin` SET `username` = '$username',`password` = '$pass',`salt` = '$salt',`invite` = '$invite' WHERE `id` = '".$adminData['id']."'";
			}
		}
		$upAdminsql = $DB->exec($sql);
		if(!$upAdminsql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改总后台信息，新的账号：".$username."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'payusermoney':
		$roleid = addslashes($post['roleid']);
		$money = addslashes($post['money']);
        if ($money=='' || $money <= 0) {
            exit('{"code":0,"msg":"充值金额不能为空"}');
        }
		$bindData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $roleid . "'  limit 1");
		$userData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $bindData['userid'] . "'  limit 1");
		if(!$bindData)exit('{"code":0,"msg":"该玩家尚未绑定任何角色"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"该大区信息不存在"}');
		$ptbbili = $serverData['ptb'];
		$vipbili = $serverData['vip'];
		$xianyubili = $serverData['xianyu'];
		$chargemoney = $serverData['charge'] * $money;
		if($userData['agentid']==$adminData['id']){ 
			if($adminData['money'] <  $money){
				exit('{"code":0,"msg":"可用额度不足"}');
			}else{
				$sql = "UPDATE `admin` SET `money` = `money`-'$money' WHERE `id` = '".$adminData['id']."'";
				$upUagentsql = $DB->exec($sql);
				if(!$upUagentsql){
					exit('{"code":0,"msg":"额度扣除失败"}');
				}else{
					$flag = ":";
					$flag1 = "\\";
					$flag2 = 'export LANG="zh_CN.UTF-8" && ';
					//检测今日累充
					$strtotime = strtotime("now"); 
					$date = date('Y-m-d',$strtotime);
					if($bindData['lastday']==$date){
						$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
					}else{
						$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
					}
					$ptb = $money * $ptbbili;
					$chargeupsql = "UPDATE `binds` SET `money`=`money`+'$ptb',`charge`=`charge`+'$chargemoney'".$sql." WHERE `id`='".$bindData['id']."' ";
					$chargeupsql = $DB->exec($chargeupsql) ;
					$ptbinfo = ',增加平台币：'.$ptb;
					//赠送VIP经验
					if($vipbili != 0){
					$vipnum = $money * $vipbili;
					$cmd = $flag2 . 'java -jar ../user/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $bindData['roleid'] . '" "addvipexp#' . $vipnum . '"';
					exec($cmd, $out);
					if(!success_cmd($out)){
						$vipinfo = ',未增加VIP经验：'.$vipnum;
					}else{
						$vipinfo = ',已增加VIP经验：'.$vipnum;
					}
					}else{
						$vipinfo = '';
					}
					//赠送仙玉
					if($xianyubili != 0){
						$xianyunum = $money * $xianyubili;
						$cmd = $flag2 . 'java -jar ../static/api/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $bindData['roleid'] . '" "addqian#3 ' . $xianyunum . '"';
						exec($cmd, $out);
					if(!success_cmd($out)){
						$xianyuinfo = ',未增加仙玉：'.$xianyunum;
					}else{
						$xianyuinfo = ',已增加仙玉：'.$xianyunum;
					}
					}else{
						$xianyuinfo = '';
					}
					$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','人工充值".$ptbinfo.$vipinfo.$xianyuinfo."', NOW(), '".$ip."', '".$city."')");
					$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为玩家人工充值，玩家账号：".$userData['username']."，充值金额：".$money."',  '".(0 - $money)."', '".$adminData['money']."', '".($adminData['money']-$money)."', '$ip', '$city', '$date', '$time')");
						exit('{"code":1,"msg":"充值成功！"}');
				}
			}
		}else{
			exit('{"code":0,"msg":"该玩家不属于你"}');
		}
    break;
    case 'payuservip':
		$roleid = addslashes($post['roleid']);
		$vipid = addslashes($post['vipid']);
		$bindData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $roleid . "'  limit 1");
		$userData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $bindData['userid'] . "'  limit 1");
		if(!$bindData)exit('{"code":0,"msg":"该玩家尚未绑定任何角色"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"该大区信息不存在"}');
		$strtotime = strtotime("now"); 
		$date = date('Y-m-d',$strtotime);
		if($userData['agentid']==$adminData['id']){ 
          if ( $vipid==1 ) {
            //周卡
			$zhouka=$DB->getRow("select * from `config` where `keys`='zhouka' limit 1");
			if($adminData['money'] <  $zhouka['values'] || $adminData['money']-$zhouka['values'] < 0){
				exit('{"code":0,"msg":"可用额度不足"}');
			}else{
				$money = $zhouka['values'];
				$chargemoney = $serverData['charge'] * $money;
				$sql = "UPDATE `admin` SET `money` = `money`-'".$money."' WHERE `id` = '".$adminData['id']."'";
				$upUagentsql = $DB->exec($sql);
				if(!$upUagentsql){
					exit('{"code":0,"msg":"额度扣除失败"}');
				}
				if($bindData['zk'] < $strtotime){
					$zhoukatime =$strtotime+(86400*7);
					$sqls = " , `zk`='$zhoukatime' ";
				}else{
					$zk =	604800;
					$sqls = " , `zk`=`zk`+'$zk'";
				}
					$vipname = '周卡';
			}
          }else if( $vipid==2 ){
            //月卡
			$yueka=$DB->getRow("select * from `config` where `keys`='yueka' limit 1");
			if($adminData['money'] <  $yueka['values'] || $adminData['money']-$yueka['values'] < 0){
				exit('{"code":0,"msg":"可用额度不足"}');
			}else{
				$money = $yueka['values'];
				$chargemoney = $serverData['charge'] * $money;
				$sql = "UPDATE `admin` SET `money` = `money`-'".$money."' WHERE `id` = '".$adminData['id']."'";
				$upUagentsql = $DB->exec($sql);
				if(!$upUagentsql){
					exit('{"code":0,"msg":"额度扣除失败"}');
				}
				if($bindData['yk'] < $strtotime){
					$yuekatime =$strtotime+(86400*30);
					$sqls = " , `yk`='$yuekatime' ";
				}else{
					$yk =	2592000;
					$sqls = " , `yk`=`yk`+'$yk'";
				}
				$vipname = '月卡';
		    }
		  }
		//检测今日累充
		if($bindData['lastday']==$date){
			$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
		}else{
			$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
		}
		$chargeupsql = "UPDATE `binds` SET `charge`=`charge`+'$chargemoney'".$sql.$sqls." WHERE `id`='".$bindData['id']."' ";
		$chargeupsql = $DB->exec($chargeupsql) ;

		$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','人工开通/续费VIP，VIP名称：".$vipname."', NOW(), '".$ip."', '".$city."')");
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为玩家人工开通/续费VIP，VIP名称：".$vipname."，玩家账号：".$userData['username']."',  '".(0 - $money)."', '".$adminData['money']."', '".($adminData['money']-$money)."', '$ip', '$city', '$date', '$time')");
		exit('{"code":1,"msg":"开通/续费成功！"}');
	
	}else{
			exit('{"code":0,"msg":"该玩家不属于你"}');
	}
    break;
    case 'payrmbshop':
		$roleid = addslashes($post['roleid']);
		$rmbshopid = addslashes($post['rmbshopid']);
		$number = addslashes($post['number']);
        if ($rmbshopid=='' ) {
            exit('{"code":0,"msg":"充值物品不能为空"}');
        }
        if ($number=='' ) {
            exit('{"code":0,"msg":"发送数量不能为空"}');
        }
		$bindData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $roleid . "'  limit 1");
		$userData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $bindData['userid'] . "'  limit 1");
		if(!$bindData)exit('{"code":0,"msg":"该玩家尚未绑定任何角色"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"该大区信息不存在"}');
		$rmbshopidData = $DB->query("SELECT * FROM `rmbshops` WHERE `id` = '".$rmbshopid."' ")->fetch();
		if(!$rmbshopidData)exit('{"code":0,"msg":"该商品不存在信息不存在"}');
		if($userData['agentid']==$adminData['id']){ 
			$kcmoney = $rmbshopidData['price']*$number;
			if($adminData['money'] < $kcmoney || $adminData['money']-$kcmoney < 0){
				exit('{"code":0,"msg":"可用额度不足"}');
			}else{
				$sql = "UPDATE `admin` SET `money` = `money`-'".$kcmoney."' WHERE `id` = '".$adminData['id']."'";
				$upUagentsql = $DB->exec($sql);
				if(!$upUagentsql && $rmbshopidData['price'] != 0 ){
					exit('{"code":0,"msg":"额度扣除失败"}');
				}else{
					$chargemoney = $serverData['charge'] * $kcmoney;
					//检测今日累充
					$strtotime = strtotime("now"); 
					$date = date('Y-m-d',$strtotime);
					if($bindData['lastday']==$date){
						$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
					}else{
						$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
					}
					$chargeupsql = "UPDATE `binds` SET `charge`=`charge`+'$chargemoney'".$sql." WHERE `id`='".$bindData['id']."' ";
					$chargeupsql = $DB->exec($chargeupsql) ;
					for($i=1; $i <= $number; $i++){
						$DB->query("insert into `bindsbag` (`bindsid`,`name`,`image`,`value`,`status`,`data`,`info`) values ('".$bindData['id']."','".$rmbshopidData['name']."','".$rmbshopidData['image']."','".$rmbshopidData['itemid'].';'.$rmbshopidData['num']."', '0', NOW(), '现金商城' )");
					}
					}
					
					$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','人工充值现金商城物品，物品名称：".$rmbshopidData['name']."，发放次数：".$number."', NOW(), '".$ip."', '".$city."')");
					$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为玩家人工充值现金商城物品，玩家账号：".$userData['username']."，充值物品：".$rmbshopidData['name']."，充值数量：".$number."',  '".(0 - $kcmoney)."', '".$adminData['money']."', '".($adminData['money']-$kcmoney)."', '$ip', '$city', '$date', '$time')");
					exit('{"code":1,"msg":"充值成功！"}');
			}
		}else{
			exit('{"code":0,"msg":"该玩家不属于你"}');
		}
    break;
    case 'paymoney':
		$agentid = addslashes($post['agentid']);
		$money = addslashes($post['money']);
        if ($money=='' || $money <= 0) {
            exit('{"code":0,"msg":"充值金额不能为空"}');
        }
		$agentData=$DB->getRow("SELECT * FROM `admin` WHERE `id` ='" . $agentid . "'  limit 1");
		if(strpos($agentData['lastuid'],'['.$adminData['id'].']') !== false){ 
			if($adminData['money'] <  $money){
				exit('{"code":0,"msg":"可用额度不足"}');
			}else{
				$sql = "UPDATE `admin` SET `money` = `money`-'$money' WHERE `id` = '".$adminData['id']."'";
				$upUagentsql = $DB->exec($sql);
				if(!$upUagentsql){
					exit('{"code":0,"msg":"额度扣除失败"}');
				}else{
				$sqlA = "UPDATE `admin` SET `money` = `money`+'$money' WHERE `id` = '".$agentid."'";
				$upUagentssql = $DB->exec($sqlA);
					if(!$upUagentssql){
						exit('{"code":0,"msg":"额度充值失败，请联系管理员"}');
					}else{
						$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为代理充值额度，代理账号：".$agentData['username']."，充值额度：".$money."',  '".(0 - $money)."', '".$adminData['money']."', '".($adminData['money']-$money)."', '$ip', '$city', '$date', '$time')");
						exit('{"code":1,"msg":"充值成功！"}');
					}
				}
			}
		}else{
			exit('{"code":0,"msg":"该代理不属于你"}');
		}
    break;
    case 'fjAgent':
		$id = intval($post['id']);
		$adminData = $Admin->getAdminId($id);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
		$sql = "UPDATE `admin` SET `status`='0' WHERE `id` = '".$id."'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"封禁失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','封禁代理，账号：".$adminData['username']."，邀请码：".$adminData['invite']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"封禁成功！"}');
		}
    break;
    case 'jfAgent':
		$id = intval($post['id']);
		$adminData = $Admin->getAdminId($id);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
		$sql = "UPDATE `admin` SET `status`='1' WHERE `id` = '".$id."'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"解封失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','解除代理封禁，账号：".$adminData['username']."，邀请码：".$adminData['invite']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"解封成功！"}');
		}
    break;
    default:
        exit('{"code":-4,"msg":"No Act"}');
    break;
}
?>