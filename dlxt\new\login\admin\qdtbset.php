<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<!-- 加载 下拉图片 -->
<link rel="stylesheet" type="text/css" href="/static/admin/image-picker/image-picker.css">
<script type="text/javascript" src="/static/admin/image-picker/image-picker.js"></script>
<style>
 .container {
  height: 60px;
  width: 90px;
  margin: 0 auto;
} 
 </style>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='edit'){
	$id = intval($get['id']);
	$tubiaoData=$DB->getRow("SELECT * FROM `tubiao` WHERE `id` ='" . $id . "' limit 1");
	if(!$tubiaoData){echo "<script>layer.ready(function(){layer.msg('该选项不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return edit(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>名称</label>
              <input type="text" class="form-control"  name="name" value="<?php echo $tubiaoData['name'] ;?>" placeholder="请输入分类名称" readonly />
              <input type="text" class="form-control"  name="id" value="<?php echo $tubiaoData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="value" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					if($res['value'] == $tubiaoData['value']){ 
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'" selected = "selected" >'.$res['value'].'</option>';
					}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
					}
					
				
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function edit(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editqdtb',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>