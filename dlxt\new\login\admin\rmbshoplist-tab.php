<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		}
	}else{
	$sql=" 1";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" 1";
}
$numrows=$DB->getColumn("SELECT count(*) from rmbshops WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>商品名称</th>
					<th>物品ID</th>
					<th>物品数量</th>
					<th>所属分类</th>
					<th>图标</th>
					<th>介绍</th>
					<th>状态</th>
					<th>价格</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM rmbshops WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
		$typesData=$DB->getRow("SELECT * FROM `types` WHERE `id` ='" . $res['itemtype'] . "' limit 1");
		if($res['status']==1){
			$status = '<span class="label label-brown">上架</span>';
		}else{
			$status = '<span class="label label-brown">下架</span>';
		}
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['name'].'</td>
<td>'.$res['itemid'].'</td>
<td>'.$res['num'].'</td>
<td><span class="label label-info">'.$typesData['name'].'</span></td>
<td><img src="http://'.$_SERVER['HTTP_HOST'].$res['image'].'" style="width:60px;height:60px;" /></td>
<td>'.$res['info'].'</td>
<td>'.$status.'</td>
<td><span class="label label-dark">'.$res['price'].'元</span></td>
<td>
<a href="./shopset.php?act=editrmbshop&id='.$res['id'].'" class="btn btn-w-xs btn-warning">编辑</a>&nbsp;
<a href="javascript:delateshop('.$res['id'].')" class="btn btn-w-xs btn-danger">删除</a>
</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
