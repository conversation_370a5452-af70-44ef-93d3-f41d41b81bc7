<?php
// 调试支付流程
include "config.php";
require_once('mobile_pay_handler.php');

echo "<h2>支付流程调试工具</h2>";

// 模拟支付参数
$type = 'wxpay';
$out_trade_no = 'DEBUG_FLOW_' . time();
$money = '1';
$name = 'DEBUG测试商品';
$notify_url = 'http://************:168/pay/notify_url.php';
$return_url = 'http://************:168/pay/return_url.php';
$ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';

// 检测设备类型
function getDeviceType() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);

    if (strpos($userAgent, 'micromessenger') !== false) {
        return 'wechat';  // 微信内浏览器
    } elseif (strpos($userAgent, 'qq/') !== false) {
        return 'qq';      // 手机QQ内浏览器
    } elseif (strpos($userAgent, 'alipayclient') !== false) {
        return 'alipay';  // 支付宝客户端
    } elseif (preg_match('/(mobile|android|iphone|ipad|ipod)/i', $userAgent)) {
        return 'mobile';  // 手机浏览器
    } else {
        return 'pc';      // 电脑浏览器
    }
}

$device = getDeviceType();

echo "<h3>环境信息：</h3>";
echo "<p><strong>User Agent:</strong> " . htmlspecialchars($_SERVER['HTTP_USER_AGENT']) . "</p>";
echo "<p><strong>检测到的设备类型:</strong> {$device}</p>";
echo "<p><strong>客户端IP:</strong> {$ip}</p>";

echo "<h3>支付参数：</h3>";
echo "<p><strong>支付类型:</strong> {$type}</p>";
echo "<p><strong>订单号:</strong> {$out_trade_no}</p>";
echo "<p><strong>金额:</strong> ¥{$money}</p>";

// 根据设备类型选择支付方式
if ($device === 'pc') {
    echo "<h3>🖥️ 走PC端逻辑</h3>";
    echo "<p>使用 submit.php 端点，调用 generateMobilePayPage()</p>";
    
    // PC端使用页面跳转支付
    $parameter = array(
        "pid" => $epay_config['pid'],
        "type" => $type,
        "notify_url" => $notify_url,
        "return_url" => $return_url,
        "out_trade_no" => $out_trade_no,
        "name" => $name,
        "money" => $money,
    );

    // 计算签名
    ksort($parameter);
    $signstr = '';
    foreach($parameter as $k => $v){
        if($k != "sign" && $k != "sign_type" && $v!=''){
            $signstr .= $k.'='.$v.'&';
        }
    }
    $signstr = substr($signstr,0,-1);
    $signstr .= $epay_config['key'];
    $sign = md5($signstr);

    $parameter['sign'] = $sign;
    $parameter['sign_type'] = 'MD5';

    // 构建支付URL
    $payUrl = $epay_config['apiurl'].'submit.php?'.http_build_query($parameter);
    
    echo "<p><strong>支付URL:</strong> " . htmlspecialchars($payUrl) . "</p>";

    // 订单信息
    $orderInfo = array(
        'out_trade_no' => $out_trade_no,
        'money' => $money,
        'name' => $name,
        'type' => $type
    );

    echo "<h4>调用 generateMobilePayPage() 的结果：</h4>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
    echo generateMobilePayPage($payUrl, $type, $orderInfo);
    echo "</div>";

} else {
    echo "<h3>📱 走移动端API逻辑</h3>";
    echo "<p>使用 mapi.php 端点，根据API返回结果处理</p>";
    
    // 移动端使用API接口支付
    $parameter = array(
        "pid" => $epay_config['pid'],
        "type" => $type,
        "notify_url" => $notify_url,
        "return_url" => $return_url,
        "out_trade_no" => $out_trade_no,
        "name" => $name,
        "money" => $money,
        "clientip" => $ip,
        "device" => $device,
    );

    // 计算签名
    ksort($parameter);
    $signstr = '';
    foreach($parameter as $k => $v){
        if($k != "sign" && $k != "sign_type" && $v!=''){
            $signstr .= $k.'='.$v.'&';
        }
    }
    $signstr = substr($signstr,0,-1);
    $signstr .= $epay_config['key'];
    $sign = md5($signstr);

    $parameter['sign'] = $sign;
    $parameter['sign_type'] = 'MD5';

    echo "<h4>API请求参数：</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    foreach($parameter as $key => $value) {
        echo "<tr><td>{$key}</td><td>{$value}</td></tr>";
    }
    echo "</table>";

    // 调用API接口
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $epay_config['apiurl'].'mapi.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($parameter));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);
    
    echo "<h4>API返回结果：</h4>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

    if ($result && $result['code'] == 1) {
        // 订单信息
        $orderInfo = array(
            'out_trade_no' => $out_trade_no,
            'money' => $money,
            'name' => $name,
            'type' => $type,
            'trade_no' => $result['trade_no'] ?? ''
        );

        echo "<h4>处理逻辑分析：</h4>";
        
        // 根据返回结果智能处理支付
        if (isset($result['payurl'])) {
            echo "<p>✅ 检测到 payurl 字段</p>";
            $payUrl = $result['payurl'];

            // 微信支付统一使用二维码方式，避免跳转问题
            if ($type === 'wxpay') {
                echo "<p>🔍 微信支付，调用 generateQRCodePage()</p>";
                echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
                echo generateQRCodePage($payUrl, $out_trade_no, $money, $name);
                echo "</div>";
            } else {
                echo "<p>🔍 其他支付方式</p>";
            }

        } elseif (isset($result['qrcode'])) {
            echo "<p>✅ 检测到 qrcode 字段，调用 generateQRCodePage()</p>";
            echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
            echo generateQRCodePage($result['qrcode'], $out_trade_no, $money, $name);
            echo "</div>";

        } elseif (isset($result['urlscheme'])) {
            echo "<p>✅ 检测到 urlscheme 字段</p>";
            echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
            echo generateMobileOptimizedPage($result['urlscheme'], $type, $orderInfo);
            echo "</div>";

        } else {
            echo "<p>❌ 未找到有效的支付字段</p>";
        }

    } else {
        echo "<p>❌ API调用失败</p>";
        echo "<p>错误信息：" . ($result['msg'] ?? '未知错误') . "</p>";
    }
}

echo "<br><a href='test.php'>返回测试页面</a>";
?>
