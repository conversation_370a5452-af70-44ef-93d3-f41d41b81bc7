<?php
//支付接口地址
$epay_config['apiurl']  = 'https://sheng.nachengweb.com/';
//商户ID
$epay_config['pid']  	= '1093';
//商户KEY
$epay_config['key']		= '72jJHMDjjYBqMmZjMjrR7mbt27Zu3Mjy';

//充值页面公告内容
$czgg = ' 本服充值没有，只有支持服务器的一个维护。全靠自愿。本服不做盈利。 ';

//商品信息
//（ 商品名称，商品介绍，人民币价格，到账仙玉数，图片位置/图片名称，支付类型）
$goods_info = array(
    '1' => array('服务器自愿支持', '共计可领取', '6', '80', 'fa-gift','xy'),
    '2' => array('服务器自愿支持', '共计可领取', '30', '450', 'fa-star','cj10'),
    '3' => array('服务器自愿支持', '共计可领取', '2', '20', 'fa-heart','xy'),
    '4' => array('服务器自愿支持', '共计可领取', '1', '10', 'fa-diamond','cj10'),
    '5' => array('服务器自愿支持', '共计可领取', '500', '500', 'fa-crown','cj50'),
    '6' => array('服务器自愿支持', '共计可领取', '0.1', '1', 'fa-coins','xy'),
);

//数据库信息
header("content-type:text/html;charset=utf-8");
$dbconfig=array(
	'host' =>  '127.0.0.1',
    'port' =>  '3306',
    'user' =>  'root',
    'dbname' =>  'daili',
    'pwd' =>  'gch19871004',
    'dbqz' =>  ''
);
$dbdlconfig=array(
	'host' =>  '127.0.0.1',
    'port' =>  '3306',
    'user' =>  'root',
    'dbname' =>  'daili',
    'pwd' =>  'gch19871004',
    'dbqz' =>  ''
);

include_once("./autoloader.php");
Autoloader::register();

try {
	$DB = new PDO("mysql:host={$dbconfig['host']};dbname={$dbconfig['dbname']};port={$dbconfig['port']}",$dbconfig['user'],$dbconfig['pwd']);
}catch(Exception $e){
	exit('数据库链接失败！');
}

try {
	$DBDL = new PDO("mysql:host={$dbdlconfig['host']};dbname={$dbdlconfig['dbname']};port={$dbdlconfig['port']}",$dbdlconfig['user'],$dbdlconfig['pwd']);
}catch(Exception $e){
	exit('数据库链接失败！');
}

$DB = new \lib\pdoHelper($dbconfig);
$DBDL = new \lib\pdoHelper($dbdlconfig);

//不需修改
$alipay_config['sign_type']    = strtoupper('MD5');
$alipay_config['input_charset']= strtolower('utf-8');

foreach ((array)$_GET as $get_key=>$get_var)
{
    if (is_numeric($get_var)) {
        $get[$get_key] = get_int($get_var);
    } else {
        $get[$get_key] = get_str($get_var);
    }
}
/* 过滤所有POST过来的变量 */
foreach ((array)$_POST as $post_key=>$post_var)
{
    if (is_numeric($post_var)) {
        $post[$post_key] = get_int($post_var);
    } else {
        $post[$post_key] = get_str($post_var);
    }
}
/* 过滤函数 */
//整型过滤函数
function get_int($number)
{
    return intval($number);
}
//字符串型过滤函数
function get_str($string)
{
    // PHP 7.4+ 中 magic_quotes_gpc 已被移除，直接使用 addslashes
    return addslashes($string);
}
?>
