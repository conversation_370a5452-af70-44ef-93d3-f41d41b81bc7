* {
	margin: 0;
	padding: 0;
}

.pay {
	width: 800px;
	margin: 150px auto;
	background: #ccc;
	border: 1px solid #e5e5e5;
	border-radius: 4px;
}

p{
	margin: 0;
}

a,
a:hover,
a:focus {
	color: #666;
}

.tr_recharge {
	max-width: 800px;
	background: #fff;
	border-radius: 4px;
	overflow: hidden;
}

.tr_rechtext {
	width: 100%;
	padding: 10px;
	background: #27b0d6;
	color: #fff;
}

.te_retit {
	font-size: 20px;
	font-weight: bold;
	line-height: 40px;
}

.te_retit img {
	float: left;
	margin-top: 5px;
	margin-right: 5px;
}

.tr_rechtext p {
	margin-bottom: 10px;
}

.tr_rechbox {
	padding: 20px;
	border-bottom: 1px solid #ededed;
}

.tr_rechhead {
	width: 100%;
	background: rgba(39, 176, 214, .15);
	padding: 20px;
	display: inline-block;
	float: left;
	margin-bottom: 40px
}

.tr_rechhead>img {
	width: 46px;
	height: 46px;
	border-radius: 50%;
	float: left;
	margin-right: 10px;
}

.tr_rechhead>p {
	line-height: 46px;
	float: left;
	color: #666;
}

.tr_rechhead p a {
	color: rgb(39, 176, 214);
}

.tr_rechheadcion {
	float: right;
	display: inline-block;
}

.tr_rechheadcion img {
	width: 20px;
	float: left;
	margin-top: 13px;
	margin-right: 5px;
}

.tr_rechheadcion span {
	color: #565656;
	line-height: 46px;
}

.tr_rechli ul li {
	float: left;
	border: 1px solid #d7d7d7;
	color: #555;
	font-size: 18px;
	padding: 15px 30px;
	margin-right: 30px;
	border-radius: 4px;
}

.tr_rechli>span {
	color: rgb(39, 176, 214);
}

.tr_rechcho {
	margin-bottom: 20px;
	display: inline-block;
}

.tr_rechcho img {
	height: 50px;
	padding: 10px;
	border: 1px solid #e5e5e5;
}

.tr_rechcho label {
	display: inline-block;
	margin-right: 30px;
}

.tr_rechli {
	display: inline-block;
	margin-bottom: 40px;
}

.tr_rechcho>span {
	color: #666;
	float: left;
	line-height: 50px;
	margin-right: 10px;
}

.tr_rechnum {
	height: 40px;
}

.tr_rechnum>span {
	line-height: 40px;
	margin-right: 10px;
}

.rechnum {
	color: #ec5a5b;
	font-size: 24px;
	font-weight: bold;
	display: inline-block;
	line-height: 40px;
}

.tr_rechoth {
	display: none;
	height: 50px;
	margin-bottom: 40px;
}

.tr_rechoth>span {
	float: left;
	line-height: 50px;
	color: #666;
	margin-right: 10px;
}

.tr_pay {
	padding: 12px 30px;
	background: #e5595c;
	border-radius: 10px;
	border: none;
	color: #fff;
	font-weight: bold;
	font-size: 16px;
	outline: 0;
}

.tr_pay:hover {
	color: #fff;
}

.tr_paybox {
	padding: 20px;
}

.tr_paybox span {
	color: #999;
	margin-left: 30px;
}

select.ui-choose {
	display: none;
}

ul.ui-choose {
	box-sizing: border-box;
	display: inline-block;
	border: 1px solid transparent;
	font: 100 16px/30px 'microsoft yahei';
	border-right: none;
	border-bottom: none;
	font-size: 16px;
	margin: 0;
	padding: 0;
	list-style: none;
	vertical-align: middle;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

ul.ui-choose:after {
	content: "";
	display: table;
	clear: both;
}

ul.ui-choose>li {
	width: 96px;
	height: 50px;
	display: block;
	margin: 0;
	padding: 0;
	font-size: 16px;
	line-height: 48px;
	color: #999;
	background: #fff;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	cursor: pointer;
	margin-right: 30px;
	text-align: center;
	border: 2px solid #CCCCCC;
}

ul.ui-choose>li:hover {
	z-index: 4;
	border-color: #27b0d6;
	color: #27b0d6;
}

ul.ui-choose>li.selected {
	z-index: 3;
	border-color: #27b0d6;
	color: #27b0d6;
	background: transparent url("../images/success1.png") no-repeat right bottom;
}

ul.ui-choose>li.disabled {
	z-index: 2;
	background-color: #f2f2f2;
	color: #aaa;
	cursor: not-allowed;
}

ul.ui-choose>li.disabled:hover {
	border-color: #aaa;
}

ul.ui-choose>li.selected.disabled {
	z-index: 2;
	background-color: #8FC7FF;
	color: #fff;
	cursor: not-allowed;
	border-color: #8FC7FF;
}

ul.choose-type-right>li.selected {
	color: #0080ff;
	background: transparent url("./icon-selected.png") no-repeat right bottom;
}

ul.ui-choose.choose-flex {
	display: flex;
}

ul.ui-choose.choose-flex>li {
	flex: 1;
	padding: 0 8px;
}

.tr_prompt {
	padding: 4px 10px;
	background: rgba(0, 0, 0, .3);
	color: #fff;
	font-size: 14px;
	display: inline-block;
}

.tr_rechoth p {
	display: inline-block;
	margin-left: 30px;
	color: #27b0d6;
	line-height: 40px;
}

.am-checkbox+.am-checkbox,
.am-radio+.am-radio {
	margin: auto;
}

.am-radio .am-icon-unchecked,
.am-radio .am-icon-checked {
	top: 13px;
}

.am-radio {
	margin: 0;
}

.am-form {
	width: 100%;
}

.am-form input[type=number] {
	width: 100px;
	background: #fff;
	border: 2px solid #CCCCCC;
	font-size: 16px;
	height: 48px;
	border-radius: 4px;
	float: left;
	margin-right: 30px;
}

.am-ucheck-checkbox:checked+.am-ucheck-icons,
.am-ucheck-radio:checked+.am-ucheck-icons {
	color: #27b0d6;
}

.am-form input[type=number]:focus {
	border-color: #27b0d6;
}

.am-form-group label {
	width: 100%;
	text-align: center;
	position: relative;
	color: #666;
}

.tr_rechli label{
	margin-top: -5px;
}

.am-radio-inline {
	padding-left: 0;
}

.am-radio-inline input[type=radio] {
	margin: 0;
	opacity: 0;
	width: 100%;
	height: 46px;
	position: absolute;
}

.am-form-group .am-alert {
	float: left;
	width: auto;
	margin: auto;
	border-radius: 4px;
	line-height: 48px;
	padding: 0;
	color: #ec5a5b;
	background: none;
	border: none;
}

.am-form-error .am-radio-inline,
.am-form-error label {
	color: #666;
}

.am-form-success .am-radio-inline,
.am-form-success label {
	color: #666;
}

.am-field-valid {
	border-color: #ccc !important;
}

.tr_rechcho label {
	width: auto;
	display: inline-block;
	float: left;
}

.am-form-success [class*=icon-] {
	color: #27b0d6;
}

.am-form-error [class*=icon-] {
	color: #999;
}

.am-form-group>label>span {
	color: #999;
}