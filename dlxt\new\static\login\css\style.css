
/* reset */
html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,dl,dt,dd,ol,nav ul,nav li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}
article, aside, details, figcaption, figure,footer, header, hgroup, menu, nav, section {display: block;}
ol,ul{list-style:none;margin:0px;padding:0px;}
blockquote,q{quotes:none;}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}
table{border-collapse:collapse;border-spacing:0;}
/* start editing from here */
a{text-decoration:none;}
.txt-rt{text-align:right;}/* text align right */
.txt-lt{text-align:left;}/* text align left */
.txt-center{text-align:center;}/* text align center */
.float-rt{float:right;}/* float right */
.float-lt{float:left;}/* float left */
.clear{clear:both;}/* clear float */
.pos-relative{position:relative;}/* Position Relative */
.pos-absolute{position:absolute;}/* Position Absolute */
.vertical-base{	vertical-align:baseline;}/* vertical align baseline */
.vertical-top{	vertical-align:top;}/* vertical align top */
nav.vertical ul li{	display:block;}/* vertical menu */
nav.horizontal ul li{	display: inline-block;}/* horizontal menu */
img{max-width:100%;}
/*end reset*/
/****-----start-body----****/
body {
font-family:Verdana;
	    background: url(../images/bg.jpg)0px 0px no-repeat;
   background-size:cover;

}
body a{
	transition: 0.1s all;
	-webkit-transition: 0.1s all;
	-moz-transition: 0.1s all;
	-o-transition: 0.1s all;
}
.app-block{
  width:28%;
  margin:0 auto;
  text-align: center;

  padding: 3em 5em;
  height: 80%;
}
form {
  padding: 3% 3% 7% 3%;
  border-bottom: 1px solid #999;
}
/*-----*/
.cube img {
  margin: 2em 0;
}
.app-block input[type="text"],.app-block input[type="password"]{
  width: 81.2%;
  padding: 1.25em 1em 1.25em 4em;
  color: #fff;
  font-size: 16px;
  outline: none;
  font-weight: 500;
  border: none;
  
  background: url("../images/icons.png") no-repeat 14px 20px #1e1e1d;
  margin:0.8em 0;
  }
.app-block input[type="password"]{
   background: url("../images/icons.png") no-repeat 13px -53px #1e1e1d;
  }
.submit {
  margin: 1em 0;
}
.app-block p.sign {
  padding-top: 3em;
}
.app-block input[type="submit"]{
  font-size: 20px;
  font-weight: 300;
  color: #fff;
  cursor: pointer;
  outline: none;
  padding:20px 20px;
  width:100%;
  border:none;
  moz-transition: all 0.3s;
  -o-transition: all 0.3s;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  background:green;
  border-radius: 2em;
  -webkit-border-radius: 2em;
  -o-border-radius: 2em;
  -moz-border-radius: 2em;
  border: 3px solid green
}
input[type="submit"]:hover{
  color:#EACB20;
  background: none;
  border: 3px dotted #EACB20;
}
.app-block p a,.app-block p.sign{
  color:#AFAFB0;
  font-weight: 400;
  font-size: 1em;
}
.app-block p{
  padding-top: 1em;
}
.app-block p a:hover,.app-block p.sign a:hover{
  text-decoration: underline;
    color: #EACB20;
}

/*-----start-responsive-design------*/
@media (max-width:1440px){
  .app-block{
    width:30%;
  }
  .app-block input[type="text"],.app-block input[type="password"]{
    width: 80.2%;
  	
  }
}
@media (max-width:1366px){
  .app-block{
    width: 30%;
  }
.app-block input[type="text"], .app-block input[type="password"] {
    width: 79.7%;
  }
}
@media (max-width:1280px){
  .app-block {
  width: 35%;
  }
  .app-block input[type="text"], .app-block input[type="password"] {
  width: 80.7%;
  }
  
}
@media (max-width:1024px){
  .app-block {
  width: 47%;
  }
  .app-block input[type="text"], .app-block input[type="password"] {
  width: 82.7%;
  }
 
}
@media (max-width:768px){
  .cam img {
  margin: 2em 0;
}
.app-block {
  width: 54%;
    margin: 0 auto 0 12%;
}
.copy-right {
  padding: 1em 1em;
}
.app-block input[type="text"], .app-block input[type="password"] {
  width:79.7%;
  }
 
}
@media (max-width:640px){
.app-block input[type="text"], .app-block input[type="password"] {
    width: 79.8%;
}
.app-block {
  width: 65%;
  margin: 0 auto 0 4.5%;
}
}
@media (max-width:480px){
  .copy-right p {
    font-size: 0.9em;
  }
  .new h3 a, .new h4 a {
  font-size: 0.9em;
  }
  form {
  width: 94%;
  margin: 0 auto;
  }
  .app-block input[type="submit"] {
  font-size: 18px;
  padding: 14px 15px;
  }
 .app-block {
  width: 68%;
  margin: 0 auto 0 2.2%;
  padding: 1em 4em 2em 4em;
}
 .app-block input[type="text"], .app-block input[type="password"] {
  width: 72.8%;
  background: url("../images/icons.png") no-repeat 16px 18px #1e1e1d;
  background-size: 7%;
  padding: 1.1em 1em 1.1em 4em;
  margin-top: 1px;
  font-size: 14px;
}
.app-block input[type="password"] {
  background: url("../images/icons.png") no-repeat 16px -28px #1e1e1d;
  background-size: 7%;
  }
  .buttons {
  margin: 0em 0 1em 0;
  padding: 3em 0em 1em 0em;
}
.block h2 {
  padding: 0.8em 0;
  font-size: 1.4em;
}
.app-block p a, .app-block p.sign a {
  font-size: 0.9em;
}
.app-block p {
  padding-top: 0em;
}
.app-block p.sign {
  padding: 2em 0 2em 0;
}
}
@media (max-width:320px){
.app-block {
  width: 96.5%;
  padding: 0em 0em 1em 0em;
  margin: 0 auto 0 2%;
}
.app-block p a, .app-block p.sign {
  font-size: 0.9em;
}
  body h1 {
  font-size: 1.7em;
}
.copy-right p {
  font-size: 0.8em;
  line-height: 1.7em;
}
.app-block p.sign {
  padding: 1em 0 1em 0;
}
.submit {
  margin: 0.5em 0;
}
.app-block input[type="submit"] {
  font-size: 18px;
  padding: 10px 10px;
}
.app-block p {
  padding-top: 0.5em;
}
 .app-block input[type="text"], .app-block input[type="password"] {
  width: 74.8%;
  background: url("../images/icons.png") no-repeat 15px 16px #1e1e1d;
  background-size: 8%;
  padding: 1em 1em 1em 4em;
  margin-top: 1px;
  font-size: 13px;
}
.app-block input[type="password"] {
  background: url("../images/icons.png") no-repeat 16px -28px #1e1e1d;
  background-size: 8%;
  }
form {
    width: 92%;
  margin: 0 auto;
}
.cube img {
    width: 27%;
  }
}
