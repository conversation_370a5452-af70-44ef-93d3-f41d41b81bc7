body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,legend,button,form,fieldset,input,textarea,p,blockquote,th,td{padding:0;margin:0;}
q:before,q:after{content:'';} 
fieldset,img,abbr,acronym{border:0 none;}
abbr,acronym{font-variant:normal;}
legend{color:#000;}
address,caption,cite,code,dfn,em,strong,th,var{font-weight:normal;font-style:normal;}
sup{vertical-align:text-top;}
sub{vertical-align:text-bottom;}
table{border-collapse:collapse;border-spacing:0;} 
caption,th{text-align:left;}
input,img,select{vertical-align:middle;}
ol,ul{list-style:none;}  
input,button,textarea,select,optgroup,option{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;}
h1,h2,h3,h4,h5,h6{font-weight:normal;font-size:100%;} 
del,ins,a{text-decoration:none;}
a:link{}
a:visited{}
input[type="submit"]{cursor:pointer;}
.content{width: 100%;float: left;}
button{cursor:pointer;}
input::-moz-focus-inner{border:0;padding:0;}
.clear{clear:both;}
.aaa,.aaaa{ border-radius: 5px; display: block;width: 30%;float: left;margin: 1% 0.5%;text-align: center;height: 2.5rem;line-height: 2.5rem;border: 1px solid #3369ff;color: #3369ff;}
.aaamoney{border-radius: 5px;border: none;width:100%;text-align: center;line-height: 2.5rem;}
.text-center{width: 80%;margin: 0 auto;}


.game_name{border-radius: 5px;height: 2.5rem;text-indent: 0.5rem;width: 100%;border: solid #627bff 1px;background: #fff;margin: 10px 0px;line-height: 2.5rem;}
/*.serverlist{border-radius: 5px;text-indent: 0.5rem;height: 2.5rem;border: solid #627bff 1px;background-color: rgba(255, 255, 255, 0.0);font-weight: 800;line-height: 2.5rem;}*/
.bottom{float: left;width: 100%;}
.bottom p{height:3rem;height: 3rem;width: 45%;float: left;background: #f8f8f8;margin: 10px 0px 10px 13px;line-height: 3rem;border-radius: 5px;}
.wxpay{background: url(/pay/img/wx.png)  50% 50% no-repeat;}
.alipay{ background: url(/pay/img/zfb.png)  40% 50% no-repeat;}
.bottom button{height: 3rem;width: 100%;border-radius: 5px;}
input:-webkit-autofill {-webkit-box-shadow: 0 0 0px 1000px white inset !important;}
#autoBox{display:none; border: solid #627bff 1px;border-radius: 5px;}
#autoBox li{ border-bottom: dashed  #627bff 1px;  margin: 2% 5%;    font-size: 1.1rem;height: 1.5rem;font-weight: 800;line-height: 1.5rem;}
#autoBox li span{float: right;color:#ff4141;}
form {width: 100%;height: 100%;float: left;}
.footer{float: left;margin: 0 auto;height: 3rem;width: 100%;
}	
.footer p{text-align:center;margin-top:5%;}
.footer a{color: #f97474;}

.background-image-box {
    width: 100%;
    height: 500px;
    background-image: url(/pay/img/beijing.png);
    background-size: cover;
    background-position: center;
    border-radius: 8px;
}