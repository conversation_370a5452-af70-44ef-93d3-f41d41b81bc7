<?php

include './common/main.php';
$i = 0;
$z = 0;
$rs=$DB->query("SELECT * FROM `mhxy`.`account` where `synchro` = '0' ");

while($res = $rs->fetch())
{
	$checkAccount = $DB->query("SELECT * FROM `new`.`account` WHERE `username` = '".$res['account_id']."' ")->fetch();
    if($checkAccount['id'] == ""){
        $username = $res['account_id'];
        $password = $res['password'];
       	$salt = $Admin->salt($username,$password);
		$pass = md5($salt.$password.$username);
		$invitation_code = $res['invitation_code'];
		$dlassy = $DB->query("SELECT * FROM `new`.`admin` WHERE `username` = '".$invitation_code."' ")->fetch();
		$dlid = $dlassy['id'];
		$ip = $res['create_ip'];
		$city = $Gets->get_city($ip);
		//写入数据库
		$addUser = $Admin->addUser($username,$pass,$salt,$dlid,$ip,$city);
		//$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $username . "','成功创建账号，账号为：".$username."，密码为：".$password."', NOW(), '".$ip."', '".$city."')");
        $iplist = "UPDATE `mhxy`.`account` SET `synchro` = '1' WHERE `account_id` = '".$res['account_id']."' ";
		$iplists = $DB->exec($iplist);
        //exit('{"code":"1","msg":"注册成功"}');
			$i++;
	}
	
$z++;
}
echo  '共查询：'.$z.'次，成功导入数据：'.$i.'次<br>';
exit('success');
?>