<?php
// 端口检测页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端口检测</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .info-card {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #fff;
        }
        .status-ok {
            border-left-color: #28a745;
            background: rgba(40,167,69,0.2);
        }
        .status-info {
            border-left-color: #17a2b8;
            background: rgba(23,162,184,0.2);
        }
        h1, h2 {
            margin-top: 0;
        }
        .url-list {
            list-style: none;
            padding: 0;
        }
        .url-list li {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .url-list a {
            color: #fff;
            text-decoration: none;
            font-weight: bold;
        }
        .url-list a:hover {
            text-decoration: underline;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 端口检测与配置</h1>
        
        <div class="info-card status-ok">
            <h2>✅ 当前访问信息</h2>
            <p><strong>协议：</strong><?php echo isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'HTTPS' : 'HTTP'; ?></p>
            <p><strong>主机：</strong><?php echo $_SERVER['HTTP_HOST']; ?></p>
            <p><strong>端口：</strong><?php echo $_SERVER['SERVER_PORT']; ?></p>
            <p><strong>完整URL：</strong><?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?></p>
        </div>

        <div class="info-card status-info">
            <h2>🔗 自动生成的回调URL</h2>
            <?php
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $base_url = $protocol . '://' . $host;
            ?>
            <p><strong>异步回调：</strong><a href="<?php echo $base_url; ?>/pay_notify.php" target="_blank"><?php echo $base_url; ?>/pay_notify.php</a></p>
            <p><strong>同步回调：</strong><a href="<?php echo $base_url; ?>/pay_return.php" target="_blank"><?php echo $base_url; ?>/pay_return.php</a></p>
        </div>

        <div class="info-card">
            <h2>🚀 功能页面链接</h2>
            <ul class="url-list">
                <li>🏠 <a href="<?php echo $base_url; ?>/">网站首页</a></li>
                <li>💳 <a href="<?php echo $base_url; ?>/chongzhi.php">游戏充值</a></li>
                <li>👤 <a href="<?php echo $base_url; ?>/login.php">用户登录</a></li>
                <li>📝 <a href="<?php echo $base_url; ?>/reg.php">用户注册</a></li>
                <li>⚙️ <a href="<?php echo $base_url; ?>/login/admin/">管理后台</a></li>
                <li>🧪 <a href="<?php echo $base_url; ?>/pay_test.php">支付测试</a></li>
            </ul>
        </div>

        <div class="info-card">
            <h2>📋 支持的端口配置</h2>
            <h3>方式1：168端口</h3>
            <p>访问地址：<code>http://121.41.9.212:168/</code></p>
            
            <h3>方式2：默认80端口</h3>
            <p>访问地址：<code>http://121.41.9.212/</code></p>
            
            <h3>方式3：HTTPS 443端口</h3>
            <p>访问地址：<code>https://121.41.9.212/</code></p>
            
            <h3>方式4：自定义域名</h3>
            <p>访问地址：<code>http://yourdomain.com/</code></p>
        </div>

        <div class="info-card status-info">
            <h2>💡 配置说明</h2>
            <p>✅ <strong>自动端口适配：</strong>系统会自动检测当前访问的端口和协议</p>
            <p>✅ <strong>回调URL自动生成：</strong>支付回调URL根据当前域名端口自动生成</p>
            <p>✅ <strong>无需修改代码：</strong>更换端口只需修改Web服务器配置</p>
            <p>✅ <strong>支持HTTPS：</strong>自动检测SSL协议</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="<?php echo $base_url; ?>/" class="btn">🏠 返回首页</a>
            <a href="<?php echo $base_url; ?>/chongzhi.php" class="btn">💳 开始充值</a>
            <a href="<?php echo $base_url; ?>/pay_test.php" class="btn">🧪 系统测试</a>
        </div>
    </div>
</body>
</html>
