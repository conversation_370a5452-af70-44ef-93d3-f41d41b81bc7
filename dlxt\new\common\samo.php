<?php
/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-05-29 13:29:29
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-29 16:03:35
 * @FilePath: \dlxt\new\common\samo.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

foreach ((array)$_GET as $get_key=>$get_var)
{
    if (is_numeric($get_var)) {
        $get[$get_key] = get_int($get_var);
    } else {
        $get[$get_key] = get_str($get_var);
    }
}
/* 过滤所有POST过来的变量 */
foreach ((array)$_POST as $post_key=>$post_var)
{
    if (is_numeric($post_var)) {
        $post[$post_key] = get_int($post_var);
    } else {
        $post[$post_key] = get_str($post_var);
    }
}
/* 过滤函数 */
//整型过滤函数
function get_int($number)
{
    return intval($number);
}
//字符串型过滤函数
function get_str($string)
{
    return addslashes($string);
}


?>