<?php
include('./auth.php');
//月卡
$yueka=$DB->getRow("select * from `config` where `keys`='yueka' limit 1");
//周卡
$zhouka=$DB->getRow("select * from `config` where `keys`='zhouka' limit 1");

?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>月卡/周卡配置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>价格设置</legend>
            <div class="form-group">
              <div class="col-xs-6">
              <label>周卡价格</label>
				<div class="input-group m-b-10">
					<input type="number" name="zhouka" class="form-control" value="<?php echo $zhouka['values']; ?>" placeholder="请输入周卡价格"">
					<span class="input-group-addon" >元</span>
				</div>
                <div class="help-block">例如：100</div>
              </div>
              <div class="col-xs-6">
              <label>月卡价格</label>
				<div class="input-group m-b-10">
					<input type="number" name="yueka" class="form-control" value="<?php echo $yueka['values']; ?>" placeholder="请输入月卡价格">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">例如：800</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=vipset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>