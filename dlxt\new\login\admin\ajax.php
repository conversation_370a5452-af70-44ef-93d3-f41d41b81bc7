<?php
/*
本后台只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
----
*/
include './auth.php';
$act=isset($get['act'])?daddslashes($get['act']):null;
switch($act){
    case 'addAgent':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$lastuid = addslashes($post['lastuid']);
		
		$fencheng = addslashes($post['fencheng']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
        if ($password=='') {
            exit('{"code":0,"msg":"密码不能为空"}');
        }
        if ($lastuid=='') {
            exit('{"code":0,"msg":"上级代理参数错误"}');
        }
       
        if ($fencheng=='') {
            exit('{"code":0,"msg":"分成信息不能为空"}');
        }
        if ($fencheng < 0) {
            exit('{"code":0,"msg":"分成信息错误"}');
        }
		if( mb_strlen($username) < "6" ||  mb_strlen($username) > "18")exit('{"code":0,"msg":"添加失败,账号长度必须为6-18位!"}');
		if( mb_strlen($password) < "6" || mb_strlen($password) > "18" )exit('{"code":0,"msg":"添加失败,密码长度必须为6-18位!!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $username))exit('{"code":0,"msg":"添加失败,账号必须是大小写字母或者数字!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $password))exit('{"code":0,"msg":"添加失败,密码必须是大小写字母或者数字!"}');
		
		$adminData = $Admin->getAdmin($username);
        if(!empty($adminData))exit('{"code":0,"msg":"此账户已存在"}');
	
		$salt = $Admin->salt($username,$password);
		$pass = md5($password.$salt.$username);
		$lastuidData = $Admin->getAdminId($lastuid);
        if($fencheng >= $lastuidData['fencheng'])exit('{"code":0,"msg":"下级代理分成必须小于上级代理"}');
		$lastuids = explode(';',$lastuidData['lastuid']);
		$lastuidz = $lastuids[1].'-['.$lastuid.']';
		$lastuid = $lastuid.';'.$lastuidz;
		$agents = explode('-',$lastuidz);
		$lv = count($agents)-1;
		//exit('{"code":0,"msg":"'.$lastuid.'"}');
		$inviteData=$DB->getRow("SELECT * FROM `account` WHERE `username` ='" . $username . "' limit 1");
       
        if(empty($inviteData))exit('{"code":0,"msg":"该账号不存在无法升级为代理"}');
        
		$addAgent = $Admin->addAdmin($username,$pass,$salt,2,$lastuid,$fencheng,$username,$lv);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加代理，账号：".$username."，密码：".$password."', NOW(), '$ip', '$city')");
        
        
        
        //$DB->query("INSERT INTO `mhxy`.`account` (`account_id`, `password`, `create_ip`) VALUES ('$username', '$password', '$ip')");
        
        
		exit('{"code":1,"msg":"添加成功<br>'.$username.'<br>'.$password.'"}');
    break;
    case 'editAgent':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$lastuid = addslashes($post['lastuid']);
		
		$fencheng = addslashes($post['fencheng']);
		$money = addslashes($post['money']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
		$adminData = $Admin->getAdmin($username);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
        if($adminData['id']==1)exit('{"code":0,"msg":"总后台账号信息请在右上角头像处修改"}');
        if ($lastuid=='') {
            exit('{"code":0,"msg":"上级代理参数错误"}');
        }
       
        if ($fencheng=='') {
            exit('{"code":0,"msg":"分成信息不能为空"}');
        }
        if ($fencheng < 0) {
            exit('{"code":0,"msg":"分成信息错误"}');
        }
        if ($money=='') {
            exit('{"code":0,"msg":"代理额度不能为空"}');
        }
        if ($money < 0) {
            exit('{"code":0,"msg":"代理额度参数信息错误"}');
        }
	
		$lastuidData = $Admin->getAdminId($lastuid);
        if($fencheng >= $lastuidData['fencheng'])exit('{"code":0,"msg":"下级代理分成必须小于上级代理"}');
		$lastuids = explode(';',$lastuidData['lastuid']);
		$lastuidz = $lastuids[1].'-['.$lastuid.']';
		$lastuid = $lastuid.';'.$lastuidz;
		$agents = explode('-',$lastuidz);
		$lv = count($agents)-1;
		if ($password=='') {
			$sql = "UPDATE `admin` SET `money` = '$money',`fencheng` = '$fencheng',`lastuid` = '$lastuid',`lv` = '$lv' WHERE `id` = '".$adminData['id']."'";
        }else{
		if( mb_strlen($password) < "6" || mb_strlen($password) > "18" )exit('{"code":0,"msg":"修改失败,密码长度必须为6-18位!!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $password))exit('{"code":0,"msg":"修改失败,密码必须是大小写字母或者数字!"}');
		$salt = $Admin->salt($username,$password);
		$pass = md5($password.$salt.$username);
		//exit('{"code":0,"msg":"'.$lastuid.'"}');
			$sql = "UPDATE `admin` SET `money` = '$money',`password` = '$pass',`salt` = '$salt',`fencheng` = '$fencheng',`lastuid` = '$lastuid',`lv` = '$lv' WHERE `id` = '".$adminData['id']."'";
		}
		$upAdminsql = $DB->exec($sql);
		if(!$upAdminsql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改代理信息，代理账号：".$username."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'editmy':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$invite = addslashes($post['invite']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
		$adminData = $Admin->getAdminId(1);
        if(empty($adminData))exit('{"code":0,"msg":"此账户信息不存在"}');
       
		
		if($username !=$adminData ['username'] && $password==''){
			exit('{"code":0,"msg":"修改账号必须同时修改密码"}');
		}
		if($password==''){
				$sql = "UPDATE `admin` SET `invite` = '$invite' WHERE `id` = '1'";
		}else{
			$salt = $Admin->salt($username,$password);
			$pass = md5($password.$salt.$username);
			$sql = "UPDATE `admin` SET `password` = '$pass',`username` = '$username',`salt` = '$salt',`invite` = '$invite' WHERE `id` = '1'";
		}
		$upAdminsql = $DB->exec($sql);
		if(!$upAdminsql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改账户信息', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
     case 'qdconfig':
	
		$qdmin = addslashes($_POST['qdmin']);
		$qdmax = addslashes($_POST['qdmax']);
        if ($qdmin==NULL  || $qdmax==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
	
		$qdmin = "UPDATE `config` SET `values` = '$qdmin' WHERE `keys` = 'qdmin' ";
		$qdmax = "UPDATE `config` SET `values` = '$qdmax' WHERE `keys` = 'qdmax' ";
	
		$qdmin = $DB->exec($qdmin);
		$qdmax = $DB->exec($qdmax);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改签到设置', NOW(), '$ip', '$city')");
    break;
    case 'edituser':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
		$status = addslashes($post['status']);
		$agentid = addslashes($post['agentid']);
        if ($username=='') {
            exit('{"code":0,"msg":"账号不能为空"}');
        }
		$userData = $Admin->getUser($username);
        if(empty($userData))exit('{"code":0,"msg":"此玩家账号不存在"}');
        if ($status=='') {
            exit('{"code":0,"msg":"请选择账号状态"}');
        }
        if ($agentid=='') {
            exit('{"code":0,"msg":"上级代理不能为空"}');
        }
		if ($password=='') {
			$sql = "UPDATE `account` SET `status` = '$status',`agentid` = '$agentid' WHERE `id` = '".$userData['id']."'";
        }else{
		if( mb_strlen($password) < "6" || mb_strlen($password) > "18" )exit('{"code":0,"msg":"修改失败,密码长度必须为6-18位!!"}');
		if(!preg_match("/^[a-zA-Z0-9]*$/", $password))exit('{"code":0,"msg":"修改失败,密码必须是大小写字母或者数字!"}');
		$salt = $Admin->salt($username,$password);
		$pass = md5($salt.$password.$username);
		//exit('{"code":0,"msg":"'.$lastuid.'"}');
			$sql = "UPDATE `account` SET `password` = '$pass',`salt` = '$salt',`agentid` = '$agentid',`status` = '$status' WHERE `id` = '".$userData['id']."'";
		}
		$upUsersql = $DB->exec($sql);
		if(!$upUsersql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改玩家账号信息，玩家账号：".$username."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'gm':
		$gmtype = addslashes($get['gmtype']);
		$flag = ":";
		$flag1 = "\\";
		$flag2 = 'export LANG="zh_CN.UTF-8" && ';
        $serverid = addslashes($post['serverid']);
        $serverData=$DB->getRow("select * from `servers` where id='$serverid' limit 1");
        $port = $serverData['port'];
		$caozuo = addslashes($post['caozuo']);
        $uid = addslashes($post['roleid']);
        $num = addslashes($post['number']);
		if ($gmtype==1){ 
			if($caozuo==1){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "nonvoice ' . $uid . ' 64000000 GM 0"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "禁言成功！";
				$caozuoinfo = "【禁言】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "unnonvoice ' . $uid . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "解除禁言成功！";
				$caozuoinfo = "【解除禁言】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==3){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "coquest"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "跳过主线成功！";
				$caozuoinfo = "【跳过主线】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==4){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "dismissguild"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "解散公会成功！";
				$caozuoinfo = "【解散公会】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==5){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "clearbag"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "清理背包成功！";
				$caozuoinfo = "【清理背包】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==6){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=9845" "roleId=' . $uid . '" "forbid#' . $uid . '#999999#1"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "封号成功！";
				$caozuoinfo = "【封号】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==7){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "unforbid ' . $uid . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "解除封号成功！";
				$caozuoinfo = "【解除封号】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==8){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=12289" "createrole 1"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "开启创建角色成功！";
				$caozuoinfo = "【开启创建角色】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==9){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=12289" "createrole 0"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "禁止创建角色成功！";
				$caozuoinfo = "【禁止创建角色】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==10){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "kick ' . $uid . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "强制下线成功！";
				$caozuoinfo = "【强制下线】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==11){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "baitantimeclear"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "清除摆摊公示成功！";
				$caozuoinfo = "【清除摆摊公示】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==12){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "checkcode  1"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "开启手机验证成功！";
				$caozuoinfo = "【开启手机验证】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==13){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '"  "hideme"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "开启隐身加速成功！";
				$caozuoinfo = "【开启隐身加速】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==14){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '"  "showme"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "取消隐身加速成功！";
				$caozuoinfo = "【取消隐身加速】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==15){
				$cmd = $flag2 . 'java -classpath .' . $flag . '../../static/api/gsxdb.jar Clients ' . $port . ' ' . $uid . ' updateshield';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "更新关键词成功！";
				$caozuoinfo = "【更新关键词】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==16){
				$cmd = $flag2 . 'java -classpath .' . $flag . '../../static/api/gsxdb.jar Clients ' . $port . ' ' . $uid . ' updatenotclear';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "更新清包过滤成功！";
				$caozuoinfo = "【更新清包过滤】角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}
		}else if ($gmtype==2){
			if($caozuo==1){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addlevel#' . $num . ' "';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "增加等级成功！";
				$caozuoinfo = "【充值等级】数量：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "subfushi ' . $uid . ' ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "减少仙玉成功！";
				$caozuoinfo = "【减少仙玉】数量：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==3){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addqian#3 ' . $num . '"';
				exec($cmd, $out);
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addvipexp#' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "充值仙玉成功！";
				$caozuoinfo = "【充值仙玉】数量：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==4){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addgold#' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "充值金币成功！";
				$caozuoinfo = "【充值金币】数量：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==5){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addbanggong#' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "充值帮贡成功！";
				$caozuoinfo = "【充值帮贡】数量：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==6){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '"  "changebindtel ' . $uid . ' ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "关联手机成功！";
				$caozuoinfo = "【关联手机】手机号码：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}
		}else if ($gmtype==3){
			$skill = $post["petskillid"];
			if($caozuo==1){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "addpetskill ' . $skill . ' 1 1 "';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "增加技能成功！";
				$caozuoinfo = "【增加技能】技能id：".$skill."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "delpetskill ' . $skill . ' 1 1 "';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "减少技能成功！";
				$caozuoinfo = "【减少技能】技能id：".$skill."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}	
		}elseif($gmtype==4){
				$petid = $post["petid"];
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '"  "addpet ' . $petid . ' ' . $num . ' ' . $uid . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送宠物成功！";
				$caozuoinfo = "【发送宠物】宠物id：".$petid."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
		}elseif($gmtype==5){
			if($caozuo==1){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetgrow ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改成长资质成功！";
				$caozuoinfo = "【修改成长资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetattack ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改攻击资质成功！";
				$caozuoinfo = "【修改攻击资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==3){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetdefend ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改防御资质成功！";
				$caozuoinfo = "【修改速度资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==4){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetmagic ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改法术资质成功！";
				$caozuoinfo = "【修改法术资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==5){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetphyforce ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改体质资质成功！";
				$caozuoinfo = "【修改体质资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==6){
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "setpetspeed ' . $num . '"';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "修改速度资质成功！";
				$caozuoinfo = "【修改速度资质】数值：".$num."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}
		}elseif($gmtype==6){
				$itemid = $post["itemid"];
				$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $port . '" "gm" "userId=4096" "roleId=' . $uid . '" "additem ' . $itemid . ' ' . $num . '" ';
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送物品成功！";
				$caozuoinfo = "【发送物品】物品id：".$itemid."角色id：".$uid;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
		}elseif($gmtype==7){
			$info = $post["info"];
			if($caozuo==1){
				$locale = 'en_US.UTF-8';
				setlocale(LC_ALL, $locale);
				putenv('LC_ALL=' . $locale);
				$cmd = $flag2 . "java -classpath ." . $flag . "../../static/api/gsxdb.jar Clients " . $port . " " . $uid . " zmd#" . $info;
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送公告成功！";
				$caozuoinfo = "【发送公告】公告内容：".$info;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$gmcmd = str_replace(" ", "#", $info);
				$cmd = $flag2 . 'java -classpath .' . $flag . '../../static/api/gsxdb.jar Clients ' . $port . ' ' . $uid . ' ' . $gmcmd;
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送CMD命令成功！";
				$caozuoinfo = "【CMD命令】命令内容：".$info;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==3){
				$gmcmd = str_replace(" ", "#", $info);
				$cmd = $flag2 . 'java -classpath .' . $flag . '../../static/api/gsxdb.jar Clients ' . $port . ' ' . $uid . ' tc#0#'.$gmcmd;
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送个人弹窗成功！";
				$caozuoinfo = "【个人弹窗】弹窗内容：".$info;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==4){
				$gmcmd = str_replace(" ", "#", $info);
				$cmd = $flag2 . 'java -classpath .' . $flag . '../../static/api/gsxdb.jar Clients ' . $port . ' ' . $uid . ' tc#1#'.$gmcmd;
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "发送全服弹窗成功！";
				$caozuoinfo = "【全服弹窗】弹窗内容：".$info;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}
		}elseif($gmtype==8){
				$zhuangbei = $post['zhuangbei'];
				$teji = $post['teji'];
				$texiao = $post['texiao'];
				$taozhuang = $post['taozhuang'];
				$str = 'addsequip'.'#'.$zhuangbei.'#'.$teji.'#'.$texiao.'#'.$taozhuang;
				$gmcmd = str_replace(" ", "#", $str);
				$cmd = $flag2 . "java -classpath ." . $flag . "../../static/api/gsxdb.jar Clients " . $port . " " . $uid ." ". $gmcmd;
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "定制装备成功！";
				$caozuoinfo = "【定制装备】装备id：".$itemid."，装备内容".$str;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
		}elseif($gmtype==9){
				$times = $post["times"];
				$mailct = $post["info"];
				$levelmin = $post["min"];
				$levelmax = $post["max"];
				$itemid = $post["itemid"];
				if($mailct==null)exit('{"code":0,"msg":"邮件内容不能为空！"}');
				if($levelmin==null)exit('{"code":0,"msg":"最低领取等级不能为空！"}');
				if($levelmax==null)exit('{"code":0,"msg":"最高领取等级不能为空！"}');
				if($itemid==null)exit('{"code":0,"msg":"奖励物品不能为空！"}');
				if($times==null && $times != 0)exit('{"code":0,"msg":"持续时间不能为空！"}');
				$cmd = $flag2 . "java -classpath ." . $flag . "../../static/api/gsxdb.jar Clients " . $port . " " . $uid . " mailbycond#GM#" . $mailct . "#" . $times . "#" . $itemid . $flag1 . "|" . $num . "#1" . $flag1 . "|" . $levelmin . $flag1 . "|" . $levelmax;
				//$cmd=$flag2.'java -cp ../../static/api/jmxc.jar "" "" "127.0.0.1" "'.$port.'" "gm" "userId=4096" "roleId='.$uid.'" "mailbycond GM 全服邮件 0 '.$itemid.'|'.$num.' 1|10|100" ';
				////mailbycond 邮件标题 邮件内容 有效时间默认填0 奖励多个用逗号1|100,2|100 领取邮件的条件1|10|100
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "全服邮件发送成功！";
				$caozuoinfo = "【全服邮件】物品id：".$itemid."，邮件内容".$mailct;
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
		}elseif($gmtype==10){
			if($caozuo==1){
				$locale = 'en_US.UTF-8';
				setlocale(LC_ALL, $locale);
				putenv('LC_ALL=' . $locale);
				$cmd = $flag2 . "java -classpath ." . $flag . "../../static/api/gsxdb.jar Clients " . $port . " " . $uid . " reload";
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "更新表格成功！";
				$caozuoinfo = "【不停服更新表格】";
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}elseif($caozuo==2){
				$gmcmd = str_replace(" ", "#", $info);
				$cmd = $flag2 . "java -classpath ." . $flag . "../../static/api/gsxdb.jar Clients " . $port . " " . $uid . " stopgs 60";
				exec($cmd, $out);
				if(success_cmd($out)){
				$tishi = "服务器关闭成功！";
				$caozuoinfo = "【友好关闭服务器】";
				}else{
				exit('{"code":0,"msg":"操作失败！"}');
				}
			}
		}else{
            exit('{"code":0,"msg":"参数异常"}');
        }
		$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','$caozuoinfo', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"'.$tishi.'"}');
    break;
    case 'jjzqsq':
		$id = addslashes($post['id']);
        if ($id=='') {
            exit('{"code":0,"msg":"信息有误"}');
        }
		$zqsqData=$DB->getRow("SELECT * FROM `zqsq_log` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zqsqData))exit('{"code":0,"msg":"此申请记录不存在"}');
        if($zqsqData['status'] == 1 || $zqsqData['status'] == 2 || $zqsqData['status'] == 3)exit('{"code":0,"msg":"此申请记录已不可操作"}');
		$sqla = "UPDATE `zqsq_log` SET `status` = '2' WHERE `id` = '".$id."'";
		$upzqsqsql = $DB->exec($sqla);
		if(!$upzqsqsql)exit('{"code":0,"msg":"修改申请记录状态失败！"}');
		$zqsqdlData=$DB->getRow("SELECT * FROM `admin` WHERE `username` = '".$zqsqData['username']."' limit 1");
		$sqlb = "UPDATE `admin` SET `money` = `money` + '".$zqsqData['shouxufei']."' WHERE `username` = '".$zqsqData['username']."'";
		$upadminsql = $DB->exec($sqlb);
		
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$zqsqData['username']."','系统拒绝您的转区申请，退还代理额度：".$zqsqData['shouxufei']."，申请记录ID：".$zqsqData['id']."',  '".$zqsqData['shouxufei']."', '".$zqsqdlData['money']."', '".($zqsqdlData['money']+$zqsqData['shouxufei'])."', '$ip', '$city', '$date', '$time')");
		$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','拒绝（".$zqsqData['username']."）提交的转区申请记录，退还代理额度：".$zqsqData['shouxufei']."，申请记录ID：".$zqsqData['id']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"拒绝成功！"}');
    break;
    case 'payvip':
		$roleid = addslashes($post['roleid']);
		$vipid = addslashes($post['vipid']);
		$bindData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $roleid . "'  limit 1");
		$userData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $bindData['userid'] . "'  limit 1");
		if(!$bindData)exit('{"code":0,"msg":"该玩家尚未绑定任何角色"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"该大区信息不存在"}');
		$strtotime = strtotime("now"); 
		$date = date('Y-m-d',$strtotime);
          if ( $vipid==1 ) {
            //普通周卡
			$zhouka=$DB->getRow("select * from `config` where `keys`='zhouka' limit 1");
				$money = $zhouka['values'];
				if($bindData['zk'] < $strtotime){
					$zhoukatime =$strtotime+(86400*7);
					$sqls = " , `zk`='$zhoukatime' ";
				}else{
					$zk =	604800;
					$sqls = " , `zk`=`zk`+'$zk'";
				}
					$vipname = '普通周卡';
          }else if( $vipid==2 ){
            //高级周卡
			$yueka=$DB->getRow("select * from `config` where `keys`='yueka' limit 1");
				$money = $yueka['values'];
				if($bindData['yk'] < $strtotime){
					$yuekatime =$strtotime+(86400*7);
					$sqls = " , `yk`='$yuekatime' ";
				}else{
					$yk =	604800;
					$sqls = " , `yk`=`yk`+'$yk'";
				}
				$vipname = '高级周卡';
		  }
		//检测今日累充
		$chargemoney = $serverData['charge'] * $money;
		if($bindData['lastday']==$date){
			$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
		}else{
			$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
		}
		$chargeupsql = "UPDATE `binds` SET `charge`=`charge`+'$chargemoney'".$sql.$sqls." WHERE `id`='".$bindData['id']."' ";
		$chargeupsql = $DB->exec($chargeupsql) ;

		$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','人工开通/续费VIP，VIP名称：".$vipname."', NOW(), '".$ip."', '".$city."')");
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为玩家人工开通/续费VIP，VIP名称：".$vipname."，玩家账号：".$userData['username']."',  '".(0 - $money)."', '".$adminData['money']."', '".($adminData['money']-$money)."', '$ip', '$city', '$date', '$time')");
		exit('{"code":1,"msg":"开通/续费成功！"}');
    break;
    case 'payusermoney':
		$roleid = addslashes($post['roleid']);
		$money = addslashes($post['money']);
        if ($money=='' || $money <= 0) {
            exit('{"code":0,"msg":"充值金额不能为空"}');
        }
		$bindData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $roleid . "'  limit 1");
		$userData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $bindData['userid'] . "'  limit 1");
		if(!$bindData)exit('{"code":0,"msg":"该玩家尚未绑定任何角色"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$bindData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"该大区信息不存在"}');
		$ptbbili = $serverData['ptb'];
		$vipbili = $serverData['vip'];
		$xianyubili = $serverData['xianyu'];
		$chargemoney = $serverData['charge'] * $money;
		$flag = ":";
		$flag1 = "\\";
		$flag2 = 'export LANG="zh_CN.UTF-8" && ';
		//检测今日累充
		$strtotime = strtotime("now"); 
		$date = date('Y-m-d',$strtotime);
		if($bindData['lastday']==$date){
			$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
		}else{
			$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
		}
		$ptb = $money * $ptbbili;
		$chargeupsql = "UPDATE `binds` SET `money`=`money`+'$ptb',`charge`=`charge`+'$chargemoney'".$sql." WHERE `id`='".$bindData['id']."' ";
		$chargeupsql = $DB->exec($chargeupsql) ;
		$ptbinfo = ',增加平台币：'.$ptb;
		//赠送VIP经验
		if($vipbili != 0){
			$vipnum = $money * $vipbili;
			$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $bindData['roleid'] . '" "addvipexp#' . $vipnum . '"';
			exec($cmd, $out);
			if(!success_cmd($out)){
				$vipinfo = ',未增加VIP经验：'.$vipnum;
			}else{
				$vipinfo = ',已增加VIP经验：'.$vipnum;
			}
		}else{
			$vipinfo = '';
		}
		//赠送仙玉
		if($xianyubili != 0){
			$xianyunum = $money * $xianyubili;
			$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $bindData['roleid'] . '" "addqian#3 ' . $xianyunum . '"';
			exec($cmd, $out);
			if(!success_cmd($out)){
				$xianyuinfo = ',未增加仙玉：'.$xianyunum;
			}else{
				$xianyuinfo = ',已增加仙玉：'.$xianyunum;
			}
		}else{
			$xianyuinfo = '';
		}
		$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','人工充值".$ptbinfo.$vipinfo.$xianyuinfo."', NOW(), '".$ip."', '".$city."')");
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$_SESSION['adminUser']."','为玩家人工充值，玩家账号：".$userData['username']."，充值金额：".$money."',  '".(0 - $money)."', '".$adminData['money']."', '".($adminData['money']-$money)."', '$ip', '$city', '$date', '$time')");
		exit('{"code":1,"msg":"充值成功！"}');
    break;
    case 'tgzqsq':
		$id = addslashes($post['id']);
        if ($id=='') {
            exit('{"code":0,"msg":"信息有误"}');
        }
		$zqsqData=$DB->getRow("SELECT * FROM `zqsq_log` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zqsqData))exit('{"code":0,"msg":"此申请记录不存在"}');
        if($zqsqData['status'] == 1 || $zqsqData['status'] == 2 || $zqsqData['status'] == 3)exit('{"code":0,"msg":"此申请记录已不可操作"}');
		$sqla = "UPDATE `zqsq_log` SET `status` = '1' WHERE `id` = '".$id."'";
		$upzqsqsql = $DB->exec($sqla);
		if(!$upzqsqsql)exit('{"code":0,"msg":"修改申请记录状态失败！"}');
		//角色查询
		$oldroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $zqsqData['oldroleid'] . "' limit 1");
        if(empty($oldroleData))exit('{"code":0,"msg":"被转区角色不存在"}');
		$oldserverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$oldroleData['serverid']."' ")->fetch();
		if(!$oldserverData)exit('{"code":0,"msg":"大区信息错误"}');
		$newroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $zqsqData['newroleid'] . "' limit 1");
        if(empty($newroleData))exit('{"code":0,"msg":"预转区角色不存在"}');
		$serverData = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$newroleData['serverid']."' ")->fetch();
		if(!$serverData)exit('{"code":0,"msg":"大区信息错误"}');
		$userData = $DB->query("SELECT * FROM `account` WHERE `id` = '".$newroleData['userid']."' ")->fetch();
		if(!$userData)exit('{"code":0,"msg":"账号不存在"}');
		//清空老角色
		$chargeupsqla = "UPDATE `binds` SET `money`='0',`charge`='0',`daycharge`='0' WHERE `id`='".$oldroleData['id']."' ";
		$chargeupsqla = $DB->exec($chargeupsqla) ;
		//强制下线
		$cmda = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $oldserverData['port'] . '" "gm" "userId=4096" "roleId=' . $oldroleData['roleid'] . '" "kick ' . $oldroleData['roleid'] . '"';
        exec($cmda, $out);
		if(success_cmd($out)){
			$a = '老角色强制下线成功';
		}
		//封禁账号
        $cmdb = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $oldserverData['port'] . '" "gm" "userId=9845" "roleId=' . $oldroleData['roleid'] . '" "forbid#' . $oldroleData['roleid'] . '#999999#1"';
		exec($cmdb, $out);
		if(success_cmd($out)){
			$b = '封禁老角色成功';
		}
		//新角色累计
		$money = $zqsqData['newcharge'];
		//操作模拟充值
		$flag = ":";
		$flag1 = "\\";
		$flag2 = 'export LANG="zh_CN.UTF-8" && ';
		//比例
		//仙玉比例
		//VIP经验比例
		$ptbbili = $serverData['ptb'];
		$vipbili = $serverData['vip'];
		$xianyubili = $serverData['xianyu'];
		$chargemoney = $serverData['charge'] * $money;
		//检测今日累充
		$strtotime = strtotime("now"); 
		$date = date('Y-m-d',$strtotime);
		if($newroleData['lastday']==$date){
			$sql = " , `daycharge`=`daycharge`+'$chargemoney'";
		}else{
			$sql = " , `daycharge`='$chargemoney', `lastday`='$date', `daylq`='[0]'";
		}
		$ptb = $money * $ptbbili;
		$chargeupsql = "UPDATE `binds` SET `money`=`money`+'$ptb',`charge`=`charge`+'$chargemoney'".$sql." WHERE `id`='".$newroleData['id']."' ";
		$chargeupsql = $DB->exec($chargeupsql) ;
		$ptbinfo = ',增加平台币：'.$ptb;
		//赠送VIP经验
		if($vipbili != 0){
			$vipnum = $money * $vipbili;
			$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $newroleData['roleid'] . '" "addvipexp#' . $vipnum . '"';
			exec($cmd, $out);
			if(!success_cmd($out)){
			$vipinfo = ',未增加VIP经验：'.$vipnum;
			}else{
			$vipinfo = ',已增加VIP经验：'.$vipnum;
			}
		}else{
			$vipinfo = '';
		}
		//赠送仙玉
		if($xianyubili != 0){
			$xianyunum = $money * $xianyubili;
			$cmd = $flag2 . 'java -jar ../../static/api/jmxc.jar "" "" "127.0.0.1" "' . $serverData['port'] . '" "gm" "userId=4096" "roleId=' . $newroleData['roleid'] . '" "addqian#3 ' . $xianyunum . '"';
			exec($cmd, $out);
			if(!success_cmd($out)){
			$xianyuinfo = ',未增加仙玉：'.$xianyunum;
			}else{
			$xianyuinfo = ',已增加仙玉：'.$xianyunum;
			}
		}else{
			$xianyuinfo = '';
		}
		$zqsqdlData=$DB->getRow("SELECT * FROM `admin` WHERE `username` = '".$zqsqData['username']."' limit 1");
		$DB->query("insert into `user_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $userData['username'] . "','角色转区，系统充值，增加角色累计、今日累计：".$money."元".$ptbinfo.$vipinfo.$xianyuinfo."', NOW(), '".$ip."', '".$city."')");
		$DB->query("insert into `gm_order` (`username`,`info`,`money`,`oldmoney`,`newmoney`,`ip`,`city`,`date`,`time`) values ('".$zqsqData['username']."','系统已通过您的转区申请，申请记录ID：".$zqsqData['id']."',  '0', '".$zqsqdlData['money']."', '".$zqsqdlData['money']."', '$ip', '$city', '$date', '$time')");
		$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','通过（".$zqsqData['username']."）提交的转区申请记录，申请记录ID：".$zqsqData['id']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"通过成功！"}');
    break;
    case 'paymoney':
		$agentid = addslashes($post['agentid']);
		$money = addslashes($post['money']);
		$adminData = $Admin->getAdminId($agentid);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
        if ($money=='' || $money <= 0) {
            exit('{"code":0,"msg":"充值金额不能为空"}');
        }
			$sql = "UPDATE `admin` SET `money` = `money`+'$money' WHERE `id` = '".$adminData['id']."'";
        
		$upUagentsql = $DB->exec($sql);
		if(!$upUagentsql){
			exit('{"code":0,"msg":"充值失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','为代理充值额度，代理账号：".$adminData['username']."，充值额度：".$money."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"充值成功！"}');
		}
    break;
    case 'bindEdit':
		$name = addslashes($post['name']);
		$id = addslashes($post['id']);
		$roleid = addslashes($post['roleid']);
		$serverid = addslashes($post['serverid']);
		$money = addslashes($post['money']);
		$charge = addslashes($post['charge']);
		$daycharge = addslashes($post['daycharge']);
		$strtotime = strtotime("now"); 
		$date = date('Y-m-d',$strtotime);
        if ($id=='' || $roleid=='' || $serverid=='' || $money=='' || $charge=='' || $daycharge=='') {
            exit('{"code":0,"msg":"请填写完整"}');
        }
		$bindsData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $id . "' limit 1");
        if(empty($bindsData))exit('{"code":0,"msg":"此绑定信息不存在"}');
		$serversData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $serverid . "' limit 1");
        if(empty($serversData))exit('{"code":0,"msg":"此区服信息不存在"}');
		if($daycharge == 0 ){
			$chargesql = "";
		}else{
			if($bindsData['lastday'] == $date){
				$chargesql = " ,`daycharge`='$daycharge'";
			}else{
				$chargesql = " ,`lastday`='$date',`daycharge`='$daycharge',`daylq`='[0]'";
			}
		}
		$sql = "UPDATE `binds` SET `name`='$name',`roleid`='$roleid',`serverid`='$serverid',`money`='$money',`charge`='$charge'".$chargesql." WHERE `id` = '".$bindsData['id']."'";
		$upBindsql = $DB->exec($sql);
		if(!$upBindsql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改玩家绑定角色信息，角色名：".$name."，角色ID：".$roleid."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'addservers':
		$quzuname = addslashes($post['quzuname']);
		$name = addslashes($post['name']);
		$info = addslashes($post['info']);
		$serverid = addslashes($post['serverid']);
		$ip = addslashes($post['ip']);
		$quport = addslashes($post['quport']);
		$deng = addslashes($post['deng']);
		$biao = addslashes($post['biao']);
		$port = addslashes($post['port']);
		$ptb = addslashes($post['ptb']);
		$charge = addslashes($post['charge']);
		$vip = addslashes($post['vip']);
		$xianyu = addslashes($post['xianyu']);
        if ($name=='' || $quzuname==''  || $info==''  || $serverid==''  || $ip==''  || $quport==''  || $deng==''  || $biao==''  || $port=='' || $ptb=='' || $charge==''|| $vip==''|| $xianyu=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
        $DB->query("insert into `servers` (`quzuname`,`name`,`info`,`serverid`,`ip`,`quport`,`deng`,`biao`,`port`,`ptb`,`charge`,`vip`,`xianyu`) values ('".$quzuname."','".$name."','".$info."','".$serverid."','".$ip."','".$quport."','".$deng."','".$biao."','".$port."','".$ptb."','".$charge."','".$vip."','".$xianyu."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加大区信息，大区名称：".$name."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'zbdzadditem':
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `zbdz` (`name`,`itemid`,`type`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','1','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加装备定制基础装备，名称：".$itemsData['name']."，价格：".$price."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'addpetskill':
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此技能信息不存在"}');
        $DB->query("insert into `petskilldz` (`name`,`itemid`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加宠物技能定制信息，名称：".$itemsData['name']."，价格：".$price."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editpetskill':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' || $id=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此技能信息不存在"}');
		
		$zbdzData=$DB->getRow("SELECT * FROM `petskilldz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zbdzData))exit('{"code":0,"msg":"此信息不存在"}');
		$addupsssql = "UPDATE `petskilldz` SET `price`='".$price."',`name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改宠物技能定制信息，名称：".$itemsData['name']."，价格：".$price."，ID：".$id."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deletepetskill':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `petskilldz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此信息不存在"}');
        $sql="DELETE FROM `petskilldz` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除宠物技能定制信息，ID：".$shopData['id']."，名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'editzbdzitem':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' || $id=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		
		$zbdzData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zbdzData))exit('{"code":0,"msg":"此信息不存在"}');
		$addupsssql = "UPDATE `zbdz` SET `price`='".$price."',`name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改装备定制信息，名称：".$itemsData['name']."，价格：".$price."，ID：".$id."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deletezbdzitem':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此信息不存在"}');
        $sql="DELETE FROM `zbdz` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除装备定制信息，ID：".$shopData['id']."，名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'blacklist':
		$ip = addslashes($post['ip']);
		$ipData=$DB->getRow("SELECT * FROM `black_ip` WHERE `ip` ='" . $ip . "' limit 1");
        if(!empty($ipData))exit('{"code":0,"msg":"此IP已加入黑名单！"}');
		$DB->query("insert into `black_ip` (`ip`) values ('".$ip."')"); 
        if(!$DB){
			exit('{"code":0,"msg":"添加失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加IP黑名单，IP：".$ip."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"添加成功！"}');
		}
    break;
    case 'deleteblacklist':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `black_ip` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此IP不存在"}');
        $sql="DELETE FROM `black_ip` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除黑名单IP，IP：".$ip."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'zbdzaddteji':
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `zbdz` (`name`,`itemid`,`type`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','2','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加装备定制装备特技，名称：".$itemsData['name']."，价格：".$price."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editzbdzteji':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' || $id=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		
		$zbdzData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zbdzData))exit('{"code":0,"msg":"此信息不存在"}');
		$addupsssql = "UPDATE `zbdz` SET `price`='".$price."',`name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改装备定制信息，名称：".$itemsData['name']."，价格：".$price."，ID：".$id."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deletezbdzteji':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此信息不存在"}');
        $sql="DELETE FROM `zbdz` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除装备定制信息，ID：".$shopData['id']."，名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
	
    case 'zbdzaddtexiao':
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `zbdz` (`name`,`itemid`,`type`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','3','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加装备定制装备特效，名称：".$itemsData['name']."，价格：".$price."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editzbdztexiao':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$price = addslashes($post['price']);
        if ($selectitem=='' || $price=='' || $id=='' ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		
		$zbdzData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($zbdzData))exit('{"code":0,"msg":"此信息不存在"}');
		$addupsssql = "UPDATE `zbdz` SET `price`='".$price."',`name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改装备定制信息，名称：".$itemsData['name']."，价格：".$price."，ID：".$id."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deletezbdztexiao':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此信息不存在"}');
        $sql="DELETE FROM `zbdz` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除装备定制信息，ID：".$shopData['id']."，名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
	
    case 'addtype':
		$name = addslashes($post['name']);
		$image = addslashes($post['image']);
        if ($name=='') {
            exit('{"code":0,"msg":"名称不能为空"}');
        }
        if ($image=='') {
            exit('{"code":0,"msg":"请选择图标"}');
        }
        $DB->query("insert into `types` (`name`,`image`,`type`) values ('".$name."', '$image', '1')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加平台币商城分类，分类名称：".$name."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'addaddup':
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$lv = addslashes($post['lv']);
		$tid = addslashes($post['tid']);
		$image = addslashes($post['image']);
        if ($selectitem=='' || $num=='' || $lv=='' || $tid=='' || $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		if($tid == 1){
        $DB->query("insert into `addup` (`name`,`image`,`itemid`,`num`,`lv`,`type`) values ('".$itemsData['name']."','".$image."','".$itemsData['itemid']."','".$num."','".$lv."','1')");
		$info = "添加新的角色累计奖励，物品名称：".$itemsData['name'];
		}else if($tid == 2){
        $DB->query("insert into `addup` (`name`,`image`,`itemid`,`num`,`lv`,`type`) values ('".$itemsData['name']."','".$image."','".$itemsData['itemid']."','".$num."','".$lv."','2')");
		$info = "添加新的今日累计奖励，物品名称：".$itemsData['name'];
		}
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','$info', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'adddraw':
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$value = addslashes($post['value']);
		$image = addslashes($post['image']);
        if ($selectitem=='' || $num=='' || $value=='' || $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `draws` (`name`,`itemid`,`num`,`value`,`image`) values ('".$itemsData['name']."','".$itemsData['itemid']."','".$num."','".$value."','".$image."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的抽奖奖品，奖品名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editdraw':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$value = addslashes($post['value']);
		$image = addslashes($post['image']);
        if ($id=='' || $selectitem=='' || $num=='' || $value=='' || $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$drawsData=$DB->getRow("SELECT * FROM `draws` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawsData))exit('{"code":0,"msg":"此奖品信息不存在"}');
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		$drawsssql = "UPDATE `draws` SET `name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."',`num`='".$num."',`value`='".$value."',`image`='".$image."' WHERE `id` = '".$id."'";
		$updrawsssql = $DB->exec($drawsssql);
		if(!$updrawsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改奖品信息，奖品ID：".$id."，名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'addaward':
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$types = addslashes($post['types']);
		$days = addslashes($post['days']);
		$image = addslashes($post['image']);
        if ($selectitem=='' || $num=='' || $types=='' || $image=='' || $days=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		if($selectitem == 'ptb'){
			$itemsData['name'] = '平台币';
			$itemsData['itemid'] = '88';
		}else{
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		}
        $DB->query("insert into `vipitems` (`name`,`itemid`,`num`,`types`,`image`,`days`) values ('".$itemsData['name']."','".$itemsData['itemid']."','".$num."','".$types."','".$image."','".$days."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的会员奖品，奖品名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editaward':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$types = addslashes($post['types']);
		$days = addslashes($post['days']);
		$image = addslashes($post['image']);
        if ($id=='' || $selectitem=='' || $num=='' || $types=='' || $days==''|| $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$drawsData=$DB->getRow("SELECT * FROM `vipitems` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawsData))exit('{"code":0,"msg":"此奖品信息不存在"}');
		if($selectitem == 'ptb'){
			$itemsData['name'] = '平台币';
			$itemsData['itemid'] = '88';
		}else{
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		}
		$drawsssql = "UPDATE `vipitems` SET `name`='".$itemsData['name']."',`days`='".$days."',`itemid`='".$itemsData['itemid']."',`num`='".$num."',`types`='".$types."',`image`='".$image."' WHERE `id` = '".$id."'";
		$updrawsssql = $DB->exec($drawsssql);
		if(!$updrawsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改会员奖品信息，奖品ID：".$id."，名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deleteaward':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `vipitems` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此奖品信息不存在"}');
        $sql="DELETE FROM `vipitems` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除会员奖品信息，奖品ID：".$shopData['id']."，奖品名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'addadduptype':
		$name = addslashes($post['name']);
		$value = addslashes($post['value']);
		$image = addslashes($post['image']);
        if ($name=='' || $value=='' || $image==''|| $value <= 0 ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
        $DB->query("insert into `adduptype` (`name`,`image`,`value`) values ('".$name."','".$image."','".$value."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的累计档次，档次名称：".$name."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'adddrawrule':
		$times = addslashes($post['times']);
		$value = addslashes($post['value']);
		$money = addslashes($post['money']);
		$ptbopen = addslashes($post['ptbopen']);
		if($ptbopen!=1)$ptbopen==0;
        if ($times=='' || $value=='' || $money=='' || $times  < 0 || $value <= 0 || $money <= 0 ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
        $DB->query("insert into `drawrule` (`times`,`value`,`money`,`ptbopen`) values ('".$times."','".$value."','".$money."','".$ptbopen."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的抽奖规则，次数：".$times."，平台币价格：".$value."，现金价格：".$value."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'allgetmoney':
		$serverid = addslashes($post['serverid']);
		$money = addslashes($post['money']);
        if ($serverid=='' || $money=='' || $money <= 0 ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$serversData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $serverid . "' limit 1");
        if(empty($serversData))exit('{"code":0,"msg":"此大区信息不存在"}');
		$addupsssql = "UPDATE `binds` SET `money`=`money`+'".$money."' WHERE `serverid` = '".$serversData['id']."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"发送失败！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','全服补偿平台币，补偿数量：".$money."元', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"发送成功！"}');
		}
    break;
    case 'editdrawrule':
		$id = addslashes($post['id']);
		$times = addslashes($post['times']);
		$value = addslashes($post['value']);
		$money = addslashes($post['money']);
		$ptbopen = addslashes($post['ptbopen']);
		if($ptbopen!=1)$ptbopen==0;
        if ($id=='' || $times=='' || $value=='' || $money=='' || $times  < 0 || $value <= 0 || $money <= 0 ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$drawsData=$DB->getRow("SELECT * FROM `drawrule` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawsData))exit('{"code":0,"msg":"此抽奖规则不存在"}');
		$addupsssql = "UPDATE `drawrule` SET `times`='".$times."',`value`='".$value."',`money`='".$money."',`ptbopen`='".$ptbopen."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改抽奖规则信息，抽奖规则ID：".$id."，抽奖次数：".$times."，平台币价格：".$value."，现金价格：".$money."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'editadduptype':
		$id = addslashes($post['id']);
		$name = addslashes($post['name']);
		$value = addslashes($post['value']);
		$image = addslashes($post['image']);
        if ($id=='' || $name=='' || $value=='' || $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$drawsData=$DB->getRow("SELECT * FROM `adduptype` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawsData))exit('{"code":0,"msg":"此档次信息不存在"}');
		$addupsssql = "UPDATE `adduptype` SET `name`='".$name."',`value`='".$value."',`image`='".$image."' WHERE `id` = '".$id."'";
		$upaddupsssql = $DB->exec($addupsssql);
		if(!$upaddupsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改累计档次信息，档次ID：".$id."，名称：".$name."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'editaddup':
		$id = addslashes($post['id']);
		$tid = addslashes($post['tid']);
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$lv = addslashes($post['lv']);
		$image = addslashes($post['image']);
        if ($id=='' || $tid=='' || $selectitem=='' || $num=='' || $lv=='' || $image=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$drawsData=$DB->getRow("SELECT * FROM `addup` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawsData))exit('{"code":0,"msg":"此累计奖励信息不存在"}');
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		$drawsssql = "UPDATE `addup` SET `name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."',`num`='".$num."',`lv`='".$lv."',`image`='".$image."',`type`='".$tid."' WHERE `id` = '".$id."'";
		$updrawsssql = $DB->exec($drawsssql);
		if(!$updrawsssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			if($tid == 1){
				$info = "修改角色累计奖品信息，奖品ID：".$id."，名称：".$itemsData['name'];
			}else{
				$info = "修改今日累计奖品信息，奖品ID：".$id."，名称：".$itemsData['name'];
			}
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','$info', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'editservers':
		$id = addslashes($post['id']);
		$quzuname = addslashes($post['quzuname']);
		$name = addslashes($post['name']);
		$info = addslashes($post['info']);
		$serverid = addslashes($post['serverid']);
		$ip = addslashes($post['ip']);
		$quport = addslashes($post['quport']);
		$deng = addslashes($post['deng']);
		$biao = addslashes($post['biao']);
		$port = addslashes($post['port']);
		$ptb = addslashes($post['ptb']);
		$charge = addslashes($post['charge']);
		$vip = addslashes($post['vip']);
		$xianyu = addslashes($post['xianyu']);
        if ($id=='' || $name=='' || $quzuname==''  || $info==''  || $serverid==''  || $ip==''  || $quport==''  || $deng==''  || $biao==''  || $port=='' || $ptb=='' || $charge==''|| $vip==''  || $xianyu==''   ) {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$serversData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $id . "' limit 1");
        if(empty($serversData))exit('{"code":0,"msg":"此大区信息不存在"}');
		$serverssql = "UPDATE `servers` SET `name`='".$name."',`quzuname`='".$quzuname."',`info`='".$info."',`serverid`='".$serverid."',`ip`='".$ip."',`quport`='".$quport."',`deng`='".$deng."',`biao`='".$biao."',`port`='".$port."',`ptb`='".$ptb."',`charge`='".$charge."',`vip`='".$vip."',`xianyu`='".$xianyu."'  WHERE `id` = '".$serversData['id']."'";
		$upserverssql = $DB->exec($serverssql);
		if(!$upserverssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改大区信息，大区ID：".$id."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'addshop':
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$itemtype = addslashes($post['itemtype']);
		$price = addslashes($post['price']);
		$status = addslashes($post['status']);
		$image = addslashes($post['image']);
		$info = addslashes($post['info']);
        if ($selectitem=='' || $num=='' || $itemtype=='' || $price=='' || $status=='' || $image=='' || $info=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `shops` (`name`,`itemid`,`num`,`itemtype`,`image`,`info`,`status`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','".$num."','".$itemtype."','".$image."','".$info."','".$status."','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的平台币商品，商品名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editshop':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$itemtype = addslashes($post['itemtype']);
		$price = addslashes($post['price']);
		$status = addslashes($post['status']);
		$image = addslashes($post['image']);
		$info = addslashes($post['info']);
        if ($id=='' || $selectitem=='' || $num=='' || $itemtype=='' || $price=='' || $status=='' || $image=='' || $info=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$shopsData=$DB->getRow("SELECT * FROM `shops` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopsData))exit('{"code":0,"msg":"此商品信息不存在"}');
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		$shopssql = "UPDATE `shops` SET `name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."',`num`='".$num."',`itemtype`='".$itemtype."',`price`='".$price."',`status`='".$status."',`image`='".$image."',`info`='".$info."' WHERE `id` = '".$id."'";
		$upshopssql = $DB->exec($shopssql);
		if(!$upshopssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改商品信息，商品ID：".$id."，名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'delateshop':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `shops` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此商品信息不存在"}');
        $sql="DELETE FROM `shops` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除商品信息，商品ID：".$shopData['id']."，商品名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'addrmbshop':
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$itemtype = addslashes($post['itemtype']);
		$price = addslashes($post['price']);
		$status = addslashes($post['status']);
		$image = addslashes($post['image']);
		$info = addslashes($post['info']);
        if ($selectitem=='' || $num=='' || $itemtype=='' || $price=='' || $status=='' || $image=='' || $info=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
        $DB->query("insert into `rmbshops` (`name`,`itemid`,`num`,`itemtype`,`image`,`info`,`status`,`price`) values ('".$itemsData['name']."','".$itemsData['itemid']."','".$num."','".$itemtype."','".$image."','".$info."','".$status."','".$price."')");
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','添加新的现金商品，商品名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
		exit('{"code":1,"msg":"添加成功"}');
    break;
    case 'editrmbshop':
		$id = addslashes($post['id']);
		$selectitem = addslashes($post['selectitem']);
		$num = addslashes($post['num']);
		$itemtype = addslashes($post['itemtype']);
		$price = addslashes($post['price']);
		$status = addslashes($post['status']);
		$image = addslashes($post['image']);
		$info = addslashes($post['info']);
        if ($id=='' || $selectitem=='' || $num=='' || $itemtype=='' || $price=='' || $status=='' || $image=='' || $info=='') {
            exit('{"code":0,"msg":"所有参数都不能为空"}');
        }
		$shopsData=$DB->getRow("SELECT * FROM `rmbshops` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopsData))exit('{"code":0,"msg":"此商品信息不存在"}');
		$itemsData=$DB->getRow("SELECT * FROM `items` WHERE `id` ='" . $selectitem . "' limit 1");
        if(empty($itemsData))exit('{"code":0,"msg":"此物品信息不存在"}');
		$shopssql = "UPDATE `rmbshops` SET `name`='".$itemsData['name']."',`itemid`='".$itemsData['itemid']."',`num`='".$num."',`itemtype`='".$itemtype."',`price`='".$price."',`status`='".$status."',`image`='".$image."',`info`='".$info."' WHERE `id` = '".$id."'";
		$upshopssql = $DB->exec($shopssql);
		if(!$upshopssql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改现金商品信息，商品ID：".$id."，名称：".$itemsData['name']."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'delatermbshop':
		$id = intval($post['id']);
		$shopData=$DB->getRow("SELECT * FROM `rmbshops` WHERE `id` ='" . $id . "' limit 1");
        if(empty($shopData))exit('{"code":0,"msg":"此商品信息不存在"}');
        $sql="DELETE FROM `rmbshops` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除现金商品信息，商品ID：".$shopData['id']."，商品名称：".$shopData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'edittype':
		$name = addslashes($post['name']);
		$id = addslashes($post['id']);
		$image = addslashes($post['image']);
        if ($id=='' || $image=='' || $name=='') {
            exit('{"code":0,"msg":"请填写完整"}');
        }
		$typesData=$DB->getRow("SELECT * FROM `types` WHERE `id` ='" . $id . "' limit 1");
        if(empty($typesData))exit('{"code":0,"msg":"此分类信息不存在"}');
		$typesql = "UPDATE `types` SET `name`='$name',`image`='$image' WHERE `id` = '".$id."'";
		$uptypesql = $DB->exec($typesql);
		if(!$uptypesql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改分类信息，分类ID：".$id."，名称：".$name."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'deleteadduptype':
		$id = intval($post['id']);
		$addupsData=$DB->getRow("SELECT * FROM `adduptype` WHERE `id` ='" . $id . "' limit 1");
        if(empty($addupsData))exit('{"code":0,"msg":"此档次信息不存在"}');
        $sql="DELETE FROM `adduptype` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除累计档次信息，档次ID：".$id."，名称：".$addupsData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'delbind':
		$id = intval($post['id']);
		$bindsData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $id . "' limit 1");
        if(empty($bindsData))exit('{"code":0,"msg":"此绑定信息不存在"}');
        $sql="DELETE FROM `binds` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除玩家绑定信息，绑定角色ID：".$bindsData['roleid']."，角色名称：".$bindsData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'deletedrawrule':
		$id = intval($post['id']);
		$drawrulesData=$DB->getRow("SELECT * FROM `drawrule` WHERE `id` ='" . $id . "' limit 1");
        if(empty($drawrulesData))exit('{"code":0,"msg":"此抽奖规则不存在"}');
        $sql="DELETE FROM `drawrule` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除抽奖规则，抽奖次数：".$drawrulesData['times']."，抽奖价格：".$drawrulesData['value']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'deleteaddup':
		$id = intval($post['id']);
		$addupsData=$DB->getRow("SELECT * FROM `addup` WHERE `id` ='" . $id . "' limit 1");
        if(empty($addupsData))exit('{"code":0,"msg":"此奖品信息不存在"}');
        $sql="DELETE FROM `addup` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			if($addupsData['type']==1){
				$info='删除角色累计奖品信息，奖品ID：'.$addupsData['id'].'，名称：'.$addupsData['name'];
			}else if($addupsData['type']==2){
				$info='删除今日累计奖品信息，奖品ID：'.$addupsData['id'].'，名称：'.$addupsData['name'];
			}
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','$info', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'deleteserver':
		$id = intval($post['id']);
		$serversData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $id . "' limit 1");
        if(empty($serversData))exit('{"code":0,"msg":"此区服信息不存在"}');
        $sql="DELETE FROM `servers` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除区服信息，区服ID：".$id."，名称：".$serversData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'deletedraw':
		$id = intval($post['id']);
		$typesData=$DB->getRow("SELECT * FROM `draws` WHERE `id` ='" . $id . "' limit 1");
        if(empty($typesData))exit('{"code":0,"msg":"此奖品信息不存在"}');
        $sql="DELETE FROM `draws` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除奖品信息，奖品ID：".$id."，名称：".$typesData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'delatetype':
		$id = intval($post['id']);
		$typesData=$DB->getRow("SELECT * FROM `types` WHERE `id` ='" . $id . "' limit 1");
        if(empty($typesData))exit('{"code":0,"msg":"此分类信息不存在"}');
        $sql="DELETE FROM `types` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除分类信息，分类ID：".$id."，名称：".$typesData['name']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'fjAgent':
		$id = intval($post['id']);
		$adminData = $Admin->getAdminId($id);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
		$sql = "UPDATE `admin` SET `status`='0' WHERE `id` = '".$id."'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"封禁失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','封禁代理，账号：".$adminData['username']."，邀请码：".$adminData['invite']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"封禁成功！"}');
		}
    break;
    case 'jfAgent':
		$id = intval($post['id']);
		$adminData = $Admin->getAdminId($id);
        if(empty($adminData))exit('{"code":0,"msg":"此代理信息不存在"}');
		$sql = "UPDATE `admin` SET `status`='1' WHERE `id` = '".$id."'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"解封失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','解除代理封禁，账号：".$adminData['username']."，邀请码：".$adminData['invite']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"解封成功！"}');
		}
    break;
    case 'delateUser':
		$id = intval($post['id']);
		$userData = $Admin->getUserId($id);
        if(empty($userData))exit('{"code":0,"msg":"此玩家账号不存在"}');
        $sql="DELETE FROM `account` WHERE `id`='$id'";
        if(!$DB->exec($sql)){
			exit('{"code":0,"msg":"删除失败！"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除玩家账号，账号：".$userData['username']."', NOW(), '$ip', '$city')");
            exit('{"code":1,"msg":"删除成功！"}');
		}
    break;
    case 'noticeset':
		$servernotice = addslashes($post['servernotice']);
		$gundongnotice = addslashes($post['gundongnotice']);
		$dailinotice = addslashes($post['dailinotice']);
        if ($servernotice==NULL || $gundongnotice==NULL || $dailinotice==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		$servernotice = "UPDATE `config` SET `values` = '$servernotice' WHERE `keys` = 'servernotice' ";
		$gundongnotice = "UPDATE `config` SET `values` = '$gundongnotice' WHERE `keys` = 'gundongnotice' ";
		$dailinotice = "UPDATE `config` SET `values` = '$dailinotice' WHERE `keys` = 'dailinotice' ";
		$servernotice = $DB->exec($servernotice);
		$gundongnotice = $DB->exec($gundongnotice);
		$dailinotice = $DB->exec($dailinotice);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改公告相关配置', NOW(), '$ip', '$city')");
    break;
    case 'otherset':
		$title = addslashes($post['title']);
		$keywords = addslashes($post['keywords']);
		$description = addslashes($post['description']);
		$banquan = addslashes($post['banquan']);
		$logoimg = addslashes($post['logoimg']);
		$bjimg = addslashes($post['bjimg']);
		$anwzA = addslashes($post['anwzA']);
		$anljA = addslashes($post['anljA']);
		$anwzB = addslashes($post['anwzB']);
		$anljB = addslashes($post['anljB']);
		$anwzC = addslashes($post['anwzC']);
		$anljC = addslashes($post['anljC']);
		$wanjiaanwz = addslashes($post['wanjiaanwz']);
		$wanjiaanlj = addslashes($post['wanjiaanlj']);
		
		$kuaijiecaidan = addslashes($post['kuaijiecaidan']);
		$kuaijimenua = addslashes($post['kuaijimenua']);
		$kuaijimenub = addslashes($post['kuaijimenub']);
		$kuaijimenuc = addslashes($post['kuaijimenuc']);
		$kuaijimenud = addslashes($post['kuaijimenud']);
		$kuaijimenue = addslashes($post['kuaijimenue']);
		$kuaijimenualj = addslashes($post['kuaijimenualj']);
		$kuaijimenublj = addslashes($post['kuaijimenublj']);
		$kuaijimenuclj = addslashes($post['kuaijimenuclj']);
		$kuaijimenudlj = addslashes($post['kuaijimenudlj']);
		$kuaijimenuelj = addslashes($post['kuaijimenuelj']);
		$qqwxjump = addslashes($post['qqwxjump']);
		if($kuaijiecaidan == null)$kuaijiecaidan = 0;
		if($qqwxjump == null)$qqwxjump = 0;
        if ( $kuaijimenua==NULL || $kuaijimenub==NULL || $kuaijimenuc==NULL  || $kuaijimenud==NULL  || $kuaijimenue==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
        if ($kuaijimenualj==NULL || $kuaijimenublj==NULL || $kuaijimenuclj==NULL || $kuaijimenudlj==NULL  || $kuaijimenuelj==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
        if ($wanjiaanwz==NULL || $wanjiaanlj==NULL || $title==NULL || $keywords==NULL  || $description==NULL  || $banquan==NULL  || $logoimg==NULL  || $bjimg==NULL || $anwzA==NULL || $anljA==NULL || $anwzB==NULL || $anljB==NULL || $anwzC==NULL  || $anljC==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		$title = "UPDATE `config` SET `values` = '$title' WHERE `keys` = 'title' ";
		$keywords = "UPDATE `config` SET `values` = '$keywords' WHERE `keys` = 'keywords' ";
		$description = "UPDATE `config` SET `values` = '$description' WHERE `keys` = 'description' ";
		$banquan = "UPDATE `config` SET `values` = '$banquan' WHERE `keys` = 'banquan' ";
		$logoimg = "UPDATE `config` SET `values` = '$logoimg' WHERE `keys` = 'logoimg' ";
		$bjimg = "UPDATE `config` SET `values` = '$bjimg' WHERE `keys` = 'bjimg' ";
		$anwzA = "UPDATE `config` SET `values` = '$anwzA' WHERE `keys` = 'anwzA' ";
		$anljA = "UPDATE `config` SET `values` = '$anljA' WHERE `keys` = 'anljA' ";
		$anwzB = "UPDATE `config` SET `values` = '$anwzB' WHERE `keys` = 'anwzB' ";
		$anljB = "UPDATE `config` SET `values` = '$anljB' WHERE `keys` = 'anljB' ";
		$anwzC = "UPDATE `config` SET `values` = '$anwzC' WHERE `keys` = 'anwzC' ";
		$anljC = "UPDATE `config` SET `values` = '$anljC' WHERE `keys` = 'anljC' ";
		$kuaijiecaidan = "UPDATE `config` SET `values` = '$kuaijiecaidan' WHERE `keys` = 'kuaijiecaidan' ";
		$wanjiaanlj = "UPDATE `config` SET `values` = '$wanjiaanlj' WHERE `keys` = 'wanjiaanlj' ";
		$qqwxjump = "UPDATE `config` SET `values` = '$qqwxjump' WHERE `keys` = 'qqwxjump' ";
		
		
		$kuaijimenua = "UPDATE `config` SET `values` = '$kuaijimenua' WHERE `keys` = 'kuaijimenua' ";
		$kuaijimenub = "UPDATE `config` SET `values` = '$kuaijimenub' WHERE `keys` = 'kuaijimenub' ";
		$kuaijimenuc = "UPDATE `config` SET `values` = '$kuaijimenuc' WHERE `keys` = 'kuaijimenuc' ";
		$kuaijimenud = "UPDATE `config` SET `values` = '$kuaijimenud' WHERE `keys` = 'kuaijimenud' ";
		$kuaijimenue = "UPDATE `config` SET `values` = '$kuaijimenue' WHERE `keys` = 'kuaijimenue' ";
		$kuaijimenualj = "UPDATE `config` SET `values` = '$kuaijimenualj' WHERE `keys` = 'kuaijimenualj' ";
		$kuaijimenublj = "UPDATE `config` SET `values` = '$kuaijimenublj' WHERE `keys` = 'kuaijimenublj' ";
		$kuaijimenuclj = "UPDATE `config` SET `values` = '$kuaijimenuclj' WHERE `keys` = 'kuaijimenuclj' ";
		$kuaijimenudlj = "UPDATE `config` SET `values` = '$kuaijimenudlj' WHERE `keys` = 'kuaijimenudlj' ";
		$kuaijimenuelj = "UPDATE `config` SET `values` = '$kuaijimenuelj' WHERE `keys` = 'kuaijimenuelj' ";
		
		
		$title = $DB->exec($title);
		$keywords = $DB->exec($keywords);
		$description = $DB->exec($description);
		$banquan = $DB->exec($banquan);
		$logoimg = $DB->exec($logoimg);
		$bjimg = $DB->exec($bjimg);
		$anwzA = $DB->exec($anwzA);
		$anljA = $DB->exec($anljA);
		$anwzB = $DB->exec($anwzB);
		$anljB = $DB->exec($anljB);
		$anwzC = $DB->exec($anwzC);
		$anljC = $DB->exec($anljC);
		$wanjiaanwz = $DB->exec($wanjiaanwz);
		$wanjiaanlj = $DB->exec($wanjiaanlj);
		$kuaijiecaidan = $DB->exec($kuaijiecaidan);
		
		$kuaijimenua = $DB->exec($kuaijimenua);
		$kuaijimenub = $DB->exec($kuaijimenub);
		$kuaijimenuc = $DB->exec($kuaijimenuc);
		$kuaijimenud = $DB->exec($kuaijimenud);
		$kuaijimenue = $DB->exec($kuaijimenue);
		$kuaijimenualj = $DB->exec($kuaijimenualj);
		$kuaijimenublj = $DB->exec($kuaijimenublj);
		$kuaijimenuclj = $DB->exec($kuaijimenuclj);
		$kuaijimenudlj = $DB->exec($kuaijimenudlj);
		$kuaijimenuelj = $DB->exec($kuaijimenuelj);
		$qqwxjump = $DB->exec($qqwxjump);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改其他设置', NOW(), '$ip', '$city')");
    break;
    case 'vipset':
		$zhouka = addslashes($post['zhouka']);
		$yueka = addslashes($post['yueka']);
        if ($yueka==NULL || $zhouka==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		$zhouka = "UPDATE `config` SET `values` = '$zhouka' WHERE `keys` = 'zhouka' ";
		$yueka = "UPDATE `config` SET `values` = '$yueka' WHERE `keys` = 'yueka' ";
		$zhouka = $DB->exec($zhouka);
		$yueka = $DB->exec($yueka);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改普通周卡高级周卡价格', NOW(), '$ip', '$city')");
    break;
    case 'allset':
		$itemid = addslashes($post['itemid']);
		$number = addslashes($post['number']);
		$zengsong = addslashes($post['zengsong']);
		$limitAccount = addslashes($post['limitAccount']);
        if ($itemid==NULL || $number==NULL || $zengsong==NULL || $limitAccount==NULL ) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		$fuliitems = $itemid.';'.$number;
		$fuliitems = "UPDATE `config` SET `values` = '$fuliitems' WHERE `keys` = 'fuliitems' ";
		$fuliitems = $DB->exec($fuliitems);
		$zengsongs = "UPDATE `config` SET `values` = '$zengsong' WHERE `keys` = 'zengsong' ";
		$zengsongs = $DB->exec($zengsongs);
		$limitAccount = "UPDATE `config` SET `values` = '$limitAccount' WHERE `keys` = 'limitAccount' ";
		$limitAccount = $DB->exec($limitAccount);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改综合配置', NOW(), '$ip', '$city')");
    break;
    case 'openingset':
		$gongneng_0 = isset($post['gongneng_0'])?addslashes($post['gongneng_0']):0;
		$gongneng_1 = isset($post['gongneng_1'])?addslashes($post['gongneng_1']):0;
		$gongneng_2 = isset($post['gongneng_2'])?addslashes($post['gongneng_2']):0;
		$gongneng_3 = isset($post['gongneng_3'])?addslashes($post['gongneng_3']):0;
		$gongneng_4 = isset($post['gongneng_4'])?addslashes($post['gongneng_4']):0;
		$gongneng_5 = isset($post['gongneng_5'])?addslashes($post['gongneng_5']):0;
		$gongneng_6 = isset($post['gongneng_6'])?addslashes($post['gongneng_6']):0;
		$gongneng_7 = isset($post['gongneng_7'])?addslashes($post['gongneng_7']):0;
		$gongneng_8 = isset($post['gongneng_8'])?addslashes($post['gongneng_8']):0;
		$gongneng_9 = isset($post['gongneng_9'])?addslashes($post['gongneng_9']):0;
		$gongneng_10 = isset($post['gongneng_10'])?addslashes($post['gongneng_10']):0;
		$gongneng_11 = isset($post['gongneng_11'])?addslashes($post['gongneng_11']):0;
		$gongneng_12 = isset($post['gongneng_12'])?addslashes($post['gongneng_12']):0;
		$gongneng_13 = isset($post['gongneng_13'])?addslashes($post['gongneng_13']):0;
		$gongneng_14 = isset($post['gongneng_14'])?addslashes($post['gongneng_14']):0;
		$gongneng_15 = isset($post['gongneng_15'])?addslashes($post['gongneng_15']):0;
		$gongneng_16 = isset($post['gongneng_16'])?addslashes($post['gongneng_16']):0;
		$gongneng_17 = isset($post['gongneng_17'])?addslashes($post['gongneng_17']):0;
		$gongneng_18 = isset($post['gongneng_18'])?addslashes($post['gongneng_18']):0;
		$gongneng_19 = isset($post['gongneng_19'])?addslashes($post['gongneng_19']):0;
		$gongneng_20 = isset($post['gongneng_19'])?addslashes($post['gongneng_20']):0;
		$gongneng = $gongneng_0.';'.$gongneng_1.';'.$gongneng_2.';'.$gongneng_3.';'.$gongneng_4.';'.$gongneng_5.';'.$gongneng_6.';'.$gongneng_7.';'.$gongneng_8.';'.$gongneng_9.';'.$gongneng_10.';'.$gongneng_11.';'.$gongneng_12.';'.$gongneng_13.';'.$gongneng_14.';'.$gongneng_15.';'.$gongneng_16.';'.$gongneng_17.';'.$gongneng_18.';'.$gongneng_19.';'.$gongneng_20.';';
		$gongneng = "UPDATE `config` SET `values` = '$gongneng' WHERE `keys` = 'opening' ";
		$opening = $DB->exec($gongneng);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改玩家功能开关配置', NOW(), '$ip', '$city')");
    break;
    case 'payset':
		$paynotice = addslashes($post['paynotice']);
		$apiurl = addslashes($post['apiurl']);
		$pid = addslashes($post['pid']);
		$key = addslashes($post['key']);
		$ysrmb1 = addslashes($post['ysrmb1']);
		$ysrmb2 = addslashes($post['ysrmb2']);
		$ysrmb3 = addslashes($post['ysrmb3']);
		$zdyrmbmin = addslashes($post['zdyrmbmin']);
		$zdyrmbmax = addslashes($post['zdyrmbmax']);
		//$ptbbili = addslashes($post['ptbbili']);
		//$vipbili = addslashes($post['vipbili']);
		//$xianyubili = addslashes($post['xianyubili']);
        //if ($paynotice==NULL || $apiurl==NULL || $pid==NULL || $key==NULL || $ysrmb1==NULL || $ysrmb2==NULL || $ysrmb3==NULL || $zdyrmbmin==NULL || $zdyrmbmax==NULL || $ptbbili==NULL || $vipbili==NULL || $xianyubili==NULL) {
        if ($paynotice==NULL || $apiurl==NULL || $pid==NULL || $key==NULL || $ysrmb1==NULL || $ysrmb2==NULL || $ysrmb3==NULL || $zdyrmbmin==NULL || $zdyrmbmax==NULL) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		$alipay=isset($post['alipay'])?daddslashes($post['alipay']):0;
		$wxpay=isset($post['wxpay'])?daddslashes($post['wxpay']):0;
		$paynotice = "UPDATE `config` SET `values` = '$paynotice' WHERE `keys` = 'paynotice' ";
		$apiurl = "UPDATE `config` SET `values` = '$apiurl' WHERE `keys` = 'apiurl' ";
		$pid = "UPDATE `config` SET `values` = '$pid' WHERE `keys` = 'pid' ";
		$key = "UPDATE `config` SET `values` = '$key' WHERE `keys` = 'key' ";
		$ysrmb1 = "UPDATE `config` SET `values` = '$ysrmb1' WHERE `keys` = 'ysrmb1' ";
		$ysrmb2 = "UPDATE `config` SET `values` = '$ysrmb2' WHERE `keys` = 'ysrmb2' ";
		$ysrmb3 = "UPDATE `config` SET `values` = '$ysrmb3' WHERE `keys` = 'ysrmb3' ";
		$zdyrmbmin = "UPDATE `config` SET `values` = '$zdyrmbmin' WHERE `keys` = 'zdyrmbmin' ";
		$zdyrmbmax = "UPDATE `config` SET `values` = '$zdyrmbmax' WHERE `keys` = 'zdyrmbmax' ";
		//$ptbbili = "UPDATE `config` SET `values` = '$ptbbili' WHERE `keys` = 'ptbbili' ";
		//$vipbili = "UPDATE `config` SET `values` = '$vipbili' WHERE `keys` = 'vipbili' ";
		//$xianyubili = "UPDATE `config` SET `values` = '$xianyubili' WHERE `keys` = 'xianyubili' ";
		$alipay = "UPDATE `config` SET `values` = '$alipay' WHERE `keys` = 'alipay' ";
		$wxpay = "UPDATE `config` SET `values` = '$wxpay' WHERE `keys` = 'wxpay' ";
		$paynotice = $DB->exec($paynotice);
		$apiurl = $DB->exec($apiurl);
		$pid = $DB->exec($pid);
		$key = $DB->exec($key) ;
		$ysrmb1 = $DB->exec($ysrmb1) ;
		$ysrmb2 = $DB->exec($ysrmb2) ;
		$ysrmb3 = $DB->exec($ysrmb3) ;
		$zdyrmbmin = $DB->exec($zdyrmbmin) ;
		$zdyrmbmax = $DB->exec($zdyrmbmax) ;
		//$ptbbili = $DB->exec($ptbbili) ;
		//$vipbili = $DB->exec($vipbili) ;
		//$xianyubili = $DB->exec($xianyubili) ;
		$alipay = $DB->exec($alipay) ;
		$wxpay = $DB->exec($wxpay);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改支付相关配置', NOW(), '$ip', '$city')");
    break;
    case 'petzzset':
		$chengzhang = addslashes($post['chengzhang']);
		$chengzhangprice = addslashes($post['chengzhangprice']);
		$gongji = addslashes($post['gongji']);
		$gongjiprice = addslashes($post['gongjiprice']);
		$fangyu = addslashes($post['fangyu']);
		$fangyuprice = addslashes($post['fangyuprice']);
		$fashu = addslashes($post['fashu']);
		$fashuprice = addslashes($post['fashuprice']);
		$tizhi = addslashes($post['tizhi']);
		$tizhiprice = addslashes($post['tizhiprice']);
		$sudu = addslashes($post['sudu']);
		$suduprice = addslashes($post['suduprice']);
        if ($chengzhang==NULL || $chengzhangprice==NULL || $gongji==NULL || $gongjiprice==NULL || $fangyu==NULL || $fangyuprice==NULL || $fashu==NULL || $fashuprice==NULL || $tizhi==NULL || $tizhiprice==NULL || $sudu==NULL || $suduprice==NULL) {
            exit('{"code":0,"msg":"修改失败，请确保所有选项都不为空"}');
        }
		//定义语句
		$uppetsetsql1 = "UPDATE `petzizhi` SET `value` = '$chengzhang' WHERE `id` = '1' ";
		$uppetsetsql2 = "UPDATE `petzizhi` SET `value` = '$chengzhangprice' WHERE `id` = '2' ";
		$uppetsetsql3 = "UPDATE `petzizhi` SET `value` = '$gongji' WHERE `id` = '3' ";
		$uppetsetsql4 = "UPDATE `petzizhi` SET `value` = '$gongjiprice' WHERE `id` = '4' ";
		$uppetsetsql5 = "UPDATE `petzizhi` SET `value` = '$fangyu' WHERE `id` = '5' ";
		$uppetsetsql6 = "UPDATE `petzizhi` SET `value` = '$fangyuprice' WHERE `id` = '6' ";
		$uppetsetsql7 = "UPDATE `petzizhi` SET `value` = '$fashu' WHERE `id` = '7' ";
		$uppetsetsql8 = "UPDATE `petzizhi` SET `value` = '$fashuprice' WHERE `id` = '8' ";
		$uppetsetsql9 = "UPDATE `petzizhi` SET `value` = '$tizhi' WHERE `id` = '9' ";
		$uppetsetsql10 = "UPDATE `petzizhi` SET `value` = '$tizhiprice' WHERE `id` = '10' ";
		$uppetsetsql11 = "UPDATE `petzizhi` SET `value` = '$sudu' WHERE `id` = '11' ";
		$uppetsetsql12 = "UPDATE `petzizhi` SET `value` = '$suduprice' WHERE `id` = '12' ";
		//执行语句
		$uppetset1 = $DB->exec($uppetsetsql1);
		$uppetset2 = $DB->exec($uppetsetsql2);
		$uppetset3 = $DB->exec($uppetsetsql3);
		$uppetset4 = $DB->exec($uppetsetsql4);
		$uppetset5 = $DB->exec($uppetsetsql5);
		$uppetset6 = $DB->exec($uppetsetsql6);
		$uppetset7 = $DB->exec($uppetsetsql7);
		$uppetset8 = $DB->exec($uppetsetsql8);
		$uppetset9 = $DB->exec($uppetsetsql9);
		$uppetset10 = $DB->exec($uppetsetsql10);
		$uppetset11 = $DB->exec($uppetsetsql11);
		$uppetset12 = $DB->exec($uppetsetsql12);
		exit('{"code":1,"msg":"修改成功！"}');
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改定制宠物资质相关配置', NOW(), '$ip', '$city')");
    break;
    case 'editqdtb':
		$id = addslashes($post['id']);
		$value = addslashes($post['value']);
        if ($id=='') {
            exit('{"code":0,"msg":"设置项参数错误"}');
        }
		$tubiaoData=$DB->getRow("SELECT * FROM `tubiao` WHERE `id` ='" . $id . "' limit 1");
        if(empty($tubiaoData))exit('{"code":0,"msg":"此图标设置项不存在"}');
        if ($value=='') {
            exit('{"code":0,"msg":"图标不能为空"}');
        }
		$sql = "UPDATE `tubiao` SET `value` = '$value' WHERE `id` = '".$id."'";
		$uptubiaosql = $DB->exec($sql);
		if(!$uptubiaosql){
			exit('{"code":0,"msg":"修改失败，未做改动请忽略！"}');
			//exit('{"code":0,"msg":"修改失败，'.$sql.'"}');
		}else{
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改图标信息，图标项名称：".$tubiaoData['name']."', NOW(), '$ip', '$city')");
			exit('{"code":1,"msg":"修改成功！"}');
		}
    break;
    case 'cdk':
		$types = $get['type'];
		if($types == 'create'){
			$money=$post['money'];
			$number=$post['number'];
			if($money==null || $number==null){
				exit('{"code":0,"msg":"请确保所有选项不为空！"}');
			}
			if($money<=0 || $number<=0){
				exit('{"code":0,"msg":"参数异常！"}');
			}
			function short_md5($str) {
			return substr(md5($str), 8, 16);
			}
			for($i=0;$i<$number;$i++ ){
			$dateA = strtotime("now"); 
			$date = date('Y-m-d',$dateA);
			$time = date('H:i:s',$dateA);
			$orderid =  "Pay".$dateA.short_md5($date);
			$codes =  md5($date.$i.$time);
			$checkCdk=$DB->getRow("SELECT * FROM `cdks` WHERE `codes` ='" . $codes . "' limit 1");
			if($checkCdk){
				while($checkCdk){
					$codes=md5($codes);
				}
			}
			$DB->query("insert into `cdks` (`orderid`,`codes`,`money`,`status`,`times`,`ip`,`city`) values ('$orderid','$codes','$money','0', NOW(), '".$ip."', '".$city."')");
			}
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('" . $_SESSION['adminUser'] . "','生成了【".$number."】张面值为【".$money."】的CDK', NOW(), '".$ip."', '".$city."')");
			exit('{"code":1,"msg":"生成成功，请到【CDK列表】页面下载记录"}');
		}else if($types == 'deletecdks'){
			$id = intval($post['id']);
			$checkCdk=$DB->getRow("SELECT * FROM `cdks` WHERE `id` ='" . $id . "' limit 1");
			if(empty($checkCdk))exit('{"code":0,"msg":"此CDK不存在"}');
			$sql="DELETE FROM `cdks` WHERE `id`='$id'";
			if(!$DB->exec($sql)){
				exit('{"code":0,"msg":"删除失败！"}');
			}else{
				$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','删除CDK信息，CDK：".$checkCdk['codes']."', NOW(), '$ip', '$city')");
				exit('{"code":1,"msg":"删除成功！"}');
			}
		}else if($types == 'cleanA'){
			$id = intval($post['id']);
			if($id == 1){
				$sql="DELETE FROM `cdks` WHERE `status`='1'";
			}else if($id == 2){
				$sql="DELETE FROM `cdks` WHERE `status`!='1'";
			}else{
				$sql="DELETE FROM `cdks` WHERE 1";
			}
			if(!$DB->exec($sql)){
				exit('{"code":0,"msg":"删除失败！"}');
			}else{
				$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','批量删除CDK信息，执行语句：".$sql."', NOW(), '$ip', '$city')");
				exit('{"code":1,"msg":"删除成功！"}');
			}
		}else if($types == 'download'){
			$id=intval($get['id']);
			if($id==1){
				$sql = ' 1';
			}else{
				$sql = ' `status`=0';
			}
			$rs=$DB->query("SELECT * FROM cdks WHERE ".$sql."  order by id asc ");
			$i=0;
			while($res = $rs->fetch())
			{
			$i++;
			if($res['suatus'] == 1){
				$suatus = '已使用';
				$infos = $res['info'];
			}else{
				$suatus = '未使用';
				$infos = '未使用';
			}
			$data.=$i.','.$res['orderid'].','.$res['codes'].','.$res['money'].','.mb_convert_encoding($suatus, "GB2312", "UTF-8").','.mb_convert_encoding($infos, "GB2312", "UTF-8").','.$res['times'].','.$res['ip'].','.mb_convert_encoding($res['city'], "GB2312", "UTF-8")."\r\n";
			$date=date("Ymd");
			$files="ID,订单批号,CDK,面值,使用状态,使用信息,时间,IP,地址\r\n";
			$file=mb_convert_encoding($files, "GB2312", "UTF-8");
			$file.=$data;
			}
			$file_name='Cdks_'.date("YmdHis").'.csv';
			$file_size=strlen($file);
			header("Content-Description: File Transfer");
			header("Content-Type:application/force-download");
			header("Content-Length: {$file_size}");
			header("Content-Disposition:attachment; filename={$file_name}");
			exit($file);
		}else if($types == 'config'){
			$ptb = isset($post['ptb'])?addslashes($post['ptb']):0;
			$charge = isset($post['charge'])?addslashes($post['charge']):0;
			$xianyu = isset($post['xianyu'])?addslashes($post['xianyu']):0;
			$vip = isset($post['vip'])?addslashes($post['vip']):0;
			$cdkset = $ptb.';'.$charge.';'.$xianyu.';'.$vip;
			$cdkset = "UPDATE `config` SET `values` = '$cdkset' WHERE `keys` = 'cdkset' ";
			$cdkset = $DB->exec($cdkset);
			exit('{"code":1,"msg":"修改成功！"}');
			$DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','修改CDK领取奖励配置', NOW(), '$ip', '$city')");
		}
    break;
    case 'imagetongbu':
		$handler = opendir('../../upimg');
		//清空图标库
		$DB->query("TRUNCATE TABLE `images` ");
		while( ($filename = readdir($handler)) !== false ) 
		{
			//略过linux目录的名字为'.'和‘..'的文件
			if($filename != '.' && $filename != '..')
			{  
				$suffix=substr($filename,-4);
				$a = strncasecmp($suffix,".png",4);
				$b = strncasecmp($suffix,".jpg",4);
				if($a == 0 || $b = 0){
					if(!is_dir('../../upimg/'.$filename)){
						//写入数据库
						$DB->query("insert into `images` (`value`) values ('/upimg/".$filename."')");
					}
				}
			}
		}
		clearstatcache();
		closedir($handler);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','操作同步图标库', NOW(), '$ip', '$city')");
        exit('{"code":1,"msg":"同步成功，请刷新页面查看！"}');
    break;
    case 'qingdang':
		//清空账号库
		$DB->query("TRUNCATE TABLE `account` ");
		usleep(5000);
		//清空后台操作日志
		$DB->query("TRUNCATE TABLE `admin_log` ");
		usleep(5000);
		//清空角色绑定库
		$DB->query("TRUNCATE TABLE `binds` ");
		usleep(5000);
		//清空网页背包库
		$DB->query("TRUNCATE TABLE `bindsbag` ");
		usleep(5000);
		//清空CDK库
		$DB->query("TRUNCATE TABLE `cdks` ");
		usleep(5000);
		//清空代理手动充值记录
		$DB->query("TRUNCATE TABLE `gm_order` ");
		usleep(5000);
		//清空玩家充值记录
		$DB->query("TRUNCATE TABLE `pay_order` ");
		usleep(5000);
		//清空玩家注册IP限制
		$DB->query("TRUNCATE TABLE `reg_ip` ");
		usleep(5000);
		//清空玩家操作日志
		$DB->query("TRUNCATE TABLE `user_log` ");
		usleep(5000);
		//清空转区申请记录
		$DB->query("TRUNCATE TABLE `zqsq_log` ");
		usleep(5000);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','操作进行后台一键清档', NOW(), '$ip', '$city')");
        exit('{"code":1,"msg":"清档成功，请刷新网页查看！"}');
    break;
    case 'tongbu':
		$DB->query("TRUNCATE TABLE `items` ");
		//间隔
		sleep(0.2);
		//导入  物品
		$file = fopen("../../txt/itemid.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 1)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  宠物
		$file = fopen("../../txt/petid.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 2)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  宠物技能
		$file = fopen("../../txt/petskill.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 3)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  装备
		$file = fopen("../../txt/equipid.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 4)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  特技
		$file = fopen("../../txt/teji.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 5)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  特效
		$file = fopen("../../txt/texiao.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 6)");
		  }
		$i++;
		}
		fclose($file);
		//间隔
		sleep(0.2);
		//导入  套装
		$file = fopen("../../txt/taozhuang.txt", "r");
		$item=array();
		$i=0;
		while(! feof($file))
		{
		$item[$i]= fgets($file);
		$key = explode(';',$item[$i]);
		  if($key[0] != NULL || $key[0] != ''){
			$DB->query("insert into `items` (`itemid`,`name`,`type`) values ('".$key[0]."','".$key[1]."', 7)");
		  }
		$i++;
		}
		fclose($file);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('".$_SESSION['adminUser']."','操作同步物品表', NOW(), '$ip', '$city')");
		$checksqla = "UPDATE `items` SET `name` = replace (`name`,CHAR(9),'')";
		$checksqlb = "UPDATE `items` SET `name` = replace (`name`,CHAR(10),'')";
		$checksqlc = "UPDATE `items` SET `name` = replace (`name`,CHAR(13),'')";
		$checksqld = "UPDATE `items` SET `name` = replace (`name`,CHAR(32),'')";
		$checka = $DB->exec($checksqla);
		//间隔
		sleep(0.5);
		$checkb = $DB->exec($checksqlb);
		//间隔
		sleep(0.5);
		$checkc = $DB->exec($checksqlc);
		//间隔
		sleep(0.5);
		$checkd = $DB->exec($checksqld);
			exit('{"code":1,"msg":"同步成功，请刷新页面查看！"}');
    break;
    default:
        exit('{"code":-4,"msg":"No Act"}');
    break;
}
?>