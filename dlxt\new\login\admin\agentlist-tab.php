<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		}
	}else{
	$sql=" 1";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" 1";
}

$strtotime = strtotime("now"); 
$date = date('Y-m-d',$strtotime);
$lastdate = date('Y-m-d',$strtotime-86400);
$numrows=$DB->getColumn("SELECT count(*) from admin WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>账号</th>
                	<th>邀请码</th>
					<th>用户身份</th>
					<th>代理等级</th>
					<th>上级代理</th>
					<th>代理额度</th>
					<th>昨日变化额度</th>
					<th>今日变化额度</th>
					<th>分成信息</th>
					<th>玩家数量</th>
					<th>昨日应结算<br><small style="color:red">包含下级代理流水</small></th>
					<th>昨日流水<br><small style="color:red">包含下级代理流水</small></th>
					<th>今日流水<br><small style="color:red">包含下级代理流水</small></th>
					<th>总流水<br><small style="color:red">包含下级代理流水</small></th>
					<th>上次登陆IP</th>
					<th>上次登陆地点</th>
                	<th>查看其他</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM admin WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	$lastuid = explode(';',$res['lastuid']);
	$agents = explode('-',$lastuid[1]);
	$agentlv = $res['lv']==0?'管理员':$res['lv'].'级代理';
	//上级代理
	$uiduser = $DB->query("SELECT * FROM `admin` WHERE `id` = '".$lastuid[0]."' ")->fetch();
	$uidusername = $res['type']!=1?$uiduser['username']:'管理员';
	//玩家数量
	$playernums=$DB->getColumn("SELECT count(*) from `account` WHERE `agentid`='".$res['id']."'");
	
	//昨日流水
	$lastday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent` like '%[".$res['id']."]%' and `date`='".$lastdate."'");
	if($lastday == null){ $lastday = 0 ;}
	//今日流水
	$today=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent` like '%[".$res['id']."]%' and `date`='".$date."'");
	if($today == null){ $today = 0 ;}
	//总计流水
	$allmoney=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent` like '%[".$res['id']."]%' ");
	if($allmoney == null){ $allmoney = 0 ;}
	
	//昨日流水
	$lastmyday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent`like'%[".$res['id']."];%' and `date`='".$lastdate."'");
	if($lastmyday == null){ $lastmyday = 0 ;}
	//今日流水
	$mytoday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent`like'%[".$res['id']."];%' and `date`='".$date."'");
	if($mytoday == null){ $mytoday = 0 ;}
	//总计流水
	$myallmoney=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent`like'%[".$res['id']."];%' ");
	if($myallmoney == null){ $myallmoney = 0 ;}
	
	
	//$sql = "SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent` like '%[".$res['id']."]%' and `date`='".$lastdate."'";
	if($res['id']==1){
	$caozuos = '<a  href="./agentset.php?act=editmy" class="btn btn-w-xs btn-dark">修改信息</a>';
	}else{
	if($res['status']==1){
		$status = '<a href="javascript:fjAgent('.$res['id'].')" class="btn btn-w-xs btn-yellow">封禁</a>';
	}else{
		$status = '<a href="javascript:jfAgent('.$res['id'].')" class="btn btn-w-xs btn-danger">解封</a>';
	}
	$caozuos = '<a href="./agentset.php?act=editAgent&id='.$res['id'].'" class="btn btn-w-xs btn-brown">编辑</a><br><br>'.$status;
	}
	
	$eduday=$DB->getColumn("SELECT SUM(money) FROM `gm_order` WHERE `username`='".$res['username']."' and `date`='".$date."'");
	if($eduday == null){ $eduday = 0 ;}
	$edulastday=$DB->getColumn("SELECT SUM(money) FROM `gm_order` WHERE `username`='".$res['username']."' and `date`='".$lastdate."'");
	if($edulastday == null){ $edulastday = 0 ;}
echo '<tr>
<td><b>'.safe_html($res['id']).'</b></td>
<td>'.safe_html($res['username']).'</td>
<td>'.safe_html($res['invite']).'</td>
<td>'.($res['type']==1?'<span class="label label-dark">管理员</span>':'<span class="label label-cyan">代理</span>').'</td>
<td><span class="label label-danger">'.$agentlv.'</span></td>
<td><span class="label label-dark">'.$uidusername.'</span></td>
<td><span class="label label-brown">'.($res['type']==1?'不限制':$res['money'].'元').'</span></td>
<td><span class="label label-primary">'.($edulastday>0==1?'+'.$edulastday.'元':$edulastday.'元').'</span></td>
<td><span class="label label-info">'.($eduday>0?'+'.$eduday.'元':$eduday.'元').'</span></td>
<td>'.$res['fencheng'].'%</td>
<td><span class="label label-primary">'.$playernums.'</span></td>
<td><span class="label label-dark">包含下级：'.($lastday*$res['fencheng']/100).'元</span><br><br><small class="label label-secondary">个人：'.($lastday*$res['fencheng']/100).'元</small></td>
<td><span class="label label-brown">包含下级：'.$lastday.'元</span><br><br><small class="label label-secondary">个人：'.$lastmyday.'元</small></td>
<td><span class="label label-pink">包含下级：'.$today.'元</span><br><br><span class="label label-secondary">个人：'.$mytoday.'元</span></td>
<td><span class="label label-purple">包含下级：'.$allmoney.'元</span><br><br><span class="label label-secondary">个人：'.$myallmoney.'元</span></td>
<td>'.$res['ip'].'</td>
<td>'.$res['city'].'</td>
<td>
<a href="userlist.php?column=agentid&value='.$res['id'].'" class="btn btn-w-xs btn-primary">查看玩家</a><br><br>
<a href="agentlist.php?column=lastuid&like=1&value='.$res['id'].'" class="btn btn-w-xs btn-primary">下级代理</a>
</td>
<td>
'.$caozuos.'
</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
