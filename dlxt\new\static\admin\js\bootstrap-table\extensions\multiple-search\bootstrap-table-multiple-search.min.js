/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableMultipleSearch={exports:{}}.exports}})(this,function(){'use strict';!function(a){a.extend(a.fn.bootstrapTable.defaults,{multipleSearch:!1,delimeter:' '});var b=a.fn.bootstrapTable.Constructor,c=b.prototype.initSearch;b.prototype.initSearch=function(){if(this.options.multipleSearch){if(this.searchText===void 0)return;var b=this.searchText.split(this.options.delimeter),d=this,e=a.isEmptyObject(this.filterColumns)?null:this.filterColumns,f=[];if(1===b.length)c.apply(this,Array.prototype.slice.apply(arguments));else{for(var g,h=0;h<b.length;h++)g=b[h].trim(),f=g?a.grep(0===f.length?this.options.data:f,function(b,c){for(var e in b){e=a.isNumeric(e)?parseInt(e,10):e;var f=b[e],h=d.columns[d.fieldsColumnsIndex[e]],i=a.inArray(e,d.header.fields);h&&h.searchFormatter&&(f=a.fn.bootstrapTable.utils.calculateObjectValue(h,d.header.formatters[i],[f,b,c],f));var j=a.inArray(e,d.header.fields);if(-1!==j&&d.header.searchables[j]&&('string'==typeof f||'number'==typeof f))if(d.options.strictSearch){if((f+'').toLowerCase()===g)return!0;}else if(-1!==(f+'').toLowerCase().indexOf(g))return!0}return!1}):this.data;this.data=f}}else c.apply(this,Array.prototype.slice.apply(arguments))}}(jQuery)});