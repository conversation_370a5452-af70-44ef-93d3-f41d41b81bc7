<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%' and `type`='1'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}' and `type`='1'";
		}
	}else{
	$sql=" `type`='1'";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" `type`='1'";
}
$numrows=$DB->getColumn("SELECT count(*) from addup WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>物品名称</th>
                	<th>奖励图标</th>
					<th>物品ID</th>
					<th>物品数量</th>
					<th>所属档次</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM addup WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
		$addtypeData=$DB->getRow("SELECT * FROM `adduptype` WHERE `id` ='" . $res['lv'] . "' limit 1");
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['name'].'</td>
<td><img src="http://'.$_SERVER['HTTP_HOST'].$res['image'].'" style="width:60px;height:60px;" /></td>
<td><b>'.$res['itemid'].'</b></td>
<td><b>'.$res['num'].'</b></td>
<td>档次名称：'.$addtypeData['name'].'<br>领取要求：'.$addtypeData['value'].'元</td>
<td>
<a href="./shopset.php?act=editaddup&tid=1&id='.$res['id'].'" class="btn btn-w-xs btn-warning">编辑</a>&nbsp;
<a href="javascript:deleteaddup('.$res['id'].')" class="btn btn-w-xs btn-danger">删除</a>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
