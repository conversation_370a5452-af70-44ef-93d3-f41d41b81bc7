<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%' and `type`='1'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}' and `type`='1'";
		}
	}else{
	$sql=" `type`='1'";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" `type`='1'";
}
$numrows=$DB->getColumn("SELECT count(*) from zbdz WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>装备名称</th>
                	<th>装备ID</th>
                	<th>价格(平台币)</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM zbdz WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	switch ($res['type']){
		case 1:
			$type = '<span class="label label-dark">物品</span>';
		break;
		case 2:
			$type = '<span class="label label-danger">宠物</span>';
		break;
		case 3:
			$type = '<span class="label label-secondary">宠物技能</span>';
		break;
		case 4:
			$type = '<span class="label label-warning">装备</span>';
		break;
		case 5:
			$type = '<span class="label label-info">特技</span>';
		break;
		case 6:
			$type = '<span class="label label-success">特效</span>';
		break;
		case 7:
			$type = '<span class="label label-primary">套装</span>';
		break;
		default:
			$type = '<span class="label label-default">未分类</span>';
	}
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['name'].'</td>
<td>'.$res['itemid'].'</td>
<td>'.$res['price'].'元</td>
<td>
<a href="./zbdzset.php?act=editzbdzitem&id='.$res['id'].'" class="btn btn-w-xs btn-warning">编辑</a>&nbsp;
<a href="javascript:deletezbdzitem('.$res['id'].')" class="btn btn-w-xs btn-danger">删除</a>
</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
