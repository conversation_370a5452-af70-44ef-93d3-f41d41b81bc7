/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableI18nEnhance={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b=a.fn.bootstrapTable.Constructor;b.prototype.changeTitle=function(b){a.each(this.options.columns,function(c,d){a.each(d,function(a,c){c.field&&(c.title=b[c.field])})}),this.initHeader(),this.initBody(),this.initToolbar()},b.prototype.changeLocale=function(a){this.options.locale=a,this.initLocale(),this.initPagination(),this.initBody(),this.initToolbar()},a.fn.bootstrapTable.methods.push('changeTitle'),a.fn.bootstrapTable.methods.push('changeLocale')}(jQuery)});