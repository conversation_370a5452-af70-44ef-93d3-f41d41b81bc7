<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Select2 -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='addAgent'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addAgent(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="" placeholder="请输入6-18位账号" />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="请输入6-18位密码" />
            </div>
			<div class="form-group col-md-12">
				<label>上级代理</label>
				<select name="lastuid" class="form-control select2" id="lastuid" >
				<?php
				$rs=$DB->query("SELECT * FROM admin order by id");
				while($res = $rs->fetch())
				{
				$lastuid = explode(';',$res['lastuid']);
				$agents = explode('-',$lastuid[1]);
				$agentlv = $res['lv']==0?'管理员':$res['lv'].'级代理';
				echo '<option value="'.$res['id'].'">'.$agentlv.'-'.$res['username'].'-'.$res['fencheng'].'%</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#lastuid').select2( {placeholder: '请选择'} );</script>
			</div>
           
            <div class="form-group col-md-12">
              <label>分成信息</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="fencheng" placeholder="请输入分成信息" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">%</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='paymoney'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return paymoney(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>充值代理</label>
				<select name="agentid" class="form-control select2" id="agentid" >
				<?php
				$rs=$DB->query("SELECT * FROM `admin` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#agentid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>充值额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" value="" placeholder="请输入充值额度" >
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editmy'){
	$adminUser = $_SESSION['adminUser'];
	$adminData = $Admin->getAdmin($username);
	if(!$adminData){echo "<script>layer.ready(function(){layer.msg('该账户信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editmy(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号(修改账号必须同时重设密码)</label>
              <input type="text" class="form-control"  name="username" value="<?php echo $adminData['username']; ?>" placeholder="请输入6-18位账号" />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="不修改请留空，请输入6-18位密码" />
            </div>
           
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editAgent'){
	$id = intval($get['id']);
	$checkadmin = $Admin->getAdminId($id);
	if($id==1){
	echo "<script>layer.ready(function(){layer.msg('总后台账户请在右上角头像处修改', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();
		
	}
	if(!$checkadmin){echo "<script>layer.ready(function(){layer.msg('该代理信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editAgent(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>账号</label>
              <input type="text" class="form-control"  name="username" value="<?php echo $checkadmin['username']; ?>" placeholder="请输入6-18位账号" readonly />
            </div>
            <div class="form-group col-md-12">
              <label>密码</label>
              <input type="text" class="form-control" name="password" value="" placeholder="不修改请留空" />
            </div>
			<div class="form-group col-md-12">
				<label>上级代理</label>
				<select name="lastuid" class="form-control select2" id="lastuid" >
				<?php
				$lastuid = explode(';',$checkadmin['lastuid']);
				$checkadmins = $Admin->getAdminId($lastuid[0]);
				$agentlv = $checkadmins['lv']==0?'管理员':$checkadmins['lv'].'级代理';
				echo '<option value="'.$lastuid[0].'"  selected = "selected">　【当前】'.$agentlv.'-'.$checkadmins['username'].'-'.$checkadmins['fencheng'].'%</option>';
				$rs=$DB->query("SELECT * FROM admin order by id");
				while($res = $rs->fetch())
				{
				if($res['id']!= $id){
				$agentlv = $res['lv']==0?'管理员':$res['lv'].'级代理';
				echo '<option value="'.$res['id'].'">　'.$agentlv.'-'.$res['username'].'-'.$res['fencheng'].'%</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#lastuid').select2( {placeholder: '请选择'} );</script>
			</div>
           
            <div class="form-group col-md-12">
              <label>代理额度</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" value="<?php echo $checkadmin['money']; ?>" placeholder="请输入代理额度" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>分成信息</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="fencheng" value="<?php echo $checkadmin['fencheng']; ?>" placeholder="请输入分成信息" aria-describedby="baifenbi">
					<span class="input-group-addon" id="baifenbi">%</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
  
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function paymoney(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=paymoney',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editmy(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editmy',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addAgent(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addAgent',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editAgent(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editAgent',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>