<?php
include "config.php";
require_once("lib/epay_notify.class.php");
function get($url,$postdata){
		$ch = curl_init(); 
		curl_setopt($ch, CURLOPT_URL, $url.'?'.http_build_query($postdata)); 
		curl_setopt($ch, CURLOPT_HEADER, 0); 
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); 
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); 
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); 
		curl_setopt($ch, CURLOPT_TIMEOUT, 10);
		$output = curl_exec($ch);
		$errorCode = curl_errno($ch);
		curl_close($ch);
		if(0 !== $errorCode){
			return '发送失败';
		}
		$return=json_decode($output,true);
		if($return['code']=='0000'){
			return '发送成功';
		}else{
			return '发送失败';
		}
	}
//计算得出通知验证结果
$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();

if ($verify_result) {//验证成功
    //订单号
    $out_trade_no = $_GET['out_trade_no'];

    //交易状态
    $trade_status = $_GET['trade_status'];

    //交易金额
    $money = $_GET['money'];

    //支付时间
    $date = date("Y-m-d H:i:s");

		if ($_GET['trade_status'] == "TRADE_SUCCESS") {
		$order = "select * from `orderid` where orderid='$out_trade_no'";
		$orderids = mysql_fetch_array(mysql_query($order));
		
		$num=$orderids['money']*$bili;
		$time=time();
		$orderid='SD'.$time;
		$data=array(
					'pOrderId'=>$orderid,
					'userId'=>0,
					'creditId'=>$orderids['uid'],
					'currency'=>'CNY',
					'amount'=>$num,
					'RCurrency'=>'CNY',
					'RAmount'=>$num,
					'gameCode'=>'twzx',
					'serverCode'=>$orderids['code'],
					'stone'=>$num,
					'stoneType'=>'diamond',
					'md5Str'=>'wBv78qM7QeEhJ6BGeZAwBStQVMKmTNG1f2QIDAQAB',
					'time'=>$time,
					'productId'=>'tw.zx.1usd',
					'activityExtra'=>0,
					'orderStateMonth'=>0,
					'point'=>0,
					'freePoint'=>1,
					'payType'=>'platform'
				);
				$result=get($orderids['url'],$data);
			$sql = "UPDATE `orderid` SET `info`='$result' WHERE (`orderid`='$out_trade_no')";
			if(mysql_query($sql)){
            echo "success";	
			exit;
			}
}
} else {
    echo "验证失败";
}
?>