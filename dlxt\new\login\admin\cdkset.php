<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='create'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return create(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>CDK面值（如价值5元，使用此CDK，玩家会得到各项奖励比例*5的数量）</label>
				<div class="input-group m-b-10">
					<input type="number" name="money" class="form-control" value="" placeholder="请输入生成的CDK面值">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <label>CDK数量</label>
              <input type="number" class="form-control" name="number" value="" placeholder="请输入生成的CDK数量" />
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认生成</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='config'){
//CDK领取设置
$cdkset=$DB->getRow("select * from `config` where `keys`='cdkset' limit 1");
$cdkset = explode(';', $cdkset['values']);
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
        <div class="tab-content">
          <div class="tab-pane active">
          <form onsubmit="return config(this)" method="post" class="row">
              <div class="form-group">
                <b class="help-block" style="color:red">各奖励比例跟随大区设置，请前往【服务器管理】-【区服列表】查看</b>
				<br>
                <label for="wipe_cache_type">CDK玩家领取设置</label>
                <small class="help-block">此页面设置玩家领取CDK时候的效果</small>
                <div class="controls-box">
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="ptb" <?php echo ($cdkset[0]=='on'?'checked':''); ?>><span>平台币</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="charge" <?php echo ($cdkset[1]=='on'?'checked':''); ?>><span>累计奖励</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="xianyu" <?php echo ($cdkset[2]=='on'?'checked':''); ?>><span>仙玉奖励</span>
                  </label>
                  <label class="lyear-checkbox checkbox-inline checkbox-primary">
                    <input type="checkbox" name="vip" <?php echo ($cdkset[3]=='on'?'checked':''); ?>><span>VIP经验</span>
                  </label>
				  <br>
				  <br>
                </div>
              </div>
              <div class="form-group">
                <button type="submit" class="btn btn-primary m-r-5">确 定</button>
                <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
              </div>
          </form>
		  </div> 
        </div>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function create(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=cdk&type=create',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function config(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=cdk&type=config',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>