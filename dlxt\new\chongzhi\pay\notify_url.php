<?php
/* *
 * 功能：彩虹易支付异步通知页面
 * 说明：
 * 以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己网站的需要，按照技术文档编写,并非一定要使用该代码。
 */

require_once("config.php");
require_once("lib/EpayCore.class.php");

//计算得出通知验证结果
$epay = new EpayCore($epay_config);
$verify_result = $epay->verifyNotify();

if($verify_result) {//验证成功

	//商户订单号
	$out_trade_no = $get['out_trade_no'];

	//彩虹易支付交易号
	$trade_no = $get['trade_no'];

	//交易状态
	$trade_status = $get['trade_status'];

	//支付方式
	$type = $get['type'];

	//支付金额
	$money = $get['money'];

	if ($get['trade_status'] == 'TRADE_SUCCESS') {
		//判断该笔订单是否在商户网站中已经做过处理
		//如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
		//如果有做过处理，不执行商户的业务程序
        //$orderData = $DB->getRow("select * from `user_order` where `orderid`='$out_trade_no' ");
        $payorderData = $DBDL->getRow("select * from `pay_order` where `orderid`='$out_trade_no' ");
        if($payorderData['status']!=0)exit('订单已完成');
		//$info = explode('-',$get['name']);
		$param =$payorderData['param'];
		$info = explode('-',$param);
		$point = $goods_info[$info[1]][3];
		
		$paytype = $goods_info[$info[1]][5];
		$account = $info[0];
		$roleid = $info[2];
		
		
		
        $filePath = 'zhifu.txt';
        $var1 = $roleid;
        $var2 = $point;
        $var3 = $paytype;
        $var4 = "0";
        $var5 = $out_trade_no;
        
        // 将变量内容拼接成新的字符串
        $newContent = "$var1,$var2,$var3,$var4,$var5;";
        
        // 追加内容到文件
        if (file_put_contents($filePath, $newContent, FILE_APPEND) !== false) {
            //echo "内容已成功追加到文件: " . $filePath;
        } else {
            echo "追加内容失败: " . $filePath;
        }
        $statusupsql = "UPDATE `pay_order` SET `status`='1' WHERE `orderid`='".$out_trade_no."' ";
		$statusupsql = $DBDL->exec($statusupsql) ;
		
		
	}

	//验证成功返回
	echo "success";
}
else {
	//验证失败
	echo "fail";
}
?>