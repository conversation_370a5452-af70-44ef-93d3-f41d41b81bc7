<?php
include './common/main.php';

// 支付配置
$epay_config = array(
    'apiurl' => 'https://sheng.nachengweb.com/',
    'pid' => '1093',
    'key' => '72jJHMDjjYBqMmZjMjrR7mbt27Zu3Mjy'
);

// 获取回调参数
$pid = $_GET['pid'];
$trade_no = $_GET['trade_no'];
$out_trade_no = $_GET['out_trade_no'];
$type = $_GET['type'];
$name = $_GET['name'];
$money = $_GET['money'];
$trade_status = $_GET['trade_status'];
$sign = $_GET['sign'];

// 验证签名
$params = array(
    'pid' => $pid,
    'trade_no' => $trade_no,
    'out_trade_no' => $out_trade_no,
    'type' => $type,
    'name' => $name,
    'money' => $money,
    'trade_status' => $trade_status
);

ksort($params);
$signstr = '';
foreach($params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$verify_sign = md5($signstr);

//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果 - <?php echo $title['values'];?></title>
    <link rel="stylesheet" type="text/css" href="/static/login/vendor/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="/static/login/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .success-icon {
            color: #28a745;
            font-size: 60px;
            margin-bottom: 20px;
        }
        .error-icon {
            color: #dc3545;
            font-size: 60px;
            margin-bottom: 20px;
        }
        .result-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .result-message {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .order-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .order-info h5 {
            margin-bottom: 15px;
            color: #333;
        }
        .order-info p {
            margin: 5px 0;
            color: #666;
        }
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn-custom {
            padding: 12px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            color: white;
            text-decoration: none;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        .countdown {
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="result-card">
        <?php if($verify_sign == $sign && $trade_status == 'TRADE_SUCCESS'): ?>
            <i class="fa fa-check-circle success-icon"></i>
            <h2 class="result-title" style="color: #28a745;">支付成功！</h2>
            <p class="result-message">
                恭喜您，支付已完成！充值将在几分钟内到账，请返回游戏查看。
            </p>
            
            <div class="order-info">
                <h5><i class="fa fa-list-alt"></i> 订单信息</h5>
                <p><strong>订单号：</strong><?php echo $out_trade_no; ?></p>
                <p><strong>支付金额：</strong>￥<?php echo $money; ?></p>
                <p><strong>商品名称：</strong><?php echo $name; ?></p>
                <p><strong>支付方式：</strong><?php echo $type == 'alipay' ? '支付宝' : '微信支付'; ?></p>
                <p><strong>交易号：</strong><?php echo $trade_no; ?></p>
            </div>
            
        <?php else: ?>
            <i class="fa fa-times-circle error-icon"></i>
            <h2 class="result-title" style="color: #dc3545;">支付失败</h2>
            <p class="result-message">
                很抱歉，支付验证失败。如果您已经完成支付，请联系客服处理。
            </p>
            
            <div class="order-info">
                <h5><i class="fa fa-exclamation-triangle"></i> 错误信息</h5>
                <p><strong>订单号：</strong><?php echo $out_trade_no; ?></p>
                <p><strong>状态：</strong>签名验证失败</p>
                <p><strong>建议：</strong>请联系客服或重新发起支付</p>
            </div>
        <?php endif; ?>
        
        <div class="btn-group">
            <a href="chongzhi.php" class="btn-custom btn-primary">
                <i class="fa fa-credit-card"></i> 继续充值
            </a>
            <a href="index.php" class="btn-custom btn-secondary">
                <i class="fa fa-home"></i> 返回首页
            </a>
        </div>
        
        <div class="countdown">
            <span id="countdown">5</span> 秒后自动跳转到首页
        </div>
    </div>

    <script>
        // 倒计时跳转
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = 'index.php';
            }
        }, 1000);
    </script>
</body>
</html>
