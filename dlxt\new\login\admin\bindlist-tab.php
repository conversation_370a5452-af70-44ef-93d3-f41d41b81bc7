<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		}
	}else{
	$sql=" 1";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" 1";
}
$numrows=$DB->getColumn("SELECT count(*) from binds WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>所属账号</th>
                	<th>角色名</th>
					<th>角色ID</th>
					<th>所属大区</th>
					<th>平台币</th>
                	<th>角色累计</th>
                	<th>今日累计</th>
                	<th>查看背包</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM `binds` WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	$servercheck = $DB->query("SELECT * FROM `servers` WHERE `id` = '".$res['serverid']."' ")->fetch();
	$usercheck = $DB->query("SELECT * FROM `account` WHERE `id` = '".$res['userid']."' ")->fetch();
	if($res['lastday']==$date){
		$daycharge = $res['daycharge'];
	}else{
		$daycharge = 0.00;
	}
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$usercheck['username'].'</td>
<td>'.$res['name'].'</td>
<td>'.$res['roleid'].'</td>
<td>'.$servercheck['name'].'</td>
<td><span class="label label-dark">'.$res['money'].'元</span></td>
<td><span class="label label-brown">'.$res['charge'].'元</span></td>
<td><span class="label label-brown">'.$daycharge.'元</span></td>
<td>
<a href="rolebag.php?column=bindsid&value='.$res['id'].'" class="btn btn-w-xs btn-primary">查看背包</a>
</td>
<td>
<a href="./userset.php?act=bindEdit&id='.$res['id'].'" class="btn btn-w-xs btn-warning">编辑</a>&nbsp;
<a href="javascript:delbind('.$res['id'].')" class="btn btn-w-xs btn-danger">删除</a>
</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
