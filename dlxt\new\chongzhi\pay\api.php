<!DOCTYPE html>
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>正在为您跳转到支付页面，请稍候...</title>
	<style type="text/css">
body{margin:0;padding:0}
p{position:absolute;left:50%;top:50%;height:35px;margin:-35px 0 0 -160px;padding:20px;font:bold 16px/30px "宋体",Arial;text-indent:40px;border:1px solid #c5d0dc}
#waiting{font-family:Arial}
	</style>
</head>
<body>
<?php

include "config.php";
require_once("lib/EpayCore.class.php");

// 获取客户端IP
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$strtotime = strtotime("now");
$date = date('Y-m-d',$strtotime);
$time = date('H:i:s',$strtotime);
$ip = get_client_ip();
$city = '未知';

$strtotime = strtotime("now"); 
function short_md5($str) 
{
	return substr(md5($str), 8, 16);
}

// 添加订单号生成函数
function generate_trade_no() {
	$strtotime = time();
	$year = date('Y', $strtotime);
	$month = date('m', $strtotime);
	$day = date('d', $strtotime);
	$hour = date('H', $strtotime);
	$minute = date('i', $strtotime);
	$second = date('s', $strtotime);
	$microtime = substr(microtime(), 2, 5);
	$random = rand(1000, 9999);
	
	return 'Pay' . $year . $month . $day . $hour . $minute . $second . $microtime . $random;
}

//异步回调
$notify_url = "http://".$_SERVER['HTTP_HOST']."/pay/notify_url.php";
//同步回调
$return_url = "http://".$_SERVER['HTTP_HOST']."/pay/return_url.php";
//商户订单号
$out_trade_no = generate_trade_no();
$type = $post['type'];
/*
account: 1234567
server: 生日快乐
role: 相伴、御战
roleid: 48904
qu: 1
text: 充值赠送100万仙玉
type: wxpay
Array ( [account] => cxy1234 [role] => 谭盼 [server] => 梦回大唐 [roleid] => 10003 [goods] => 12 [type] => wxpay )

print_r($post);die();
*/



$account=$post['account'];
$info = base64_encode('?server='.urlencode($post['server']).'&role='.urlencode($post['role']).'&roleid='.$post['roleid'].'&account='.$post['account']);

// 使用 PdoHelper 的 query 方法
$accountData = $DBDL->getRow("SELECT * FROM `account` WHERE `username` = :username", [':username' => $account]);

// if(!$accountData) {
//     exit('账号不存在');
// }

// 验证商品ID合法性
if(!isset($goods_info[$post['goods']]) || !is_numeric($post['goods'])) {
    exit('非法商品');
}

$money = $goods_info[$post['goods']][2];
$xianyu = $goods_info[$post['goods']][3];
$name = $account.'-'.$post['goods'].'-'.$post['roleid'];

// 验证金额合法性
if(!is_numeric($money) || $money <= 0 || $money > 10000) {
    exit('非法金额');
}

// 检查订单是否存在
$existOrder = $DBDL->getRow("SELECT id FROM pay_order WHERE orderid = :orderid", [':orderid' => $out_trade_no]);
if($existOrder) {
    exit('订单号重复');
}

//构建代理系统订单
$agentid = $accountData['agentid'];
if ($agentid == '') {
    $agentid = 1;
}
$checkAgent = $DBDL->query("SELECT * FROM `admin` WHERE `id` = '$agentid' ")->fetch();
if(!$checkAgent){
    //exit('上级代理信息错误');
}

$agents = explode(';',$checkAgent['lastuid']);
$agent = '['.$checkAgent['id'].'];'.$agents[1];

// 使用 PdoHelper 的 exec 方法插入订单
$DBDL->exec("INSERT INTO `pay_order` (`orderid`,`ordertype`,`value`,`user`,`roleid`,`rolename`,`qu`,`agent`,`money`,`status`,`ip`,`city`,`date`,`time`,`param`)
    VALUES (:orderid, :ordertype, :value, :user, :roleid, :rolename, :qu, :agent, :money, :status, :ip, :city, :date, :time, :param)",
    [
        ':orderid' => $out_trade_no,
        ':ordertype' => '1',
        ':value' => '00',
        ':user' => $account,
        ':roleid' => $post['roleid'],
        ':rolename' => $post['role'],
        ':qu' => $post['server'],
        ':agent' => $agent,
        ':money' => $money,
        ':status' => '0',
        ':ip' => $ip,
        ':city' => $city,
        ':date' => $date,
        ':time' => $time,
        ':param' => $name
    ]
);

/************************************************************/

//构造要请求的参数数组，无需改动
$parameter = array(
	"pid" => $epay_config['pid'],
	"type" => $type,
	"notify_url" => $notify_url,
	"return_url" => $return_url,
	"out_trade_no" => $out_trade_no,
	"name" => $name,
	"money"	=> $money,
);

//建立请求
$epay = new EpayCore($epay_config);
$html_text = $epay->pagePay($parameter);
echo $html_text;

?>
<p>正在为您跳转到支付页面，请稍候...</p>
</body>
</html>
	