<?php
/* *
 * 移动端支付跳转处理器
 * 功能：处理移动端支付URL scheme跳转问题
 * 解决：ERR_UNKNOWN_URL_SCHEME 错误
 */

/**
 * 检测用户设备类型
 * @return string 设备类型：mobile, pc
 */
function detectDevice() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
    
    $mobileKeywords = array(
        'mobile', 'android', 'iphone', 'ipad', 'ipod', 
        'blackberry', 'windows phone', 'opera mini', 
        'iemobile', 'mobile safari'
    );
    
    foreach ($mobileKeywords as $keyword) {
        if (strpos($userAgent, $keyword) !== false) {
            return 'mobile';
        }
    }
    
    return 'pc';
}

/**
 * 检测是否在微信或QQ内置浏览器中
 * @return bool
 */
function isInAppBrowser() {
    $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
    return (strpos($userAgent, 'micromessenger') !== false || 
            strpos($userAgent, 'qq/') !== false);
}

/**
 * 生成移动端友好的支付跳转页面
 * @param string $payUrl 支付URL
 * @param string $payType 支付类型 (alipay/wxpay)
 * @param array $orderInfo 订单信息
 * @return string HTML内容
 */
function generateMobilePayPage($payUrl, $payType, $orderInfo) {
    $device = detectDevice();
    $isInApp = isInAppBrowser();

    // 特殊处理：如果是微信支付且在微信内置浏览器中，直接跳转
    if ($payType === 'wxpay' && $isInApp && strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
        return generateWeChatInAppPayPage($payUrl, $payType, $orderInfo);
    }

    // 如果是在其他内置浏览器中，显示提示页面
    if ($isInApp) {
        return generateInAppTipPage($payUrl, $payType, $orderInfo);
    }

    // 如果是移动端，生成移动端优化的跳转页面
    if ($device === 'mobile') {
        return generateMobileOptimizedPage($payUrl, $payType, $orderInfo);
    }

    // PC端正常跳转
    return generatePCPayPage($payUrl, $payType, $orderInfo);
}

/**
 * 生成微信内置浏览器支付页面（直接跳转）
 */
function generateWeChatInAppPayPage($payUrl, $payType, $orderInfo) {
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付</title>
    <style>
        body { margin: 0; padding: 20px; background: linear-gradient(135deg, #1aad19 0%, #00d4aa 100%); font-family: Arial, sans-serif; min-height: 100vh; }
        .container { max-width: 400px; margin: 50px auto; background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #1aad19; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .title { font-size: 18px; color: #333; margin-bottom: 15px; }
        .wechat-icon { font-size: 48px; margin-bottom: 20px; }
        .order-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; }
        .order-info div { margin-bottom: 8px; color: #555; }
        .order-info strong { color: #333; }
        .tips { font-size: 14px; color: #666; line-height: 1.5; margin-top: 20px; }
        .manual-btn { display: inline-block; padding: 12px 24px; background: #1aad19; color: white; text-decoration: none; border-radius: 8px; margin: 10px 5px; }
        .manual-btn:hover { background: #179b16; }
        .manual-btn:visited { color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="wechat-icon">💚</div>
        <div class="spinner"></div>
        <div class="title">正在启动微信支付...</div>

        <div class="order-info">
            <div><strong>订单号：</strong>' . htmlspecialchars($orderInfo['out_trade_no']) . '</div>
            <div><strong>支付金额：</strong>￥' . htmlspecialchars($orderInfo['money']) . '</div>
            <div><strong>商品名称：</strong>' . htmlspecialchars($orderInfo['name']) . '</div>
        </div>

        <div class="tips">
            正在为您跳转到微信支付页面，请稍候...
        </div>

        <div id="manualOption" style="display: none;">
            <a href="' . htmlspecialchars($payUrl) . '" class="manual-btn">手动打开支付页面</a>
        </div>
    </div>

    <script>
        // 立即跳转到微信支付
        setTimeout(function() {
            try {
                window.location.href = "' . addslashes($payUrl) . '";
            } catch (e) {
                console.log("跳转失败:", e);
                document.getElementById("manualOption").style.display = "block";
            }
        }, 500);

        // 备用方案：如果3秒后还没跳转，显示手动按钮
        setTimeout(function() {
            document.getElementById("manualOption").style.display = "block";
        }, 3000);
    </script>
</body>
</html>';
}

/**
 * 生成内置浏览器提示页面
 */
function generateInAppTipPage($payUrl, $payType, $orderInfo) {
    $payTypeName = ($payType === 'alipay') ? '支付宝' : '微信';
    
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请在浏览器中打开</title>
    <style>
        body { margin: 0; padding: 20px; background: #f5f5f5; font-family: Arial, sans-serif; }
        .container { max-width: 400px; margin: 50px auto; background: white; border-radius: 10px; padding: 30px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .icon { font-size: 48px; color: #ff6b6b; margin-bottom: 20px; }
        .title { font-size: 20px; color: #333; margin-bottom: 15px; }
        .message { color: #666; line-height: 1.6; margin-bottom: 25px; }
        .steps { text-align: left; background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .steps ol { margin: 0; padding-left: 20px; }
        .steps li { margin-bottom: 8px; color: #555; }
        .url-box { background: #e9ecef; padding: 10px; border-radius: 5px; word-break: break-all; font-size: 12px; color: #666; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⚠️</div>
        <div class="title">无法在当前环境中打开' . $payTypeName . '</div>
        <div class="message">
            检测到您正在微信或QQ内置浏览器中访问，为了正常完成支付，请按以下步骤操作：
        </div>
        <div class="steps">
            <ol>
                <li>点击右上角的 <strong>···</strong> 菜单</li>
                <li>选择 <strong>"在浏览器中打开"</strong></li>
                <li>或复制下方链接到浏览器中打开</li>
            </ol>
        </div>
        <div class="url-box">' . htmlspecialchars($payUrl) . '</div>
        <a href="' . htmlspecialchars($payUrl) . '" class="btn">尝试打开支付页面</a>
        <a href="javascript:history.back()" class="btn" style="background: #6c757d;">返回上一页</a>
    </div>
</body>
</html>';
}

/**
 * 生成移动端优化的支付页面（带二维码备用方案）
 */
function generateMobileOptimizedPage($payUrl, $payType, $orderInfo) {
    $payTypeName = ($payType === 'alipay') ? '支付宝' : '微信';
    $payTypeColor = ($payType === 'alipay') ? '#1677ff' : '#1aad19';
    $payTypeIcon = ($payType === 'alipay') ? '🔵' : '💚';

    // 检测是否为URL scheme
    $isUrlScheme = (strpos($payUrl, 'weixin://') === 0 || strpos($payUrl, 'alipays://') === 0);

    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $payTypeName . '支付</title>
    <style>
        body { margin: 0; padding: 20px; background: linear-gradient(135deg, ' . $payTypeColor . ' 0%, #f8f9fa 100%); font-family: Arial, sans-serif; min-height: 100vh; }
        .container { max-width: 400px; margin: 20px auto; background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid ' . $payTypeColor . '; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .title { font-size: 18px; color: #333; margin-bottom: 15px; }
        .pay-icon { font-size: 32px; margin-bottom: 10px; }
        .order-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; }
        .order-info div { margin-bottom: 8px; color: #555; }
        .order-info strong { color: #333; }
        .tips { font-size: 14px; color: #666; line-height: 1.5; margin-top: 20px; }
        .manual-btn { display: inline-block; padding: 12px 24px; background: ' . $payTypeColor . '; color: white; text-decoration: none; border-radius: 8px; margin: 10px 5px; text-decoration: none; }
        .manual-btn:hover { opacity: 0.8; }
        .manual-btn:visited { color: white; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107; }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #28a745; }
        .copy-btn { background: #6c757d; font-size: 12px; padding: 8px 16px; }
        .url-display { background: #e9ecef; padding: 10px; border-radius: 5px; word-break: break-all; font-size: 12px; margin: 10px 0; }
        .qr-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid ' . $payTypeColor . '; display: none; }
        .qr-image { width: 200px; height: 200px; margin: 0 auto; display: block; border: 1px solid #ddd; border-radius: 8px; }
        .fallback-section { margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 8px; display: none; }
        .step-indicator { background: #007bff; color: white; border-radius: 50%; width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; font-size: 12px; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="pay-icon">' . $payTypeIcon . '</div>
        <div id="loadingSection">
            <div class="spinner"></div>
            <div class="title">正在启动' . $payTypeName . '支付...</div>
        </div>

        <div class="order-info">
            <div><strong>订单号：</strong>' . htmlspecialchars($orderInfo['out_trade_no']) . '</div>
            <div><strong>支付金额：</strong>￥' . htmlspecialchars($orderInfo['money']) . '</div>
            <div><strong>商品名称：</strong>' . htmlspecialchars($orderInfo['name']) . '</div>
        </div>

        <!-- 智能支付处理区域 -->
        <div id="smartPaySection">
            <div class="success">
                <span class="step-indicator">1</span>
                <strong>正在尝试打开' . $payTypeName . 'APP...</strong>
            </div>

            <div id="manualOptions" style="display: none;">
                <a href="' . htmlspecialchars($payUrl) . '" class="manual-btn">手动打开' . $payTypeName . '</a>
                <button onclick="copyToClipboard()" class="manual-btn copy-btn">复制链接</button>
                <div class="url-display" id="payUrl" style="display: none;">' . htmlspecialchars($payUrl) . '</div>
            </div>
        </div>

        <!-- 二维码备用方案 -->
        <div id="qrFallback" class="fallback-section">
            <div class="warning">
                <span class="step-indicator">2</span>
                <strong>备用方案：扫码支付</strong><br>
                如果无法打开' . $payTypeName . 'APP，请使用下方二维码扫码支付
            </div>

            <div class="qr-container" id="qrContainer">
                <div id="qrcode"></div>
            </div>

            <div class="tips">
                <p><strong>📱 扫码步骤：</strong></p>
                <p>1. 打开' . $payTypeName . 'APP，点击"扫一扫"</p>
                <p>2. 扫描上方二维码完成支付</p>
            </div>
        </div>

        <div class="tips">
            <p>💡 系统将自动尝试打开' . $payTypeName . 'APP，如果失败将显示二维码</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        const payUrl = "' . addslashes($payUrl) . '";
        const payType = "' . ($payType === 'alipay' ? 'alipay' : 'wxpay') . '";
        let appOpenAttempted = false;

        // 尝试打开APP
        function attemptAppOpen() {
            if (appOpenAttempted) return;
            appOpenAttempted = true;

            console.log("尝试打开APP:", payUrl);

            // 创建隐藏的iframe尝试打开APP
            const iframe = document.createElement("iframe");
            iframe.style.display = "none";
            iframe.src = payUrl;
            document.body.appendChild(iframe);

            // 设置超时，如果APP没有打开，显示备用选项
            setTimeout(function() {
                document.body.removeChild(iframe);
                showFallbackOptions();
            }, 3000);

            // 监听页面可见性变化，如果用户切换到APP再回来，说明APP打开成功
            let hidden = false;
            const handleVisibilityChange = function() {
                if (document.hidden) {
                    hidden = true;
                } else if (hidden) {
                    // 用户从APP回来了，可能支付成功
                    console.log("用户从APP返回，可能已完成支付");
                }
            };
            document.addEventListener("visibilitychange", handleVisibilityChange);
        }

        // 显示备用选项
        function showFallbackOptions() {
            document.getElementById("loadingSection").style.display = "none";
            document.getElementById("manualOptions").style.display = "block";
            document.getElementById("qrFallback").style.display = "block";
            document.getElementById("qrContainer").style.display = "block";

            // 生成二维码
            generateQRCode();
        }

        // 生成二维码
        function generateQRCode() {
            const qrContainer = document.getElementById("qrcode");
            if (window.QRCode) {
                QRCode.toCanvas(qrContainer, payUrl, {
                    width: 200,
                    height: 200,
                    margin: 2,
                    color: {
                        dark: "#000000",
                        light: "#FFFFFF"
                    }
                }, function(error) {
                    if (error) {
                        console.error("二维码生成失败:", error);
                        qrContainer.innerHTML = "<p style=\"color: #dc3545;\">二维码生成失败，请使用手动链接</p>";
                    }
                });
            } else {
                // 备用方案：使用在线二维码生成服务
                const qrImageUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" + encodeURIComponent(payUrl);
                qrContainer.innerHTML = "<img src=\"" + qrImageUrl + "\" alt=\"支付二维码\" style=\"width: 200px; height: 200px;\" onerror=\"this.style.display=\'none\'; this.parentNode.innerHTML=\'<p style=\\\"color: #dc3545;\\\">二维码加载失败</p>\'\">";
            }
        }

        // 复制功能
        function copyToClipboard() {
            const urlElement = document.getElementById("payUrl");
            urlElement.style.display = "block";

            const textArea = document.createElement("textarea");
            textArea.value = payUrl;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                document.execCommand("copy");
                alert("链接已复制到剪贴板，请在' . $payTypeName . 'APP中粘贴打开");
            } catch (err) {
                alert("复制失败，请手动复制下方显示的链接");
            }

            document.body.removeChild(textArea);
        }

        // 页面加载完成后开始处理
        window.addEventListener("load", function() {
            setTimeout(attemptAppOpen, 1000);

            // 如果5秒后还没有显示备用选项，强制显示
            setTimeout(function() {
                if (document.getElementById("qrFallback").style.display === "none") {
                    showFallbackOptions();
                }
            }, 5000);
        });

        // 错误处理
        window.addEventListener("error", function(e) {
            console.log("检测到错误:", e.message);
            if (e.message && e.message.indexOf("ERR_UNKNOWN_URL_SCHEME") !== -1) {
                showFallbackOptions();
            }
        });
    </script>
</body>
</html>';
    } else {
        // 普通HTTP链接处理 - 也添加二维码备用方案
        return $html . '
        <!-- 智能支付处理区域 -->
        <div id="smartPaySection">
            <div class="success">
                <span class="step-indicator">1</span>
                <strong>正在跳转到' . $payTypeName . '支付页面...</strong>
            </div>

            <div id="manualOptions" style="display: none;">
                <a href="' . htmlspecialchars($payUrl) . '" class="manual-btn">手动打开支付页面</a>
                <button onclick="copyToClipboard()" class="manual-btn copy-btn">复制链接</button>
                <div class="url-display" id="payUrl" style="display: none;">' . htmlspecialchars($payUrl) . '</div>
            </div>
        </div>

        <!-- 二维码备用方案 -->
        <div id="qrFallback" class="fallback-section">
            <div class="warning">
                <span class="step-indicator">2</span>
                <strong>备用方案：扫码支付</strong><br>
                如果页面跳转失败，请使用下方二维码扫码支付
            </div>

            <div class="qr-container" id="qrContainer">
                <div id="qrcode"></div>
            </div>

            <div class="tips">
                <p><strong>📱 扫码步骤：</strong></p>
                <p>1. 打开' . $payTypeName . 'APP，点击"扫一扫"</p>
                <p>2. 扫描上方二维码完成支付</p>
            </div>
        </div>

        <div class="tips">
            <p>💡 系统将自动跳转到支付页面，如果失败将显示二维码</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        const payUrl = "' . addslashes($payUrl) . '";
        const payType = "' . ($payType === 'alipay' ? 'alipay' : 'wxpay') . '";
        let redirectAttempted = false;

        // 尝试页面跳转
        function attemptRedirect() {
            if (redirectAttempted) return;
            redirectAttempted = true;

            console.log("尝试跳转到支付页面:", payUrl);

            try {
                window.location.href = payUrl;
            } catch (e) {
                console.error("跳转失败:", e);
                showFallbackOptions();
            }
        }

        // 显示备用选项
        function showFallbackOptions() {
            document.getElementById("loadingSection").style.display = "none";
            document.getElementById("manualOptions").style.display = "block";
            document.getElementById("qrFallback").style.display = "block";
            document.getElementById("qrContainer").style.display = "block";

            // 生成二维码
            generateQRCode();
        }

        // 生成二维码
        function generateQRCode() {
            const qrContainer = document.getElementById("qrcode");
            if (window.QRCode) {
                QRCode.toCanvas(qrContainer, payUrl, {
                    width: 200,
                    height: 200,
                    margin: 2,
                    color: {
                        dark: "#000000",
                        light: "#FFFFFF"
                    }
                }, function(error) {
                    if (error) {
                        console.error("二维码生成失败:", error);
                        qrContainer.innerHTML = "<p style=\"color: #dc3545;\">二维码生成失败，请使用手动链接</p>";
                    }
                });
            } else {
                // 备用方案：使用在线二维码生成服务
                const qrImageUrl = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" + encodeURIComponent(payUrl);
                qrContainer.innerHTML = "<img src=\"" + qrImageUrl + "\" alt=\"支付二维码\" style=\"width: 200px; height: 200px;\" onerror=\"this.style.display=\'none\'; this.parentNode.innerHTML=\'<p style=\\\"color: #dc3545;\\\">二维码加载失败</p>\'\">";
            }
        }

        // 复制功能
        function copyToClipboard() {
            const urlElement = document.getElementById("payUrl");
            urlElement.style.display = "block";

            const textArea = document.createElement("textarea");
            textArea.value = payUrl;
            document.body.appendChild(textArea);
            textArea.select();

            try {
                document.execCommand("copy");
                alert("链接已复制到剪贴板，请在' . $payTypeName . 'APP中粘贴打开");
            } catch (err) {
                alert("复制失败，请手动复制下方显示的链接");
            }

            document.body.removeChild(textArea);
        }

        // 页面加载完成后开始处理
        window.addEventListener("load", function() {
            setTimeout(attemptRedirect, 2000);

            // 如果5秒后还没有显示备用选项，强制显示
            setTimeout(function() {
                if (document.getElementById("qrFallback").style.display === "none") {
                    showFallbackOptions();
                }
            }, 7000);
        });

        // 错误处理
        window.addEventListener("error", function(e) {
            console.log("检测到错误:", e.message);
            if (e.message && e.message.indexOf("ERR_UNKNOWN_URL_SCHEME") !== -1) {
                showFallbackOptions();
            }
        });
    </script>
</body>
</html>';
    }
}

/**
 * 生成PC端支付页面
 */
function generatePCPayPage($payUrl, $payType, $orderInfo) {
    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付页面</title>
    <style>
        body { margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: Arial, sans-serif; }
        .container { position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); text-align: center; color: white; }
        .loading { font-size: 18px; margin-bottom: 20px; }
        .spinner { border: 4px solid rgba(255,255,255,0.3); border-radius: 50%; border-top: 4px solid white; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 20px; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="loading">正在为您跳转到支付页面，请稍候...</div>
        <p>订单号：' . htmlspecialchars($orderInfo['out_trade_no']) . '</p>
        <p>支付金额：￥' . htmlspecialchars($orderInfo['money']) . '</p>
    </div>

    <script>
        setTimeout(function() {
            window.location.href = "' . addslashes($payUrl) . '";
        }, 1000);
    </script>
</body>
</html>';
}

/**
 * 生成二维码支付页面
 * @param string $qrcode 二维码内容
 * @param string $out_trade_no 订单号
 * @param string $money 金额
 * @param string $name 商品名称
 * @return string HTML内容
 */
function generateQRCodePage($qrcode, $out_trade_no, $money, $name) {
    // 检测支付类型
    $isWechatPay = (strpos($qrcode, 'weixin://') === 0 || strpos($qrcode, 'wxp://') === 0);
    $payTypeName = $isWechatPay ? '微信' : '支付宝';
    $payTypeColor = $isWechatPay ? '#1aad19' : '#1677ff';
    $payTypeIcon = $isWechatPay ? '💚' : '🔵';

    // 生成二维码图片URL (使用在线二维码生成服务)
    $qrImageUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($qrcode);

    return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $payTypeName . '扫码支付</title>
    <style>
        body { margin: 0; padding: 20px; background: linear-gradient(135deg, ' . $payTypeColor . ' 0%, #f8f9fa 100%); font-family: Arial, sans-serif; min-height: 100vh; }
        .container { max-width: 400px; margin: 20px auto; background: white; border-radius: 15px; padding: 30px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .title { font-size: 20px; color: #333; margin-bottom: 20px; }
        .pay-icon { font-size: 32px; margin-bottom: 10px; }
        .qr-container { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid ' . $payTypeColor . '; }
        .qr-image { width: 200px; height: 200px; margin: 0 auto; display: block; border: 1px solid #ddd; border-radius: 8px; }
        .order-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left; }
        .order-info div { margin-bottom: 8px; color: #555; }
        .order-info strong { color: #333; }
        .tips { font-size: 14px; color: #666; line-height: 1.5; margin-top: 20px; }
        .status { padding: 10px; border-radius: 5px; margin: 15px 0; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .refresh-btn { display: inline-block; padding: 10px 20px; background: ' . $payTypeColor . '; color: white; text-decoration: none; border-radius: 5px; margin-top: 15px; }
        .refresh-btn:hover { opacity: 0.8; }
        .pay-steps { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: left; }
        .pay-steps ol { margin: 0; padding-left: 20px; }
        .pay-steps li { margin-bottom: 8px; color: #555; }
    </style>
</head>
<body>
    <div class="container">
        <div class="pay-icon">' . $payTypeIcon . '</div>
        <div class="title">' . $payTypeName . '扫码支付</div>

        <div class="qr-container">
            <img src="' . $qrImageUrl . '" alt="支付二维码" class="qr-image" onerror="this.src=\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuS6jOe7tOeggeWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=\';">
        </div>

        <div class="order-info">
            <div><strong>订单号：</strong>' . htmlspecialchars($out_trade_no) . '</div>
            <div><strong>支付金额：</strong>￥' . htmlspecialchars($money) . '</div>
            <div><strong>商品名称：</strong>' . htmlspecialchars($name) . '</div>
        </div>

        <div class="status">
            ⏰ 请在15分钟内完成支付，超时订单将自动取消
        </div>

        <div class="pay-steps">
            <strong>📋 支付步骤：</strong>
            <ol>
                <li>打开' . $payTypeName . 'APP，点击"扫一扫"</li>
                <li>扫描上方二维码</li>
                <li>确认支付金额后完成付款</li>
                <li>支付完成后页面将自动跳转</li>
            </ol>
        </div>

        <div class="tips">
            <p><strong>💡 温馨提示：</strong></p>
            <p>• 如果二维码无法显示，请点击下方刷新按钮</p>
            <p>• 支付完成后请稍等片刻，系统正在处理中</p>
            <p>• 如有问题请联系客服</p>
        </div>

        <a href="javascript:location.reload()" class="refresh-btn">刷新页面</a>
    </div>

    <script>
        // 定期检查支付状态
        var checkCount = 0;
        var maxChecks = 60; // 最多检查5分钟

        function checkPaymentStatus() {
            checkCount++;
            if (checkCount > maxChecks) {
                return;
            }

            // 这里可以添加AJAX检查支付状态的逻辑
            // 暂时使用简单的页面刷新提示
            setTimeout(function() {
                if (checkCount % 12 === 0) { // 每分钟提示一次
                    console.log("正在检查支付状态...");
                }
                checkPaymentStatus();
            }, 5000);
        }

        // 开始检查支付状态
        checkPaymentStatus();

        // 处理二维码加载失败
        document.querySelector(".qr-image").onerror = function() {
            this.style.display = "none";
            this.parentNode.innerHTML += "<p style=\"color: #dc3545; margin-top: 20px;\">二维码加载失败，请刷新页面重试</p>";
        };
    </script>
</body>
</html>';
}
?>
