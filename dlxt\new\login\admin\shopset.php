<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<!-- 加载 下拉图片 -->
<link rel="stylesheet" type="text/css" href="/static/admin/image-picker/image-picker.css">
<script type="text/javascript" src="/static/admin/image-picker/image-picker.js"></script>
<style>
 .container {
  height: 60px;
  width: 90px;
  margin: 0 auto;
} 
 </style>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='addtype'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addtype(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>分类名称</label>
              <input type="text" class="form-control"  name="name" value="" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'" >'.$res['value'].'</option>';
				
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='edittype'){
	$id = intval($get['id']);
	$typesData=$DB->getRow("SELECT * FROM `types` WHERE `id` ='" . $id . "' limit 1");
	if(!$typesData){echo "<script>layer.ready(function(){layer.msg('该分类信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return edittype(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>分类名称</label>
              <input type="text" class="form-control"  name="name" value="<?php echo $typesData['name'] ;?>" placeholder="请输入分类名称" />
              <input type="text" class="form-control"  name="id" value="<?php echo $typesData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					if($res['value'] == $typesData['image']){ 
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'" selected = "selected" >'.$res['value'].'</option>';
					}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
					}
					
				
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
 
<?php
}else if($act=='addshop'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addshop(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="" placeholder="请输入物品数量" />
            </div>
			<div class="form-group col-md-6">
				<label>选择分类</label>
				<select name="itemtype" class="form-control select2" id="itemtype" >
				<?php
				$rs=$DB->query("SELECT * FROM `types` where `type`='1' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#itemtype').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>上架状态</label>
				<select name="status" class="form-control" id="status" >
				<option value="1">上架</option>
				<option value="2">下架</option>
				</select>
				<script>var selectorx = $('#status').select2(  );</script>
			</div>
            <div class="form-group col-md-12">
              <label>介绍</label>
				<input type="text" class="form-control" name="info"  value="暂未设置" placeholder="请输入介绍">
            </div>
            <div class="form-group col-md-12">
              <label>商品价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入商品价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div> 
  
  
<?php
}else if($act=='addrmbshop'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addrmbshop(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="" placeholder="请输入物品数量" />
            </div>
			<div class="form-group col-md-6">
				<label>选择分类</label>
				<select name="itemtype" class="form-control select2" id="itemtype" >
				<?php
				$rs=$DB->query("SELECT * FROM `types` where `type`='1' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#itemtype').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>上架状态</label>
				<select name="status" class="form-control" id="status" >
				<option value="1">上架</option>
				<option value="2">下架</option>
				</select>
				<script>var selectorx = $('#status').select2(  );</script>
			</div>
            <div class="form-group col-md-12">
              <label>介绍</label>
				<input type="text" class="form-control" name="info"  value="暂未设置" placeholder="请输入介绍">
            </div>
            <div class="form-group col-md-12">
              <label>商品价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入商品价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div> 
  
<?php
}else if($act=='editshop'){
	$id = intval($get['id']);
	$shopsData=$DB->getRow("SELECT * FROM `shops` WHERE `id` ='" . $id . "' limit 1");
	if(!$shopsData){echo "<script>layer.ready(function(){layer.msg('该商品信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editshop(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="<?php echo $shopsData['num'] ;?>" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="id" value="<?php echo $shopsData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-6">
				<label>选择分类</label>
				<select name="itemtype" class="form-control select2" id="itemtype" >
				<?php
				$rs=$DB->query("SELECT * FROM `types` where `type`='1' order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['itemtype']== $res['id'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#itemtype').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>上架状态</label>
				<select name="status" class="form-control" id="status" >
				<?php 
				if($shopsData['status'] == '1' ){
					echo '<option value="1" selected="selected">上架</option><option value="2">下架</option>';
				}else{
					echo '<option value="2" selected="selected">下架</option><option value="1">上架</option>';
				}
				?>
				</select>
				<script>var selectorx = $('#status').select2(  );</script>
			</div>
            <div class="form-group col-md-12">
              <label>介绍</label>
				<input type="text" class="form-control" name="info"  value="<?php echo $shopsData['info'] ;?>" placeholder="请输入介绍">
            </div>
            <div class="form-group col-md-12">
              <label>商品价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price"  value="<?php echo $shopsData['price'] ;?>" placeholder="请输入商品价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editrmbshop'){
	$id = intval($get['id']);
	$shopsData=$DB->getRow("SELECT * FROM `rmbshops` WHERE `id` ='" . $id . "' limit 1");
	if(!$shopsData){echo "<script>layer.ready(function(){layer.msg('该商品信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editrmbshop(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="<?php echo $shopsData['num'] ;?>" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="id" value="<?php echo $shopsData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-6">
				<label>选择分类</label>
				<select name="itemtype" class="form-control select2" id="itemtype" >
				<?php
				$rs=$DB->query("SELECT * FROM `types` where `type`='1' order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['itemtype']== $res['id'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#itemtype').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>上架状态</label>
				<select name="status" class="form-control" id="status" >
				<?php 
				if($shopsData['status'] == '1' ){
					echo '<option value="1" selected="selected">上架</option><option value="2">下架</option>';
				}else{
					echo '<option value="2" selected="selected">下架</option><option value="1">上架</option>';
				}
				?>
				</select>
				<script>var selectorx = $('#status').select2(  );</script>
			</div>
            <div class="form-group col-md-12">
              <label>介绍</label>
				<input type="text" class="form-control" name="info"  value="<?php echo $shopsData['info'] ;?>" placeholder="请输入介绍">
            </div>
            <div class="form-group col-md-12">
              <label>商品价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price"  value="<?php echo $shopsData['price'] ;?>" placeholder="请输入商品价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($shopsData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='adddrawrule'){
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return adddrawrule(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>抽奖次数</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="times"  value="" placeholder="请输入抽奖次数">
					<span class="input-group-addon">次</span>
				</div>
            </div>
            <div class="form-group col-md-6">
              <label>平台币价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="value"  value="" placeholder="请输入平台币价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-6">
              <label>现金价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money"  value="" placeholder="请输入现金价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-6">
				<label>平台币支付</label>
				<select name="ptbopen" class="form-control">
					<option value="1" selected="selected">开启</option>
					<option value="0">关闭</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='blacklist'){
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return blacklist(this)" method="post" class="row">
            <div class="form-group col-md-12">
              <label>IP</label>
				<div class="input-group col-md-12">
					<input type="text" class="form-control" name="ip"  value="" placeholder="请输入IP">
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editdrawrule'){
	$id = intval($get['id']);
	$drawruleData=$DB->getRow("SELECT * FROM `drawrule` WHERE `id` ='" . $id . "' limit 1");
	if(!$drawruleData){echo "<script>layer.ready(function(){layer.msg('该规则信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editdrawrule(this)" method="post" class="row">
            <div class="form-group col-md-6">
              <label>抽奖次数</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="times"  value="<?php echo $drawruleData['times'] ;?>" placeholder="请输入抽奖次数">
					<span class="input-group-addon">次</span>
					<input type="text" class="form-control"  name="id" value="<?php echo $drawruleData['id'] ;?>" style="display:none">
				</div>
            </div>
            <div class="form-group col-md-6">
              <label>平台币价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="value"  value="<?php echo $drawruleData['value'] ;?>" placeholder="请输入平台币价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-6">
              <label>现金价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money"  value="<?php echo $drawruleData['money'] ;?>" placeholder="请输入现金价格">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-6">
				<label>平台币支付</label>
				<select name="ptbopen" class="form-control">
				<?php 
				if($drawruleData['ptbopen'] == '1' ){
					echo '<option value="1" selected="selected">开启</option><option value="0">关闭</option>';
				}else{
					echo '<option value="0" selected="selected">关闭</option><option value="1">开启</option>';
				}
				?>
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
  </div>
<?php
}else if($act=='adddraw'){
$allvalue=$DB->getColumn("SELECT SUM(value) FROM draws");
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return adddraw(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="" placeholder="请输入物品数量" />
            </div>
            <div class="form-group col-md-12">
              <label>奖品几率（当前奖品总概率：<?php echo $allvalue;?>）</label>
					<input type="number" class="form-control" name="value"  value="" placeholder="请输入奖品几率">
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editdraw'){
	$id = intval($get['id']);
	$drawsData=$DB->getRow("SELECT * FROM `draws` WHERE `id` ='" . $id . "' limit 1");
	if(!$drawsData){echo "<script>layer.ready(function(){layer.msg('该奖品信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
$allvalue=$DB->getColumn("SELECT SUM(value) FROM draws");
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editdraw(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($drawsData['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="<?php echo $drawsData['num'] ;?>" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="id" value="<?php echo $drawsData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
            <div class="form-group col-md-12">
              <label>奖品几率（当前奖品总概率：<?php echo $allvalue;?>）</label>
					<input type="number" class="form-control" name="value"  value="<?php echo $drawsData['value'] ;?>" placeholder="请输入奖品几率">
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($drawsData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='addadduptype'){
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addadduptype(this)" method="post" class="row">
            <div class="form-group col-md-6">
              <label>档次名称</label>
              <input type="text" class="form-control"  name="name" value="" placeholder="请输入档次名称" />
            </div>
            <div class="form-group col-md-6">
              <label>领取要求</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="value"  value="" placeholder="请输入领取要求">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editadduptype'){
	$id = intval($get['id']);
	$addupData=$DB->getRow("SELECT * FROM `adduptype` WHERE `id` ='" . $id . "' limit 1");
	if(!$addupData){echo "<script>layer.ready(function(){layer.msg('该档次信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editadduptype(this)" method="post" class="row">
            <div class="form-group col-md-6">
              <label>档次名称</label>
              <input type="text" class="form-control"  name="name" value="<?php echo $addupData['name'] ;?>" placeholder="请输入档次名称" />
              <input type="text" class="form-control"  name="id" value="<?php echo $addupData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
            <div class="form-group col-md-6">
              <label>领取要求</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="value"  value="<?php echo $addupData['value'] ;?>" placeholder="请输入领取要求">
					<span class="input-group-addon">元</span>
				</div>
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($addupData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
  </div>
<?php
}else if($act=='addaddup'){
	$tid = intval($get['tid']);
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <form onsubmit="return addaddup(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>累计档次</label>
				<select name="lv" class="form-control select2" id="lv" >
				<?php
				$rs=$DB->query("SELECT * FROM `adduptype` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#lv').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="tid" value="<?php echo $tid ;?>" style="display:none" />
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
  </div>
<?php
}else if($act=='editaddup'){
	$tid = intval($get['tid']);
	$id = intval($get['id']);
	$addupsData=$DB->getRow("SELECT * FROM `addup` WHERE `id` ='" . $id . "' limit 1");
	if(!$addupsData){echo "<script>layer.ready(function(){layer.msg('该累计信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          <form onsubmit="return editaddup(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($addupsData['itemid']== $res['itemid'] ){
					echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
					echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-6">
				<label>累计档次</label>
				<select name="lv" class="form-control select2" id="lv" >
				<?php
				$rs=$DB->query("SELECT * FROM `adduptype` order by id");
				while($res = $rs->fetch())
				{
				if($addupsData['lv']== $res['id'] ){
					echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
					echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#lv').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="<?php echo $addupsData['num'] ;?>" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="tid" value="<?php echo $tid ;?>" style="display:none" />
              <input type="text" class="form-control"  name="id" value="<?php echo $id ;?>" style="display:none" />
            </div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($addupsData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function adddrawrule(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=adddrawrule',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editdrawrule(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editdrawrule',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editaddup(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editaddup',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function blacklist(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=blacklist',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addaddup(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addaddup',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editadduptype(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editadduptype',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addadduptype(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addadduptype',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editdraw(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editdraw',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function adddraw(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=adddraw',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editrmbshop(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editrmbshop',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addrmbshop(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addrmbshop',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editshop(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editshop',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addshop(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addshop',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addtype(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addtype',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function edittype(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=edittype',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>