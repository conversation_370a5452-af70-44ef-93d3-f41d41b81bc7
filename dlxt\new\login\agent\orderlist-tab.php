<?php
include('./auth.php');
if(isset($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%' and `agent`like'%[".$adminData['id']."]%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}' and `agent`like'%[".$adminData['id']."]%'";
		}
	}else{
	$sql=" `agent`like'%[".$adminData['id']."]%'";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" `agent`like'%[".$adminData['id']."]%'";
}
$numrows=$DB->getColumn("SELECT count(*) from pay_order WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>订单号</th>
					<th>订单类型</th>
					<th>代理信息</th>
					<th>充值账号</th>
					<th>所属大区</th>
					<th>角色名称</th>
					<th>角色ID</th>
                	<th>支付金额</th>
                	<th>订单状态</th>
                	<th>订单IP</th>
                	<th>支付地点</th>
                	<th>支付时间</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM pay_order WHERE{$sql} order by id desc limit $offset,$pagesize");
while($res = $rs->fetch())
{
	switch ($res['ordertype']){
		case 1:
			$ordertype = '<span class="label label-dark">平台币</span>';
		break;
		case 2:
			$ordertype = '<span class="label label-danger">现金商城</span>';
		break;
		case 3:
			$ordertype = '<span class="label label-secondary">周卡/月卡</span>';
		break;
		case 4:
			$ordertype = '<span class="label label-warning">抽奖</span>';
		break;
		case 5:
			$ordertype = '<span class="label label-info">特技</span>';
		break;
		case 6:
			$ordertype = '<span class="label label-success">特效</span>';
		break;
		case 7:
			$ordertype = '<span class="label label-primary">套装</span>';
		break;
		default:
			$ordertype = '<span class="label label-default">未分类</span>';
	}
	$qucheck=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['qu'] . "' limit 1");
	$lastuids = explode(';',$res['agent']);
	$lastuidz = explode(']',$lastuids[0]);
	$lastuid = explode('[',$lastuidz[0]);
	$agentcheck=$DB->getRow("SELECT * FROM `admin` WHERE `id` ='" . $lastuid[1] . "' limit 1");
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['orderid'].'</td>
<td>'.$ordertype.'</td>
<td><span class="label label-primary">'.$agentcheck['username'].'</span></td>
<td>'.$res['user'].'</td>
<td>'.$qucheck['name'].'</td>
<td>'.$res['rolename'].'</td>
<td>'.$res['roleid'].'</td>
<td>'.$res['money'].'</td>
<td>'.($res['status']==1?'<span class="label label-dark">已支付</span>':'<span class="label label-cyan">未支付</span>').'</td>
<td>'.$res['ip'].'</td>
<td>'.$res['city'].'</td>
<td>'.$res['date'].$res['time'].'</a></td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
