<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='zbdzadditem'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return zbdzadditem(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择物品（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='zbdzaddteji'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return zbdzaddteji(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择特技（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='5' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='addpetskill'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addpetskill(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择宠物技能（仅支持宠物技能类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='3' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='zbdzaddtexiao'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return zbdzaddtexiao(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择特效（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='6' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editpetskill'){
	$id = intval($get['id']);
	$checks=$DB->getRow("SELECT * FROM `petskilldz` WHERE `id` ='" . $id . "' limit 1");
	if(!$checks){echo "<script>layer.ready(function(){layer.msg('该信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editpetskill(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择物品（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='3' order by id");
				while($res = $rs->fetch())
				{
				if($checks['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" value="<?php echo $checks['price'] ?>" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
			<input type="number" style="display:none" value="<?php echo $checks['id'] ?>" name="id" placeholder="请输入定制价格">
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认编辑</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editzbdzitem'){
	$id = intval($get['id']);
	$checks=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
	if(!$checks){echo "<script>layer.ready(function(){layer.msg('该信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editzbdzitem(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择物品（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($checks['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" value="<?php echo $checks['price'] ?>" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
			<input type="number" style="display:none" value="<?php echo $checks['id'] ?>" name="id" placeholder="请输入定制价格">
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认编辑</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editzbdzteji'){
	$id = intval($get['id']);
	$checks=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
	if(!$checks){echo "<script>layer.ready(function(){layer.msg('该信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editzbdzteji(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择物品（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='5' order by id");
				while($res = $rs->fetch())
				{
				if($checks['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" value="<?php echo $checks['price'] ?>" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
			<input type="number" style="display:none" value="<?php echo $checks['id'] ?>" name="id" placeholder="请输入定制价格">
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认编辑</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editzbdztexiao'){
	$id = intval($get['id']);
	$checks=$DB->getRow("SELECT * FROM `zbdz` WHERE `id` ='" . $id . "' limit 1");
	if(!$checks){echo "<script>layer.ready(function(){layer.msg('该信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editzbdztexiao(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择物品（仅支持装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='6' order by id");
				while($res = $rs->fetch())
				{
				if($checks['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>定制价格</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" value="<?php echo $checks['price'] ?>" name="price" placeholder="请输入定制价格">
					<span class="input-group-addon" id="baifenbi">元</span>
				</div>
            </div>
			<input type="number" style="display:none" value="<?php echo $checks['id'] ?>" name="id" placeholder="请输入定制价格">
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认编辑</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function editzbdzitem(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editzbdzitem',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function addpetskill(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addpetskill',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editpetskill(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editpetskill',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editzbdzteji(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editzbdzteji',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editzbdztexiao(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editzbdztexiao',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function zbdzadditem(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=zbdzadditem',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function zbdzaddteji(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=zbdzaddteji',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function zbdzaddtexiao(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=zbdzaddtexiao',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>