<?php
include('./auth.php');
//支付公告
$paynotice=$DB->getRow("select * from `config` where `keys`='paynotice' limit 1");
//支付api
$apiurl=$DB->getRow("select * from `config` where `keys`='apiurl' limit 1");
//商户PID
$pid=$DB->getRow("select * from `config` where `keys`='pid' limit 1");
//商户KEY
$key=$DB->getRow("select * from `config` where `keys`='key' limit 1");
//预设金额
$ysrmb1=$DB->getRow("select * from `config` where `keys`='ysrmb1' limit 1");
$ysrmb2=$DB->getRow("select * from `config` where `keys`='ysrmb2' limit 1");
$ysrmb3=$DB->getRow("select * from `config` where `keys`='ysrmb3' limit 1");
$zdyrmbmax=$DB->getRow("select * from `config` where `keys`='zdyrmbmax' limit 1");
$zdyrmbmin=$DB->getRow("select * from `config` where `keys`='zdyrmbmin' limit 1");
//支付开关
$alipay=$DB->getRow("select * from `config` where `keys`='alipay' limit 1");
$wxpay=$DB->getRow("select * from `config` where `keys`='wxpay' limit 1");
//比例设置
$ptbbili=$DB->getRow("select * from `config` where `keys`='ptbbili' limit 1");
$vipbili=$DB->getRow("select * from `config` where `keys`='vipbili' limit 1");
$xianyubili=$DB->getRow("select * from `config` where `keys`='xianyubili' limit 1");

?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>支付配置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>支付公告设置</legend>
			<div class="form-group">
              <label class="col-xs-12">支付公告</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="paynotice" rows="6" placeholder="请输入支付页面公告内容"><?php echo $paynotice['values']; ?></textarea>
              </div>
            </div>
			
            <legend>商户信息设置</legend>
			<div class="form-group">
              <div class="col-xs-6">
              <label>支付网关</label>
                <input class="form-control" type="text" name="apiurl" value="<?php echo $apiurl['values']; ?>" placeholder="请输入易支付网关地址">
                <div class="help-block">例如：https://codepay.quyoumao.com/</div>
              </div>
              <!--div class="col-xs-6">
              <label for="apiurl">支付网关</label>
                <input class="form-control" type="text" name="apiurl" placeholder="例如：https://codepay.quyoumao.com/">
                <div class="help-block">请输入易支付网关地址</div>
              </div-->
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>商户PID</label>
                <input class="form-control" type="text" name="pid" value="<?php echo $pid['values']; ?>" placeholder="请输入易支付商户PID">
                <div class="help-block">例如：1000</div>
              </div>
              <div class="col-xs-6">
              <label>商户秘钥</label>
                <input class="form-control" type="text" name="key" value="<?php echo $key['values']; ?>" placeholder="请输入易支付商户秘钥KEY">
                <div class="help-block">例如：wwRRtktkrmRzL6Y8XsMeQimHInE32Gcu</div>
              </div>
            </div>
			<legend>支付预设金额设置</legend>
            <div class="form-group">
              <div class="col-xs-4">
              <label>预设金额一</label>
				<div class="input-group m-b-10">
					<input type="number" name="ysrmb1" class="form-control" value="<?php echo $ysrmb1['values']; ?>" placeholder="请输入玩家支付页面预设金额一" aria-describedby="addon1">
					<span class="input-group-addon" id="addon1">元</span>
				</div>
                <div class="help-block">例如：100</div>
              </div>
              <div class="col-xs-4">
              <label>预设金额二</label>
				<div class="input-group m-b-10">
					<input type="number" name="ysrmb2" class="form-control" value="<?php echo $ysrmb2['values']; ?>" placeholder="请输入玩家支付页面预设金额二" aria-describedby="addon1">
					<span class="input-group-addon" id="addon1">元</span>
				</div>
                <div class="help-block">例如：300</div>
              </div>
              <div class="col-xs-4">
              <label>预设金额三</label>
				<div class="input-group m-b-10">
					<input type="number" name="ysrmb3" class="form-control" value="<?php echo $ysrmb3['values']; ?>" placeholder="请输入玩家支付页面预设金额三" aria-describedby="addon1">
					<span class="input-group-addon" id="addon1">元</span>
				</div>
                <div class="help-block">例如：800</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>自定义金额最小值</label>
				<div class="input-group m-b-10">
					<input type="number" name="zdyrmbmin" class="form-control" value="<?php echo $zdyrmbmin['values']; ?>" placeholder="请输入玩家支付页面自定义金额最小值" aria-describedby="addon1">
					<span class="input-group-addon" id="addon1">元</span>
				</div>
                <div class="help-block">例如：1000</div>
              </div>
              <div class="col-xs-6">
              <label>自定义金额最大值</label>
				<div class="input-group m-b-10">
					<input type="number" name="zdyrmbmax" class="form-control" value="<?php echo $zdyrmbmax['values']; ?>" placeholder="请输入玩家支付页面自定义金额最大值" aria-describedby="addon1">
					<span class="input-group-addon" id="addon1">元</span>
				</div>
                <div class="help-block">例如：5000</div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-xs-12">支付方式</label>
              <div class="col-xs-12">
                <label class="checkbox-inline" for="alipay">
                  <input type="checkbox"  name="alipay" value="1" <?php echo ($alipay['values']=='1'?'checked':''); ?> >
                  支&nbsp;付&nbsp;宝
                </label>
				&nbsp;&nbsp;&nbsp;&nbsp;
                <label class="checkbox-inline" for="wxpay">
                  <input type="checkbox"  name="wxpay" value="1"  <?php echo ($wxpay['values']=='1'?'checked':''); ?> >
                  微&nbsp;&nbsp;信
                </label>
              </div>
            </div>
			<br>
        <!--legend>支付比例设置</legend>
            <div class="form-group">
              <div class="col-xs-4">
              <label>平台币比例</label>
				<input type="number" name="ptbbili" value="<?php echo $ptbbili['values']; ?>" class="form-control" placeholder="请输入玩家充值平台币比例" >
                <div class="help-block">例如：100</div>
              </div>
              <div class="col-xs-4">
              <label>赠送VIP经验比例</label>
				<input type="number" name="vipbili" value="<?php echo $vipbili['values']; ?>" class="form-control" placeholder="请输入玩家充值平台币赠送VIP经验比例" >
                <div class="help-block">不开启，请填写&nbsp;0</div>
              </div>
              <div class="col-xs-4">
              <label>赠送仙玉比例</label>
				<input type="number" name="xianyubili" value="<?php echo $xianyubili['values']; ?>" class="form-control" placeholder="请输入玩家充值平台币赠送仙玉经验" >
                <div class="help-block">不开启，请填写&nbsp;0</div>
              </div>
            </div-->
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=payset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>