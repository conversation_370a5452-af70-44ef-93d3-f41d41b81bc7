<?php
include('auth.php');
$strtotime = strtotime("now"); 
$date = date('Y-m-d',$strtotime);
$times = date('Y-m-d h:m:s',$strtotime);
$lastdate = date('Y-m-d',$strtotime-86400);
//玩家账号数量
$playernums=$DB->getColumn("SELECT count(*) from `account` where `agentid`='".$adminData['id']."'");
//代理数量
$agentnums=$DB->getColumn("SELECT count(*) from `admin` where `lastuid`like'%[".$adminData['id']."]%'");

//代理后台首页右下角公告
$dailinotice=$DB->getRow("select * from `config` where `keys`='dailinotice' limit 1");
//一周时间
$lastdatea = date('Y-m-d',$strtotime-172800);
$lastdateb = date('Y-m-d',$strtotime-259200);
$lastdatec = date('Y-m-d',$strtotime-345600);
$lastdated = date('Y-m-d',$strtotime-432000);
$lastdatee = date('Y-m-d',$strtotime-518400);

//一周流水
$lastdaya=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdatea."' and `agent`like'%[".$adminData['id']."]%'");
if($lastdaya == null){ $lastdaya = 0 ;}
$lastdayb=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdateb."' and `agent`like'%[".$adminData['id']."]%'");
if($lastdayb == null){ $lastdayb = 0 ;}
$lastdayc=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdatec."' and `agent`like'%[".$adminData['id']."]%'");
if($lastdayc == null){ $lastdayc = 0 ;}
$lastdayd=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdated."' and `agent`like'%[".$adminData['id']."]%'");
if($lastdayd == null){ $lastdayd = 0 ;}
$lastdaye=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdatee."' and `agent`like'%[".$adminData['id']."]%'");
if($lastdaye == null){ $lastdaye = 0 ;}
//昨日流水
$lastday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdate."' and `agent`like'%[".$adminData['id']."]%'");
if($lastday == null){ $lastday = 0 ;}
//今日流水
$today=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$date."' and `agent`like'%[".$adminData['id']."]%'");
if($today == null){ $today = 0 ;}
//总计流水
$allmoney=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `agent`like'%[".$adminData['id']."]%'");
if($allmoney == null){ $allmoney = 0 ;}

//昨日流水
$mylastday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$lastdate."' and `agent`like'%[".$adminData['id']."];%'");
if($mylastday == null){ $mylastday = 0 ;}
//今日流水
$mytoday=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `date`='".$date."' and `agent`like'%[".$adminData['id']."];%'");
if($mytoday == null){ $mytoday = 0 ;}
//总计流水
$myallmoney=$DB->getColumn("SELECT SUM(money) FROM `pay_order` WHERE `status`='1'  and `agent`like'%[".$adminData['id']."];%'");
if($myallmoney == null){ $myallmoney = 0 ;}
//$sql = "SELECT SUM(money) FROM `pay_order` WHERE `status`='1' and  `agent` like '%[".$res['id']."]%' and `date`='".$lastdate."'";
$i=0;
$rs=$DB->query("SELECT * FROM `servers` order by id ");
while($res = $rs->fetch())
{
	$server[$i]['name'] = $res['name'] ;
	$quport = $res['quport'];
	$server[$i]['rtn'] = exec('netstat -nat|grep -i '.$quport.'|wc -l');
	$i++;
}
//区服数量
$serversnums=$DB->getColumn("SELECT count(*) from `servers`");


$days=$DB->getColumn("SELECT SUM(money) FROM `gm_order` WHERE `username`='".$adminData['username']."' and `date`='".$date."'");
if($days == null){ $days = 0 ;}
$lastdates = date('Y-m-d',$strtotime-86400);
$lastdays=$DB->getColumn("SELECT SUM(money) FROM `gm_order` WHERE `username`='".$adminData['username']."' and `date`='".$lastdates."'");
if($lastdays == null){ $lastdays = 0 ;}

?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
  
<body>
<div class="container-fluid p-t-15">
  
  <div class="row">
    <div class="col-sm-6 col-md-2">
      <div class="card bg-warning">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">日期</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $date;?></b></p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-2">
      <div class="card bg-info">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">玩家账号数量</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $playernums;?></b>条</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-2">
      <div class="card bg-info">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">代理数量</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $agentnums;?></b>位</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-danger">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">代理额度</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $adminData['money'];?></b>元</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-danger">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">昨日变化额度</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $lastdays;?></b>元</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-danger">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">今日变化额度</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $days;?></b>元</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row">
    <div class="col-sm-6 col-md-2">
      <div class="card bg-primary">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">总流水(包含下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $allmoney;?></b>元</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-2">
      <div class="card bg-primary">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">总流水(不包下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $myallmoney;?></b>元</p>
        </div>
      </div>
    </div>
    <div class="col-sm-6 col-md-2">
      <div class="card bg-dark">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">昨日流水(包含下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $lastday;?></b>元</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-dark">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">昨日流水(不包下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $mylastday;?></b>元</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-pink">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">今日流水(包含下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $today;?></b>元</p>
        </div>
      </div>
    </div>
    
    <div class="col-sm-6 col-md-2">
      <div class="card bg-pink">
        <div class="card-body clearfix">
            <p class="h4 text-white m-t-0">今日流水(不包下级)</p>
            <p class="h3 text-white m-b-0 fa-1-5x"><b><?php echo $mytoday;?></b>元</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row">
    
    <div class="col-md-6"> 
      <div class="card">
        <div class="card-header">
          <h4>区服信息</h4>
        </div>
        <div class="card-body">
          <canvas class="js-chartjs-bars"></canvas>
        </div>
      </div>
    </div>
    
    <div class="col-md-6"> 
      <div class="card">
        <div class="card-header">
          <h4>交易流水</h4>
        </div>
        <div class="card-body">
          <canvas class="js-chartjs-lines"></canvas>
        </div>
      </div>
    </div>
     
  </div>
  <div class="row">
    
    <div class="col-md-6">
      <div class="card">
        <div class="card-header"><h4>代理信息</h4></div>
        <div class="card-body">
          
          <div class="alert alert-success" role="alert">
          代理账号：<a class="alert-link"><?php echo $_SESSION['adminUser'];?></a>
          </div>
          <div class="alert alert-danger" role="alert">
          分成信息： <a class="alert-link"><?php echo $adminData['fencheng'];?>%</a>
          </div>
          <div class="alert alert-warning" role="alert">
          邀请码：<a class="alert-link"><?php echo $adminData['invite'];?></a>
          </div>
          <div class="alert alert-info" role="alert">
          一键推广链接：<a href="<?php echo "http://".$_SERVER['HTTP_HOST']."/reg.php?invite=".$adminData['invite'];?>" class="alert-link" target="_blank" style="color:blue"><?php echo "http://".$_SERVER['HTTP_HOST']."/reg.php?invite=".$adminData['invite'];?></a>
          </div>
          
        </div>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="card">
        <div class="card-header"><h4>站点公告</h4></div>
        <div class="card-body">
          
          <div class="alert alert-danger" role="alert">
           <?php echo $dailinotice['values']; ?>
          </div>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
<script type="text/javascript" src="/static/admin/js/Chart.js"></script>
<script type="text/javascript">

function deleteaddup(id){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=deleteaddup',
	    data : {id:id},
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}


$(document).ready(function(e) {
    var $dashChartBarsCnt  = jQuery( '.js-chartjs-bars' )[0].getContext( '2d' ),
        $dashChartLinesCnt = jQuery( '.js-chartjs-lines' )[0].getContext( '2d' );
    
    var $dashChartBarsData = {
		labels: ['玩家数量'],
		datasets: [
			{
				label: '拥有玩家数量',
                borderWidth: 1,
                borderColor: 'rgba(0,0,0,0)',
				backgroundColor: 'rgba(51,202,185,0.5)',
                hoverBackgroundColor: "rgba(51,202,185,0.7)",
                hoverBorderColor: "rgba(0,0,0,0)",
				data: [<?php echo $playernums;	?>]
			}
		]
	};
    var $dashChartLinesData = {
		labels: [  '<?php echo $lastdatee;?>', '<?php echo $lastdated;?>', '<?php echo $lastdatec;?>', '<?php echo $lastdateb;?>', '<?php echo $lastdatea;?>', '昨日', '今日'],
		datasets: [
			{
				label: '交易流水(包含下级)',
				data: [<?php echo $lastdaye;?>, <?php echo $lastdayd;?>,<?php echo $lastdayc;?>,<?php echo $lastdayb;?>,<?php echo $lastdaya;?>,<?php echo $lastday;?>,<?php echo $today;?>,],
				borderColor: '#358ed7',
				backgroundColor: 'rgba(53, 142, 215, 0.175)',
                borderWidth: 1,
                fill: false,
                lineTension: 0.5
			}
		]
	};
    
    new Chart($dashChartBarsCnt, {
        type: 'bar',
        data: $dashChartBarsData
    });
    
    var myLineChart = new Chart($dashChartLinesCnt, {
        type: 'line',
        data: $dashChartLinesData,
    });
});
</script>
</body>
</html>