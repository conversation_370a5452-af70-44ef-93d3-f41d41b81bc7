<?php
include './common/main.php';

// 支付配置
$epay_config = array(
    'apiurl' => 'https://sheng.nachengweb.com/',
    'pid' => '1093',
    'key' => '72jJHMDjjYBqMmZjMjrR7mbt27Zu3Mjy'
);

// 商品信息
$goods_info = array(
    '1' => array('服务器自愿支持', '共计可领取', '6', '80', 'fa-gift','xy'),
    '2' => array('服务器自愿支持', '共计可领取', '30', '450', 'fa-star','cj10'),
    '3' => array('服务器自愿支持', '共计可领取', '2', '20', 'fa-heart','xy'),
    '4' => array('服务器自愿支持', '共计可领取', '1', '10', 'fa-diamond','cj10'),
    '5' => array('服务器自愿支持', '共计可领取', '500', '500', 'fa-crown','cj50'),
    '6' => array('服务器自愿支持', '共计可领取', '0.1', '1', 'fa-coins','xy'),
);

// 获取客户端IP
function get_client_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 生成订单号
function generate_trade_no() {
    $strtotime = time();
    $year = date('Y', $strtotime);
    $month = date('m', $strtotime);
    $day = date('d', $strtotime);
    $hour = date('H', $strtotime);
    $minute = date('i', $strtotime);
    $second = date('s', $strtotime);
    $microtime = substr(microtime(), 2, 5);
    $random = rand(1000, 9999);
    
    return 'Pay' . $year . $month . $day . $hour . $minute . $second . $microtime . $random;
}

$strtotime = strtotime("now");
$date = date('Y-m-d',$strtotime);
$time = date('H:i:s',$strtotime);
$ip = get_client_ip();
$city = '未知';

// 自动检测协议和端口
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST']; // 这里已经包含了端口信息

//异步回调
$notify_url = $protocol."://".$host."/pay_notify.php";
//同步回调
$return_url = $protocol."://".$host."/pay_return.php";
//商户订单号
$out_trade_no = generate_trade_no();
$type = $post['type'];

$account = $post['account'];
$info = base64_encode('?server='.urlencode($post['server']).'&role='.urlencode($post['role']).'&roleid='.$post['roleid'].'&account='.$post['account']);

// 使用 PdoHelper 的 query 方法
$accountData = $DB->getRow("SELECT * FROM `account` WHERE `username` = ?", [$account]);

// 验证商品ID合法性
if(!isset($goods_info[$post['goods']]) || !is_numeric($post['goods'])) {
    exit('非法商品');
}

$money = $goods_info[$post['goods']][2];
$xianyu = $goods_info[$post['goods']][3];
$name = $account.'-'.$post['goods'].'-'.$post['roleid'];

// 验证金额合法性
if(!is_numeric($money) || $money <= 0 || $money > 10000) {
    exit('非法金额');
}

// 检查订单是否存在
$existOrder = $DB->getRow("SELECT id FROM pay_order WHERE orderid = ?", [$out_trade_no]);
if($existOrder) {
    exit('订单号重复');
}

//构建代理系统订单
$agentid = $accountData['agentid'] ?? 1;
if ($agentid == '') {
    $agentid = 1;
}
$checkAgent = $DB->query("SELECT * FROM `admin` WHERE `id` = '$agentid' ")->fetch();
if(!$checkAgent){
    $agentid = 1;
    $checkAgent = $DB->query("SELECT * FROM `admin` WHERE `id` = '1' ")->fetch();
}

$agents = explode(';',$checkAgent['lastuid']);
$agent = '['.$checkAgent['id'].'];'.$agents[1];

// 使用 PdoHelper 的 exec 方法插入订单
$DB->exec("INSERT INTO `pay_order` (`orderid`,`ordertype`,`value`,`user`,`roleid`,`rolename`,`qu`,`agent`,`money`,`status`,`ip`,`city`,`date`,`time`,`param`) 
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
    [
        $out_trade_no,
        '1',
        '00',
        $account,
        $post['roleid'],
        $post['role'],
        $post['server'],
        $agent,
        $money,
        '0',
        $ip,
        $city,
        $date,
        $time,
        $name
    ]
);

// 构造要请求的参数数组
$parameter = array(
    "pid" => $epay_config['pid'],
    "type" => $type,
    "notify_url" => $notify_url,
    "return_url" => $return_url,
    "out_trade_no" => $out_trade_no,
    "name" => $name,
    "money" => $money,
);

// 计算签名
ksort($parameter);
$signstr = '';
foreach($parameter as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$sign = md5($signstr);

$parameter['sign'] = $sign;
$parameter['sign_type'] = 'MD5';

// 生成支付表单
$html = '<form id="dopay" action="'.$epay_config['apiurl'].'submit.php" method="post">';
foreach ($parameter as $k=>$v) {
    $html.= '<input type="hidden" name="'.$k.'" value="'.$v.'"/>';
}
$html .= '<input type="submit" value="正在跳转到支付页面..."></form>';
$html .= '<script>document.getElementById("dopay").submit();</script>';

?>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在为您跳转到支付页面，请稍候...</title>
    <style type="text/css">
        body{margin:0;padding:0;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);font-family: Arial, sans-serif;}
        .container{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);text-align:center;color:white;}
        .loading{font-size:18px;margin-bottom:20px;}
        .spinner{border:4px solid rgba(255,255,255,0.3);border-radius:50%;border-top:4px solid white;width:40px;height:40px;animation:spin 1s linear infinite;margin:0 auto 20px;}
        @keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="loading">正在为您跳转到支付页面，请稍候...</div>
        <p>订单号：<?php echo $out_trade_no; ?></p>
        <p>支付金额：￥<?php echo $money; ?></p>
    </div>
    <?php echo $html; ?>
</body>
</html>
