<?php
include('./auth.php');
//资质查询
$petvalue1=$DB->getRow("select * from `petzizhi` where `id`='1' limit 1");
$petvalue2=$DB->getRow("select * from `petzizhi` where `id`='2' limit 1");
$petvalue3=$DB->getRow("select * from `petzizhi` where `id`='3' limit 1");
$petvalue4=$DB->getRow("select * from `petzizhi` where `id`='4' limit 1");
$petvalue5=$DB->getRow("select * from `petzizhi` where `id`='5' limit 1");
$petvalue6=$DB->getRow("select * from `petzizhi` where `id`='6' limit 1");
$petvalue7=$DB->getRow("select * from `petzizhi` where `id`='7' limit 1");
$petvalue8=$DB->getRow("select * from `petzizhi` where `id`='8' limit 1");
$petvalue9=$DB->getRow("select * from `petzizhi` where `id`='9' limit 1");
$petvalue10=$DB->getRow("select * from `petzizhi` where `id`='10' limit 1");
$petvalue11=$DB->getRow("select * from `petzizhi` where `id`='11' limit 1");
$petvalue12=$DB->getRow("select * from `petzizhi` where `id`='12' limit 1");

?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>定制宠物资质设置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>资质及价格</legend>
            <div class="form-group">
              <div class="col-xs-6">
              <label>成长资质定制上限值</label>
				<input type="number" name="chengzhang" class="form-control" value="<?php echo $petvalue1['value']; ?>" placeholder="请输入成长资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>成长资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="chengzhangprice" class="form-control" value="<?php echo $petvalue2['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>攻击资质定制上限值</label>
				<input type="number" name="gongji" class="form-control" value="<?php echo $petvalue3['value']; ?>" placeholder="请输入攻击资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>攻击资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="gongjiprice" class="form-control" value="<?php echo $petvalue4['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>防御资质定制上限值</label>
				<input type="number" name="fangyu" class="form-control" value="<?php echo $petvalue5['value']; ?>" placeholder="请输入防御资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>防御资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="fangyuprice" class="form-control" value="<?php echo $petvalue6['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>法术资质定制上限值</label>
				<input type="number" name="fashu" class="form-control" value="<?php echo $petvalue7['value']; ?>" placeholder="请输入法术资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>法术资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="fashuprice" class="form-control" value="<?php echo $petvalue8['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>体质资质定制上限值</label>
				<input type="number" name="tizhi" class="form-control" value="<?php echo $petvalue9['value']; ?>" placeholder="请输入体质资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>体质资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="tizhiprice" class="form-control" value="<?php echo $petvalue10['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-6">
              <label>速度资质定制上限值</label>
				<input type="number" name="sudu" class="form-control" value="<?php echo $petvalue11['value']; ?>" placeholder="请输入速度资质定制上限值">
                <div class="help-block">此选项设置玩家最多可以定制多少数值限制</div>
              </div>
              <div class="col-xs-6">
              <label>速度资质价格(x平台币/1点)</label>
				<div class="input-group m-b-10">
					<input type="number" name="suduprice" class="form-control" value="<?php echo $petvalue12['value']; ?>" placeholder="请输入玩家支付页面自定义金额最大值">
					<span class="input-group-addon">元</span>
				</div>
                <div class="help-block">此选项设置每增加1点数值需要多少平台币，例如定制4000，价格为2，则需要4000*2=8000平台币</div>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=petzzset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>