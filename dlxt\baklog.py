#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
手动执行备份功能 - 用于测试
"""

import os
import shutil
from datetime import datetime

# 配置信息
CONFIG = {
    'DATA_DIR': 'C:/server/data',
    'BACKUP_DIR': 'C:/server/backup'
}

def log_message(message):
    """输出带时间戳的日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")

def backup_character_logs():
    """备份角色日志功能"""
    try:
        log_message("开始备份角色日志...")
        
        # 创建备份根目录
        backup_root = CONFIG['BACKUP_DIR']
        if not os.path.exists(backup_root):
            os.makedirs(backup_root)
            log_message(f"创建备份根目录: {backup_root}")
        
        # 创建今日备份目录
        today = datetime.now().strftime('%Y-%m-%d')
        today_backup_dir = os.path.join(backup_root, today)
        if not os.path.exists(today_backup_dir):
            os.makedirs(today_backup_dir)
            log_message(f"创建今日备份目录: {today_backup_dir}")
        
        backup_stats = {
            'accounts_processed': 0,
            'characters_processed': 0,
            'logs_copied': 0,
            'errors': 0
        }
        
        # 遍历所有账号文件夹
        data_dir = CONFIG['DATA_DIR']
        if not os.path.exists(data_dir):
            log_message(f"数据目录不存在: {data_dir}")
            return backup_stats
        
        for account_folder in os.listdir(data_dir):
            account_path = os.path.join(data_dir, account_folder)
            
            # 跳过非目录和排除的账号
            if not os.path.isdir(account_path) or account_folder in ['xiaoshendada']:
                if account_folder == 'xiaoshendada':
                    log_message(f"跳过排除账号: {account_folder}")
                continue
            
            backup_stats['accounts_processed'] += 1
            log_message(f"备份账号: {account_folder}")
            
            # 遍历账号下的角色文件夹
            for item in os.listdir(account_path):
                character_path = os.path.join(account_path, item)
                
                # 跳过非目录项
                if not os.path.isdir(character_path):
                    continue
                
                # 检查是否是角色文件夹（通常是数字）
                if not item.isdigit():
                    continue
                
                backup_stats['characters_processed'] += 1
                
                # 查找日志记录文件夹
                log_dir = os.path.join(character_path, '日志记录')
                if os.path.exists(log_dir) and os.path.isdir(log_dir):
                    try:
                        # 创建备份目标路径
                        backup_target = os.path.join(today_backup_dir, account_folder, item, '日志记录')

                        # 确保目标目录存在
                        os.makedirs(backup_target, exist_ok=True)

                        # 统计原始文件数量
                        original_files = os.listdir(log_dir)
                        file_count = len(original_files)

                        if file_count > 0:
                            # 移动日志记录文件夹内的所有文件
                            moved_count = 0
                            for log_file in original_files:
                                source_file = os.path.join(log_dir, log_file)
                                target_file = os.path.join(backup_target, log_file)

                                # 如果是文件，则移动
                                if os.path.isfile(source_file):
                                    # 如果目标文件已存在，先删除
                                    if os.path.exists(target_file):
                                        os.remove(target_file)

                                    shutil.move(source_file, target_file)
                                    moved_count += 1
                                # 如果是文件夹，则移动整个文件夹
                                elif os.path.isdir(source_file):
                                    if os.path.exists(target_file):
                                        shutil.rmtree(target_file)

                                    shutil.move(source_file, target_file)
                                    moved_count += 1

                            backup_stats['logs_copied'] += 1
                            log_message(f"移动日志: {account_folder}/{item}/日志记录 ({moved_count} 个项目)")
                        else:
                            log_message(f"跳过 {account_folder}/{item}: 日志记录文件夹为空")

                    except Exception as e:
                        backup_stats['errors'] += 1
                        log_message(f"移动失败 {account_folder}/{item}: {e}")
                else:
                    log_message(f"跳过 {account_folder}/{item}: 无日志记录文件夹")
        
        # 输出备份统计
        log_message("角色日志备份完成")
        log_message(f"备份统计 - 账号: {backup_stats['accounts_processed']}, "
                   f"角色: {backup_stats['characters_processed']}, "
                   f"日志: {backup_stats['logs_copied']}, "
                   f"错误: {backup_stats['errors']}")
        
        # 计算备份大小
        if os.path.exists(today_backup_dir):
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(today_backup_dir):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            
            # 转换为可读格式
            if total_size < 1024:
                size_str = f"{total_size} B"
            elif total_size < 1024 * 1024:
                size_str = f"{total_size / 1024:.2f} KB"
            elif total_size < 1024 * 1024 * 1024:
                size_str = f"{total_size / (1024 * 1024):.2f} MB"
            else:
                size_str = f"{total_size / (1024 * 1024 * 1024):.2f} GB"
            
            log_message(f"备份大小: {size_str}")
            log_message(f"备份位置: {today_backup_dir}")
        
        return backup_stats
        
    except Exception as e:
        log_message(f"备份过程发生错误: {e}")
        return {'accounts_processed': 0, 'characters_processed': 0, 'logs_copied': 0, 'errors': 1}

def list_existing_backups():
    """列出已有的备份"""
    backup_root = CONFIG['BACKUP_DIR']
    
    if not os.path.exists(backup_root):
        log_message("备份目录不存在，这是首次备份")
        return
    
    backups = []
    for item in os.listdir(backup_root):
        backup_path = os.path.join(backup_root, item)
        if os.path.isdir(backup_path):
            # 统计备份内容
            account_count = 0
            total_files = 0
            
            for account in os.listdir(backup_path):
                account_path = os.path.join(backup_path, account)
                if os.path.isdir(account_path):
                    account_count += 1
                    for root, dirs, files in os.walk(account_path):
                        total_files += len(files)
            
            backups.append({
                'date': item,
                'path': backup_path,
                'accounts': account_count,
                'files': total_files
            })
    
    if backups:
        log_message(f"找到 {len(backups)} 个已有备份:")
        for backup in sorted(backups, key=lambda x: x['date']):
            log_message(f"  {backup['date']}: {backup['accounts']} 个账号, {backup['files']} 个文件")
    else:
        log_message("没有找到已有备份")

def main():
    """主函数"""
    print("=" * 60)
    log_message("手动执行角色日志备份")
    print("=" * 60)
    
    # 列出已有备份
    list_existing_backups()
    
    # 确认是否继续
    print()
    confirm = input("是否继续执行备份？(y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        log_message("备份已取消")
        return
    
    # 执行备份
    backup_stats = backup_character_logs()
    
    print("=" * 60)
    if backup_stats['logs_copied'] > 0:
        log_message("备份成功完成！")
    elif backup_stats['errors'] > 0:
        log_message("备份过程中出现错误，请检查日志")
    else:
        log_message("没有找到需要备份的日志文件")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("备份被用户中断")
    except Exception as e:
        log_message(f"备份过程发生错误: {e}")
        import traceback
        traceback.print_exc()
