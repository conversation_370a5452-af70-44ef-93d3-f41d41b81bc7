<?php
include "config.php";
require_once("lib/epay_notify.class.php");
//计算得出通知验证结果
$alipayNotify = new AlipayNotify($alipay_config);
$verify_result = $alipayNotify->verifyNotify();

if ($verify_result) {//验证成功
    //订单号
    $out_trade_no = $_GET['out_trade_no'];

    //交易状态
    $trade_status = $_GET['trade_status'];

    //交易金额
    $money = $_GET['money'];

    //支付时间
    $date = date("Y-m-d H:i:s");
$zhuangtai="支付失败";
		if ($_GET['trade_status'] == "TRADE_SUCCESS") {
$zhuangtai="支付成功，请返回游戏查看！";

		}
		}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html  xmlns="http://www.w3.org/1999/xhtml">
<head>
<script src="https://apps.bdimg.com/libs/jquery/2.1.4/jquery.min.js"></script>

<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">
<link type="text/css" rel="stylesheet" href="./img/css/style.css?<?php echo time()?>" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>赞助中心</title>
</head>
<body >
<center class="top_center" ><?php echo $zhuangtai?></center>
<div class="footer">
	<p ><a class="url" href="https://codepay.quyoumao.com">支付系统</a></p>
</div>


</body>


</html>