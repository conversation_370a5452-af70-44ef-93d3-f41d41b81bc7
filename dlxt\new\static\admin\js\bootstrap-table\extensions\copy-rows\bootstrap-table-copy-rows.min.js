/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableCopyRows={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b=a.fn.bootstrapTable.utils.calculateObjectValue,c=a.fn.bootstrapTable.utils.sprintf,d=function(b){var c=document.createElement('textarea');a(c).html(b),document.body.appendChild(c),c.select();try{document.execCommand('copy')}catch(a){console.log('Oops, unable to copy')}a(c).remove()};a.extend(a.fn.bootstrapTable.defaults,{copyBtn:!1,copyWHiddenBtn:!1,copyDelemeter:', '}),a.fn.bootstrapTable.methods.push('copyColumnsToClipboard','copyColumnsToClipboardWithHidden');var e=a.fn.bootstrapTable.Constructor,f=e.prototype.initToolbar;e.prototype.initToolbar=function(){f.apply(this,Array.prototype.slice.apply(arguments));var a=this,b=this.$toolbar.find('>.btn-group');if(this.options.clickToSelect||this.options.singleSelect){if(this.options.copyBtn){b.append('<button class=\'btn btn-default\' id=\'copyBtn\'><span class=\'glyphicon glyphicon-copy icon-pencil\'></span></button>'),b.find('#copyBtn').click(function(){a.copyColumnsToClipboard()})}if(this.options.copyWHiddenBtn){b.append('<button class=\'btn btn-default\' id=\'copyWHiddenBtn\'><span class=\'badge\'><span class=\'glyphicon glyphicon-copy icon-pencil\'></span></span></button>'),b.find('#copyWHiddenBtn').click(function(){a.copyColumnsToClipboardWithHidden()})}}},e.prototype.copyColumnsToClipboard=function(){var c=this,e='',f=this.options.copyDelemeter;a.each(c.getSelections(),function(d,g){a.each(c.options.columns[0],function(a,h){'state'!==h.field&&'RowNumber'!==h.field&&h.visible&&(null!==g[h.field]&&(e+=b(h,c.header.formatters[a],[g[h.field],g,d],g[h.field])),e+=f)}),e+='\r\n'}),d(e)},e.prototype.copyColumnsToClipboardWithHidden=function(){var c=this,e='',f=this.options.copyDelemeter;a.each(c.getSelections(),function(d,g){a.each(c.options.columns[0],function(a,h){'state'!=h.field&&'RowNumber'!==h.field&&(null!==g[h.field]&&(e+=b(h,c.header.formatters[a],[g[h.field],g,d],g[h.field])),e+=f)}),e+='\r\n'}),d(e)}}(jQuery)});