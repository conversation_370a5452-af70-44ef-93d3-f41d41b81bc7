<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Scheme 支付测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .test-btn.success {
            background: #28a745;
        }
        .test-btn.warning {
            background: #ffc107;
            color: #333;
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .info-box {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .code-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 URL Scheme 支付测试页面</h1>
        
        <div class="info-box">
            <strong>📋 测试说明：</strong><br>
            此页面用于测试 ERR_UNKNOWN_URL_SCHEME 错误的解决方案。<br>
            请在不同设备和浏览器中测试以下链接。
        </div>

        <div class="device-info">
            <h4>🔍 当前设备信息：</h4>
            <div id="deviceInfo">正在检测...</div>
        </div>

        <div class="test-section">
            <h3>🔗 微信支付测试</h3>
            <p>测试微信支付 URL scheme 跳转</p>
            
            <button class="test-btn" onclick="testWeChatPay()">
                测试微信支付链接
            </button>
            
            <button class="test-btn warning" onclick="testWeChatPayDirect()">
                直接跳转微信链接
            </button>
            
            <div class="code-box">
                <strong>测试链接：</strong><br>
                weixin://dl/business/?t=xQBVQeg80Ju
            </div>
        </div>

        <div class="test-section">
            <h3>💰 支付宝测试</h3>
            <p>测试支付宝 URL scheme 跳转</p>
            
            <button class="test-btn success" onclick="testAlipay()">
                测试支付宝链接
            </button>
            
            <button class="test-btn warning" onclick="testAlipayDirect()">
                直接跳转支付宝链接
            </button>
            
            <div class="code-box">
                <strong>测试链接：</strong><br>
                alipays://platformapi/startapp?saId=10000007&qrcode=https://qr.alipay.com/test
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 普通网页支付测试</h3>
            <p>测试普通 HTTP/HTTPS 链接跳转</p>
            
            <button class="test-btn" onclick="testWebPay()">
                测试网页支付
            </button>
            
            <div class="code-box">
                <strong>测试链接：</strong><br>
                https://www.baidu.com
            </div>
        </div>

        <div class="test-section">
            <h3>⚠️ 错误模拟测试</h3>
            <p>模拟各种错误情况</p>
            
            <button class="test-btn danger" onclick="testUnknownScheme()">
                测试未知协议
            </button>
            
            <button class="test-btn danger" onclick="simulateError()">
                模拟 URL Scheme 错误
            </button>
        </div>

        <div class="warning-box">
            <strong>⚠️ 注意事项：</strong><br>
            • 在PC端测试时，URL scheme 链接可能无法直接打开<br>
            • 在移动端测试时，需要安装对应的APP<br>
            • 某些浏览器可能会阻止 URL scheme 跳转<br>
            • 测试时请观察控制台输出和页面反应
        </div>

        <div id="testResults" style="margin-top: 30px;">
            <h3>📊 测试结果</h3>
            <div id="resultsList"></div>
        </div>
    </div>

    <script src="url_scheme_handler.js"></script>
    <script>
        // 显示设备信息
        function showDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
                isAndroid: /Android/.test(navigator.userAgent),
                isWeChat: /MicroMessenger/i.test(navigator.userAgent),
                isQQ: /QQ\//i.test(navigator.userAgent),
                isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            };

            document.getElementById('deviceInfo').innerHTML = `
                <strong>平台：</strong>${info.platform}<br>
                <strong>语言：</strong>${info.language}<br>
                <strong>是否移动端：</strong>${info.isMobile ? '是' : '否'}<br>
                <strong>是否iOS：</strong>${info.isIOS ? '是' : '否'}<br>
                <strong>是否Android：</strong>${info.isAndroid ? '是' : '否'}<br>
                <strong>是否微信：</strong>${info.isWeChat ? '是' : '否'}<br>
                <strong>是否QQ：</strong>${info.isQQ ? '是' : '否'}<br>
                <strong>User Agent：</strong>${info.userAgent}
            `;
        }

        // 添加测试结果
        function addTestResult(testName, result, details = '') {
            const resultsList = document.getElementById('resultsList');
            const timestamp = new Date().toLocaleTimeString();
            const resultItem = document.createElement('div');
            resultItem.style.cssText = 'padding: 10px; margin: 5px 0; border-left: 4px solid #007bff; background: #f8f9fa;';
            resultItem.innerHTML = `
                <strong>[${timestamp}] ${testName}</strong><br>
                结果: ${result}<br>
                ${details ? `详情: ${details}` : ''}
            `;
            resultsList.appendChild(resultItem);
        }

        // 测试微信支付
        function testWeChatPay() {
            const testUrl = 'weixin://dl/business/?t=xQBVQeg80Ju';
            const orderInfo = {
                out_trade_no: 'TEST_' + Date.now(),
                money: '0.01',
                name: '测试商品'
            };

            addTestResult('微信支付测试', '开始测试', '使用 URL Scheme 处理器');
            
            if (window.urlSchemeHandler) {
                try {
                    urlSchemeHandler.handlePayment(testUrl, 'wxpay', orderInfo);
                    addTestResult('微信支付测试', '处理器调用成功', '已调用 URL Scheme 处理器');
                } catch (e) {
                    addTestResult('微信支付测试', '处理器调用失败', e.message);
                }
            } else {
                addTestResult('微信支付测试', '处理器未加载', '回退到直接跳转');
                window.location.href = testUrl;
            }
        }

        // 直接测试微信支付
        function testWeChatPayDirect() {
            const testUrl = 'weixin://dl/business/?t=xQBVQeg80Ju';
            addTestResult('微信支付直接跳转', '开始测试', '直接使用 window.location.href');
            
            try {
                window.location.href = testUrl;
                addTestResult('微信支付直接跳转', '跳转命令执行', '已执行跳转命令');
            } catch (e) {
                addTestResult('微信支付直接跳转', '跳转失败', e.message);
            }
        }

        // 测试支付宝
        function testAlipay() {
            const testUrl = 'alipays://platformapi/startapp?saId=10000007&qrcode=https://qr.alipay.com/test';
            const orderInfo = {
                out_trade_no: 'TEST_' + Date.now(),
                money: '0.01',
                name: '测试商品'
            };

            addTestResult('支付宝测试', '开始测试', '使用 URL Scheme 处理器');
            
            if (window.urlSchemeHandler) {
                try {
                    urlSchemeHandler.handlePayment(testUrl, 'alipay', orderInfo);
                    addTestResult('支付宝测试', '处理器调用成功', '已调用 URL Scheme 处理器');
                } catch (e) {
                    addTestResult('支付宝测试', '处理器调用失败', e.message);
                }
            } else {
                addTestResult('支付宝测试', '处理器未加载', '回退到直接跳转');
                window.location.href = testUrl;
            }
        }

        // 直接测试支付宝
        function testAlipayDirect() {
            const testUrl = 'alipays://platformapi/startapp?saId=10000007&qrcode=https://qr.alipay.com/test';
            addTestResult('支付宝直接跳转', '开始测试', '直接使用 window.location.href');
            
            try {
                window.location.href = testUrl;
                addTestResult('支付宝直接跳转', '跳转命令执行', '已执行跳转命令');
            } catch (e) {
                addTestResult('支付宝直接跳转', '跳转失败', e.message);
            }
        }

        // 测试网页支付
        function testWebPay() {
            const testUrl = 'https://www.baidu.com';
            const orderInfo = {
                out_trade_no: 'TEST_' + Date.now(),
                money: '0.01',
                name: '测试商品'
            };

            addTestResult('网页支付测试', '开始测试', '测试普通 HTTP 链接');
            
            if (window.urlSchemeHandler) {
                try {
                    urlSchemeHandler.handlePayment(testUrl, 'web', orderInfo);
                    addTestResult('网页支付测试', '处理器调用成功', '已调用 URL Scheme 处理器');
                } catch (e) {
                    addTestResult('网页支付测试', '处理器调用失败', e.message);
                }
            } else {
                addTestResult('网页支付测试', '处理器未加载', '回退到直接跳转');
                window.location.href = testUrl;
            }
        }

        // 测试未知协议
        function testUnknownScheme() {
            const testUrl = 'unknown://test/scheme';
            const orderInfo = {
                out_trade_no: 'TEST_' + Date.now(),
                money: '0.01',
                name: '测试商品'
            };

            addTestResult('未知协议测试', '开始测试', '测试未知 URL scheme');
            
            if (window.urlSchemeHandler) {
                try {
                    urlSchemeHandler.handlePayment(testUrl, 'unknown', orderInfo);
                    addTestResult('未知协议测试', '处理器调用成功', '已调用 URL Scheme 处理器');
                } catch (e) {
                    addTestResult('未知协议测试', '处理器调用失败', e.message);
                }
            } else {
                addTestResult('未知协议测试', '处理器未加载', '无法处理未知协议');
            }
        }

        // 模拟错误
        function simulateError() {
            addTestResult('错误模拟', '开始测试', '模拟 ERR_UNKNOWN_URL_SCHEME 错误');
            
            // 创建一个模拟错误事件
            const errorEvent = new ErrorEvent('error', {
                message: 'ERR_UNKNOWN_URL_SCHEME',
                filename: 'test',
                lineno: 1,
                colno: 1
            });
            
            window.dispatchEvent(errorEvent);
            addTestResult('错误模拟', '错误事件已触发', '已模拟 URL scheme 错误');
        }

        // 监听错误事件
        window.addEventListener('error', function(e) {
            addTestResult('错误监听', '捕获到错误', e.message);
        });

        // 页面加载完成后显示设备信息
        document.addEventListener('DOMContentLoaded', function() {
            showDeviceInfo();
            addTestResult('页面初始化', '页面加载完成', 'URL Scheme 处理器状态: ' + (window.urlSchemeHandler ? '已加载' : '未加载'));
        });
    </script>
</body>
</html>
