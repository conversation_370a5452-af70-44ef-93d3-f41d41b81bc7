<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%' and `username`='".$adminData['username']."'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'  and `username`='".$adminData['username']."'";
		}
	}else{
	$sql=" `username`='".$adminData['username']."'";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" `username`='".$adminData['username']."'";
}
$numrows=$DB->getColumn("SELECT count(*) from zqsq_log WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>操作代理</th>
                	<th>被转区角色信息</th>
					<th>预转区角色信息</th>
					<th>转区方案</th>
					<th>手续费（额度扣除）</th>
					<th>转移累计共计</th>
					<th>申请时间</th>
					<th>状态</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM zqsq_log WHERE{$sql} order by id desc limit $offset,$pagesize");
while($res = $rs->fetch())
{
	//老角色
	$oldroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $res['oldroleid'] . "' limit 1");
	$oldserverData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $oldroleData['serverid'] . "' limit 1");
	//新角色
	$newroleData=$DB->getRow("SELECT * FROM `binds` WHERE `id` ='" . $res['newroleid'] . "' limit 1");
	$newserverData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $newroleData['serverid'] . "' limit 1");
	//查询方案
	$fanganData=$DB->getRow("SELECT * FROM `zqfa` WHERE `id` ='" . $res['zhuanqufangan'] . "' limit 1");
	if($res['status']==1){
		$status = '<span class="btn btn-info">已通过，累计已转移到新角色</span>';
	}else if($res['status']==2){
		$status = '<span class="btn btn-secondary">已拒绝，代理额度已返还</span>';
	}else if($res['status']==3){
		$status = '<span class="btn btn-default">已撤销，代理额度已返还</span>';
	}else{
		$status = '<span class="btn btn-secondary">待审核</span>';
	}
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['username'].'</td>
<td>【'.$oldserverData['name'].'】'.$oldroleData['name'].' - ['.$oldroleData['roleid'].']</td>
<td>【'.$newserverData['name'].'】'.$newroleData['name'].' - ['.$newroleData['roleid'].']</td>
<td>'.$fanganData['name'].'</td>
<td><b>'.$res['shouxufei'].'</b></td>
<td><b>'.$res['newcharge'].'</b></td>
<td>'.$res['date'].'</td>
<td>'.$status.'</td>
<td>
<a href="javascript:chexiao('.$res['id'].')" class="btn btn-w-xs btn-danger">撤销</a>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
