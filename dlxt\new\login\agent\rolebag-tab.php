<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		}
	}else{
	$sql=" 1";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" 1";
}
$numrows=$DB->getColumn("SELECT count(*) from bindsbag WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>所属角色</th>
                	<th>物品名字</th>
					<th>图标</th>
					<th>物品ID</th>
					<th>物品数量</th>
					<th>领取状态</th>
					<th>获取时间</th>
					<th>获取来源</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM bindsbag WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	$bindcheck = $DB->query("SELECT * FROM `binds` WHERE `id` = '".$res['bindsid']."' ")->fetch();
	$value = explode(';',$res['value']);
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>【'.$bindcheck['name'].'】角色ID：'.$bindcheck['roleid'].'</td>
<td>'.$res['name'].'</a></td>
<td><img src="http://'.$_SERVER['HTTP_HOST'].$res['image'].'" style="width:60px;height:60px;" /></td>
<td><b>'.$value[0].'</b></td>
<td><b>'.$value[1].'</b></td>
<td>'.($res['status']==1?'<span class="label label-dark">已领取</span>':'<span class="label label-cyan">未领取</span>').'</td>
<td>'.$res['data'].'</td>
<td>'.$res['info'].'</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
