<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='allgetmoney'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return allgetmoney(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
            <div class="form-group col-md-12">
              <label>补发平台币数量</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" placeholder="请输入补发平台币数量">
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认补发</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmjichu'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmjichu(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">禁言</option>
					<option value="2">解除禁言</option>
					<option value="3">跳过主线</option>
					<option value="4">解散公会</option>
					<option value="5">清理背包</option>
					<option value="6">封禁账号</option>
					<option value="7">解除账号封禁</option>
					<option value="8">开启创建角色</option>
					<option value="9">禁止创建角色</option>
					<option value="10">强制下线</option>
					<option value="11">清除摆摊公示</option>
					<option value="12">开启手机验证</option>
					<option value="13">隐身加速</option>
					<option value="14">取消隐身加速</option>
					<option value="15">更新关键词</option>
					<option value="16">更新清包过滤</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmsend'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmsend(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">增加等级</option>
					<option value="2">减少仙玉</option>
					<option value="3">充值仙玉</option>
					<option value="4">充值金币</option>
					<option value="5">充值帮贡</option>
					<option value="6">关联手机</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="number">修改数值</label>
				<small style="color:blue">**必填**</small>
				<input type="number" class="form-control" name="number" value="" placeholder="请输入修改数值" />
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmpetskill'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmpetskill(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">增加技能</option>
					<option value="2">删除技能</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label>选择技能</label>
				<select name="petskillid" id="petskillid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='3' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#petskillid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmsendpet'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmsendpet(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label>选择宠物</label>
				<select name="petid" id="petid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='2' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#petid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label for="number">宠物等级</label>
				<small style="color:blue">**必填**</small>
				<input type="number" class="form-control" name="number" value="" placeholder="请输入宠物等级" />
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmpetdz'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmpetdz(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">修改成长资质</option>
					<option value="2">修改攻击资质</option>
					<option value="3">修改防御资质</option>
					<option value="4">修改法术资质</option>
					<option value="5">修改体质资质</option>
					<option value="6">修改速度资质</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="number">资质数值</label>
				<small style="color:blue">**必填，修改当前参战宠物资质**</small>
				<input type="number" class="form-control" name="number" value="" placeholder="请输入资质数值" />
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='payusermoney'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return payusermoney(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>充值玩家</label>
				<select name="roleid" class="form-control select2" id="roleid" >
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid` in (SELECT `id` FROM `account`)");
				while($res = $rs->fetch())
				{
						$serData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "' limit 1");
						$accountsData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $res['userid'] . "' limit 1");
						echo '<option value="'.$res['id'].'">角色名称:'.$res['name'].'，角色ID:'.$res['roleid'].'，所属大区:'.$serData['name'].'，所属账号:'.$accountsData['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#roleid').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <label>充值金额</label>
				<div class="input-group m-b-10">
					<input type="number" class="form-control" name="money" value="" placeholder="请输入充值金额" >
					<span class="input-group-addon">元</span>
				</div>
            </div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='payvip'){
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return payvip(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>充值玩家</label>
				<select name="roleid" class="form-control select2" id="roleid" >
				<?php
				$rs=$DB->query("SELECT * FROM `binds` where `userid` in (SELECT `id` FROM `account`)");
				while($res = $rs->fetch())
				{
						$serData=$DB->getRow("SELECT * FROM `servers` WHERE `id` ='" . $res['serverid'] . "' limit 1");
						$accountsData=$DB->getRow("SELECT * FROM `account` WHERE `id` ='" . $res['userid'] . "' limit 1");
						echo '<option value="'.$res['id'].'">角色名称:'.$res['name'].'，角色ID:'.$res['roleid'].'，所属大区:'.$serData['name'].'，所属账号:'.$accountsData['username'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#roleid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>选择操作内容</label>
				<select name="vipid" class="form-control">
					<option value="1">普通周卡</option>
					<option value="2">高级周卡</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认充值</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmsenditem'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmsenditem(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label>选择物品</label>
				<select name="itemid" id="itemid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#itemid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label for="number">发送数量</label>
				<small style="color:blue">**必填**</small>
				<input type="number" class="form-control" name="number" value="" placeholder="请输入发送数量" />
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmggsend'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmggsend(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">发送公告</option>
					<option value="2">CMD命令</option>
					<option value="3">发送个人弹窗</option>
					<option value="4">发送全服弹窗</option>
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="info">内容&CMD命令</label>
				<small style="color:blue">**必填**</small>
				<textarea class="form-control" rows="5" name="info" placeholder="请输入发送内容或CMD命令" ></textarea>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmzbdz'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmzbdz(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label>选择装备</label>
				<select name="zhuangbei" id="zhuangbei" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#zhuangbei').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>选择特技</label>
				<select name="teji" id="teji" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='5' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#teji').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>选择特效</label>
				<select name="texiao" id="texiao" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='6' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#texiao').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label>选择套装</label>
				<select name="taozhuang" id="taozhuang" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='7' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#taozhuang').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmmail'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmmail(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="请输入角色ID" />
			</div>
			<div class="form-group col-md-12">
				<label>选择物品</label>
				<select name="itemid" id="itemid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['itemid'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#itemid').select2( {placeholder: '请选择'} );</script>
			</div>
			<div class="form-group col-md-12">
				<label for="number">发送数量</label>
				<small style="color:blue">**必填**</small>
				<input type="number" class="form-control" name="number" value="" placeholder="请输入发送数量" />
			</div>
			<div class="form-group col-md-12">
			<label for="times">有效时间</label>
			<small style="color:blue">**必填：填‘0’永久有效。</small>
			<input type="number" class="form-control" name="times" value="0" placeholder="请输入有效时间" />
			</div>
			<div class="form-group col-md-12">
			<label for="min">最低等级</label>
			<small style="color:blue">**必填**</small>
			<input type="number" class="form-control" name="min" value="" placeholder="请输入最低等级" />
			</div>
			<div class="form-group col-md-12">
			<label for="max">最高等级</label>
			<small style="color:blue">**必填**</small>
			<input type="number" class="form-control" name="max" value="" placeholder="请输入最高等级" />
			</div>
			<div class="form-group col-md-12">
			<label for="info">邮件内容</label>
			<small style="color:blue">**必填**</small>
			<textarea class="form-control" rows="5" name="info" placeholder="请输入邮件内容" ></textarea>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='gmserver'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return gmserver(this)" method="post" class="row">
			<div class="form-group col-md-12">
				<label>选择大区</label>
				<select name="serverid" class="form-control">
				<?php
				$rs=$DB->query("SELECT * FROM `servers` order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
			</div>
			<div class="form-group col-md-12">
				<label for="roleid">角色ID</label>
				<small style="color:blue">**必填**</small>
				<input type="text" class="form-control" name="roleid" value="" placeholder="ID随便输就行" />
			</div>
			<div class="form-group col-md-12">
				<label for="caozuo">选择操作内容</label>
				<small style="color:red">以下操作响应较慢，请耐心等待</small>
				<select name="caozuo" class="form-control" id="caozuo" >
					<option value="1">不停服更新表格</option>
					<option value="2">友好关闭服务器</option>
				</select>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>

<script type="text/javascript" src="/static/admin/js/lightyear.js"></script>
<script src="/static/admin/js/bootstrap-notify.min.js"></script>
<script>
function gmserver(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=10',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmmail(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=9',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function payusermoney(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=payusermoney',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function payvip(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=payvip',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmzbdz(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=8',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmggsend(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=7',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmsenditem(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=6',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmpetdz(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=5',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmsendpet(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=4',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
			//layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
			lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        //lightyear.notify(data.msg, 'success', 3000);;
			lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
			layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmpetskill(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=3',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmsend(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=2',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function gmjichu(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=gm&gmtype=1',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function allgetmoney(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=allgetmoney',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	       // layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	       lightyear.notify(data.msg, 'success', 3000);
	      }else{
	        lightyear.notify(data.msg, 'success', 3000);
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>