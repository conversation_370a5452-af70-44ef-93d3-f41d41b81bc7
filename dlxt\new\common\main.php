<?php
/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-01-04 00:00:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-01-04 16:09:58
 * @FilePath: \dlxt\new\common\main.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
本后台只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
*/

	header('Content-type:text/html;charset=utf-8');
	if(defined('IN_CRONLITE'))return; 
	define('IN_CRONLITE', true);
	define('SYSTEM_ROOT', dirname(__FILE__).'/');
	define('SYS_KEY', 'samo2023mt3qdd666');
	define('ROOT', dirname(SYSTEM_ROOT).'/');
	date_default_timezone_set('PRC');
	
	// 修复日期处理逻辑
	$now = time();
	$date = date("Y-m-d", $now);
	$time = date("H:i:s", $now); // 注意这里是i不是m
	
	if(!is_file(__DIR__.'/number.php')){
		//exit('核心文件缺失，请立即处理');
	}
	
	// 设置内存限制
	ini_set('memory_limit', '256M');
	
	include_once(SYSTEM_ROOT."autoloader.php");
	Autoloader::register();
	//防注入脚本
	require_once('samo.php');
	//检测数据库
	require SYSTEM_ROOT.'config.php';
	
	try {
		$DB = new PDO("mysql:host={$dbconfig['host']};dbname={$dbconfig['dbname']};port={$dbconfig['port']}",$dbconfig['user'],$dbconfig['pwd']);
	}catch(Exception $e){
		exit('数据库1链接失败！');
	}
	
	try {
		$DB_B = new PDO("mysql:host={$dbconfig_b['host']};dbname={$dbconfig_b['dbname']};port={$dbconfig_b['port']}",$dbconfig_b['user'],$dbconfig_b['pwd']);
	}catch(Exception $e){
		//exit('数据库2链接失败！');
	}
	
//引入类
	$DB = new \lib\pdoHelper($dbconfig);
	
	$DB_B = new \lib\pdoHelper($dbconfig_b);
	
	$Admin = new \lib\adminclass();
	$Gets = new \lib\gets();
//检测版权
	$CheckMysql = $DB->getRow("select * from `config` where `keys`='quyoumao' ");
	
	
	if($CheckMysql['values'] != '934977452' ){
		header('Content-type:text/html;charset=utf-8');echo "请先导入数据库";exit();
	}
//引入方法
	include_once(SYSTEM_ROOT."functions.php");
//检测IP
	$onlineip = $Gets->my_ip();
	session_start();
	$ip = $Gets->ip();
	$city = $Gets->get_city($ip);
	$device = $Gets->device();
	$ipblackData = $DB->getRow("select * from `black_ip` where `ip`='".$ip."' ");
	if($ipblackData)exit('</b>此IP禁止访问！</b><br>');
	$qqwxjump=$DB->getRow("select * from `config` where `keys`='qqwxjump' limit 1");
	if($qqwxjump['values'] == 1){
		require_once(ROOT.'/static/qqwxjump/qqwxjump.php');
	}

// 定义允许包含的文件白名单
$allowed_includes = array(
    'functions.php',
    'samo.php',
    'config.php'
);

// 检查文件名是否在白名单中
function safe_include($filename) {
    global $allowed_includes;
    if(in_array(basename($filename), $allowed_includes)) {
        return include_once(SYSTEM_ROOT.$filename);
    }
    return false;
}

// 使用安全的include
safe_include('functions.php');
safe_include('samo.php');
?>