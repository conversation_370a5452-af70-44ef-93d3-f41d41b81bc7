/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableFilterControl={exports:{}}.exports}})(this,function(){'use strict';function a(a,b){if(!(a instanceof b))throw new TypeError('Cannot call a class as a function')}function b(a,b){if(!a)throw new ReferenceError('this hasn\'t been initialised - super() hasn\'t been called');return b&&('object'==typeof b||'function'==typeof b)?b:a}function c(a,b){if('function'!=typeof b&&null!==b)throw new TypeError('Super expression must either be null or a function, not '+typeof b);a.prototype=Object.create(b&&b.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(a,b):a.__proto__=b)}var d=function(){function a(a,b){for(var c,d=0;d<b.length;d++)c=b[d],c.enumerable=c.enumerable||!1,c.configurable=!0,'value'in c&&(c.writable=!0),Object.defineProperty(a,c.key,c)}return function(b,c,d){return c&&a(b.prototype,c),d&&a(b,d),b}}(),e=function a(b,c,d){null===b&&(b=Function.prototype);var e=Object.getOwnPropertyDescriptor(b,c);if(e===void 0){var f=Object.getPrototypeOf(b);return null===f?void 0:a(f,c,d)}if('value'in e)return e.value;var g=e.get;return void 0===g?void 0:g.call(d)};(function(f){var g=f.fn.bootstrapTable.utils,h={getOptionsFromSelectControl:function(a){return a.get(a.length-1).options},hideUnusedSelectOptions:function(a,b){for(var c=h.getOptionsFromSelectControl(a),d=0;d<c.length;d++)''!==c[d].value&&(b.hasOwnProperty(c[d].value)?a.find(g.sprintf('option[value=\'%s\']',c[d].value)).show():a.find(g.sprintf('option[value=\'%s\']',c[d].value)).hide())},addOptionToSelectControl:function(a,b,c){var d=f.trim(b),e=f(a.get(a.length-1));h.existOptionInSelectControl(a,d)||e.append(f('<option></option>').attr('value',d).text(f('<div />').html(c).text()))},sortSelectControl:function(a){var b=f(a.get(a.length-1)),c=b.find('option:gt(0)');c.sort(function(c,a){var b=f(c).text().toLowerCase(),d=f(a).text().toLowerCase();return f.isNumeric(c)&&f.isNumeric(a)&&(b=parseFloat(b),d=parseFloat(d)),b>d?1:b<d?-1:0}),b.find('option:gt(0)').remove(),b.append(c)},existOptionInSelectControl:function(a,b){for(var c=h.getOptionsFromSelectControl(a),d=0;d<c.length;d++)if(c[d].value===b.toString())return!0;return!1},fixHeaderCSS:function(a){var b=a.$tableHeader;b.css('height','77px')},getCurrentHeader:function(a){var b=a.$header,c=a.options,d=a.$tableHeader,e=b;return c.height&&(e=d),e},getCurrentSearchControls:function(a){var b=a.options,c='select, input';return b.height&&(c='table select, table input'),c},getCursorPosition:function(a){if(g.isIEBrowser()){if(f(a).is('input[type=text]')){var d=0;if('selectionStart'in a)d=a.selectionStart;else if('selection'in document){a.focus();var b=document.selection.createRange(),c=document.selection.createRange().text.length;b.moveStart('character',-a.value.length),d=b.text.length-c}return d}return-1}return-1},setCursorPosition:function(a){f(a).val(a.value)},copyValues:function(a){var b=h.getCurrentHeader(a),c=h.getCurrentSearchControls(a);a.options.valuesFilterControl=[],b.find(c).each(function(){a.options.valuesFilterControl.push({field:f(this).closest('[data-field]').data('field'),value:f(this).val(),position:h.getCursorPosition(f(this).get(0)),hasFocus:f(this).is(':focus')})})},setValues:function(a){var b=null,c=[],d=h.getCurrentHeader(a),e=h.getCurrentSearchControls(a);if(0<a.options.valuesFilterControl.length){var g=null;d.find(e).each(function(){b=f(this).closest('[data-field]').data('field'),c=a.options.valuesFilterControl.filter(function(a){return a.field===b}),0<c.length&&(f(this).val(c[0].value),c[0].hasFocus&&(g=function(a,b){return function closedCallback(){a.focus(),h.setCursorPosition(a,b)}}(f(this).get(0),c[0].position)))}),null!==g&&g()}},collectBootstrapCookies:function(){var a=[],b=document.cookie.match(/(?:bs.table.)(\w*)/g);if(b)return f.each(b,function(b,c){var d=c;/./.test(d)&&(d=d.split('.').pop()),-1===f.inArray(d,a)&&a.push(d)}),a},escapeID:function(a){return(a+'').replace(/(:|\.|\[|\]|,)/g,'\\$1')},isColumnSearchableViaSelect:function(a){var b=a.filterControl,c=a.searchable;return b&&'select'===b.toLowerCase()&&c},isFilterDataNotGiven:function(a){var b=a.filterData;return b===void 0||'column'===b.toLowerCase()},hasSelectControlElement:function(a){return a&&0<a.length},initFilterSelectControls:function(a){var b=a.data,c=a.pageTo<a.options.data.length?a.options.data.length:a.pageTo,d=a.options.pagination?'server'===a.options.sidePagination?a.pageTo:a.options.totalRows:a.pageTo;f.each(a.header.fields,function(c,e){var j=a.columns[a.fieldsColumnsIndex[e]],k=f('.bootstrap-table-filter-control-'+h.escapeID(j.field));if(h.isColumnSearchableViaSelect(j)&&h.isFilterDataNotGiven(j)&&h.hasSelectControlElement(k)){0===k.get(k.length-1).options.length&&h.addOptionToSelectControl(k,'',j.filterControlPlaceholder);for(var l={},m=0;m<d;m++){var i=b[m][e],n=g.calculateObjectValue(a.header,a.header.formatters[c],[i,b[m],m],i);l[n]=i}for(var o in l)h.addOptionToSelectControl(k,l[o],o);h.sortSelectControl(k),a.options.hideUnusedSelectOptions&&h.hideUnusedSelectOptions(k,l)}}),a.trigger('created-controls')},getFilterDataMethod:function(a,b){for(var c=Object.keys(a),d=0;d<c.length;d++)if(c[d]===b)return a[b];return null},createControls:function(a,b){var c=!1,d=void 0,e=void 0;f.each(a.columns,function(g,j){if(d='hidden',e=[],!!j.visible){if(!j.filterControl)e.push('<div class="no-filter-control"></div>');else{e.push('<div class="filter-control">');var p=j.filterControl.toLowerCase();j.searchable&&a.options.filterTemplate[p]&&(c=!0,d='visible',e.push(a.options.filterTemplate[p](a,j.field,d,j.filterControlPlaceholder?j.filterControlPlaceholder:'','filter-control-'+g)))}if(f.each(b.children().children(),function(a,b){var c=f(b);if(c.data('field')===j.field)return c.find('.fht-cell').append(e.join('')),!1}),void 0!==j.filterData&&'column'!==j.filterData.toLowerCase()){var k=h.getFilterDataMethod(i,j.filterData.substring(0,j.filterData.indexOf(':'))),l=void 0,m=void 0;if(null!==k)l=j.filterData.substring(j.filterData.indexOf(':')+1,j.filterData.length),m=f('.bootstrap-table-filter-control-'+h.escapeID(j.field)),h.addOptionToSelectControl(m,'',j.filterControlPlaceholder),k(l,m);else throw new SyntaxError('Error. You should use any of these allowed filter data methods: var, json, url. Use like this: var: {key: "value"}');var n=void 0,o=void 0;switch(k){case'url':f.ajax({url:l,dataType:'json',success:function(a){for(var b in a)h.addOptionToSelectControl(m,b,a[b]);h.sortSelectControl(m)}});break;case'var':for(o in n=window[l],n)h.addOptionToSelectControl(m,o,n[o]);h.sortSelectControl(m);break;case'jso':for(o in n=JSON.parse(l),n)h.addOptionToSelectControl(m,o,n[o]);h.sortSelectControl(m);}}}}),c?(b.off('keyup','input').on('keyup','input',function(b,c){if((b.keyCode=c?c.keyCode:b.keyCode,!(a.options.searchOnEnterKey&&13!==b.keyCode))&&!(-1<f.inArray(b.keyCode,[37,38,39,40]))){var d=f(b.currentTarget);d.is(':checkbox')||d.is(':radio')||(clearTimeout(b.currentTarget.timeoutId||0),b.currentTarget.timeoutId=setTimeout(function(){a.onColumnSearch(b)},a.options.searchTimeOut))}}),b.off('change','select').on('change','select',function(b){a.options.searchOnEnterKey&&13!==b.keyCode||-1<f.inArray(b.keyCode,[37,38,39,40])||(clearTimeout(b.currentTarget.timeoutId||0),b.currentTarget.timeoutId=setTimeout(function(){a.onColumnSearch(b)},a.options.searchTimeOut))}),b.off('mouseup','input').on('mouseup','input',function(b){var c=f(this),d=c.val();''===d||setTimeout(function(){var d=c.val();''===d&&(clearTimeout(b.currentTarget.timeoutId||0),b.currentTarget.timeoutId=setTimeout(function(){a.onColumnSearch(b)},a.options.searchTimeOut))},1)}),0<b.find('.date-filter-control').length&&f.each(a.columns,function(a,c){var d=c.filterControl,e=c.field,g=c.filterDatepickerOptions;d!==void 0&&'datepicker'===d.toLowerCase()&&b.find('.date-filter-control.bootstrap-table-filter-control-'+e).datepicker(g).on('changeDate',function(a){var b=a.currentTarget;f(b).val(b.value),f(b).keyup()})})):b.find('.filterControl').hide()},getDirectionOfSelectOptions:function(a){var b=a===void 0?'left':a.toLowerCase();return'left'===b?'ltr':'right'===b?'rtl':'auto'===b?'auto':'ltr'}},i={var:function(a,b){var c=window[a];for(var d in c)h.addOptionToSelectControl(b,d,c[d]);h.sortSelectControl(b)},url:function(a,b){f.ajax({url:a,dataType:'json',success:function(a){for(var c in a)h.addOptionToSelectControl(b,c,a[c]);h.sortSelectControl(b)}})},json:function(a,b){var c=JSON.parse(a);for(var d in c)h.addOptionToSelectControl(b,d,c[d]);h.sortSelectControl(b)}},j={3:{icons:{clear:'glyphicon-trash icon-clear'}},4:{icons:{clear:'fa-trash icon-clear'}}}[g.bootstrapVersion];f.extend(f.fn.bootstrapTable.defaults,{filterControl:!1,onColumnSearch:function(){return!1},onCreatedControls:function(){return!0},filterShowClear:!1,alignmentSelectControlOptions:void 0,filterTemplate:{input:function(a,b,c,d){return g.sprintf('<input type="text" class="form-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s" placeholder="%s">',b,c,d)},select:function(a,b,c){var d=a.options;return g.sprintf('<select class="form-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s" dir="%s"></select>',b,c,h.getDirectionOfSelectOptions(d.alignmentSelectControlOptions))},datepicker:function(a,b,c){return g.sprintf('<input type="text" class="form-control date-filter-control bootstrap-table-filter-control-%s" style="width: 100%; visibility: %s">',b,c)}},disableControlWhenSearch:!1,searchOnEnterKey:!1,valuesFilterControl:[]}),f.extend(f.fn.bootstrapTable.columnDefaults,{filterControl:void 0,filterData:void 0,filterDatepickerOptions:void 0,filterStrictSearch:!1,filterStartsWithSearch:!1,filterControlPlaceholder:''}),f.extend(f.fn.bootstrapTable.Constructor.EVENTS,{"column-search.bs.table":'onColumnSearch',"created-controls.bs.table":'onCreatedControls'}),f.extend(f.fn.bootstrapTable.defaults.icons,{clear:j.icons.clear}),f.extend(f.fn.bootstrapTable.locales,{formatClearFilters:function(){return'Clear Filters'}}),f.extend(f.fn.bootstrapTable.defaults,f.fn.bootstrapTable.locales),f.fn.bootstrapTable.methods.push('triggerSearch'),f.fn.bootstrapTable.methods.push('clearFilterControl'),f.BootstrapTable=function(i){function j(){return a(this,j),b(this,(j.__proto__||Object.getPrototypeOf(j)).apply(this,arguments))}return c(j,i),d(j,[{key:'init',value:function(){if(this.options.filterControl){var a=this;this.options.valuesFilterControl=[],this.$el.on('reset-view.bs.table',function(){!a.options.height||0<a.$tableHeader.find('select').length||0<a.$tableHeader.find('input').length||h.createControls(a,a.$tableHeader)}).on('post-header.bs.table',function(){h.setValues(a)}).on('post-body.bs.table',function(){a.options.height&&h.fixHeaderCSS(a)}).on('column-switch.bs.table',function(){h.setValues(a)}).on('load-success.bs.table',function(){a.EnableControls(!0)}).on('load-error.bs.table',function(){a.EnableControls(!0)})}e(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),'init',this).call(this)}},{key:'initToolbar',value:function(){if(this.showToolbar=this.showToolbar||this.options.filterControl&&this.options.filterShowClear,e(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),'initToolbar',this).call(this),this.options.filterControl&&this.options.filterShowClear){var a=this.$toolbar.find('>.btn-group'),b=a.find('.filter-show-clear');b.length||(b=f([g.sprintf('<button class="btn btn-%s filter-show-clear" ',this.options.buttonsClass),g.sprintf('type="button" title="%s">',this.options.formatClearFilters()),g.sprintf('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.clear),'</button>'].join('')).appendTo(a),b.off('click').on('click',f.proxy(this.clearFilterControl,this)))}}},{key:'initHeader',value:function(){e(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),'initHeader',this).call(this),this.options.filterControl&&h.createControls(this,this.$header)}},{key:'initBody',value:function(){e(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),'initBody',this).call(this),h.initFilterSelectControls(this)}},{key:'initSearch',value:function(){var a=this,b=f.isEmptyObject(a.filterColumnsPartial)?null:a.filterColumnsPartial;(null===b||1>=Object.keys(b).length)&&e(j.prototype.__proto__||Object.getPrototypeOf(j.prototype),'initSearch',this).call(this),'server'===this.options.sidePagination||null===b||(a.data=b?a.options.data.filter(function(c,d){var e=[];return Object.keys(c).forEach(function(g){var h=a.columns[a.fieldsColumnsIndex[g]],i=(b[g]||'').toLowerCase(),j=c[g];''===i?e.push(!0):(h&&h.searchFormatter&&(j=f.fn.bootstrapTable.utils.calculateObjectValue(a.header,a.header.formatters[f.inArray(g,a.header.fields)],[j,c,d],j)),-1!==f.inArray(g,a.header.fields)&&('string'==typeof j||'number'==typeof j)&&(h.filterStrictSearch?j.toString().toLowerCase()===i.toString().toLowerCase()?e.push(!0):e.push(!1):h.filterStartsWithSearch?0===(''+j).toLowerCase().indexOf(i)?e.push(!0):e.push(!1):-1===(''+j).toLowerCase().indexOf(i)?e.push(!1):e.push(!0)))}),-1===e.indexOf(!1)}):a.data)}},{key:'initColumnSearch',value:function(a){if(h.copyValues(this),a)for(var b in this.filterColumnsPartial=a,this.updatePagination(),a)this.trigger('column-search',b,a[b])}},{key:'onColumnSearch',value:function(a){if(!(-1<f.inArray(a.keyCode,[37,38,39,40]))){h.copyValues(this);var b=f.trim(f(a.currentTarget).val()),c=f(a.currentTarget).closest('[data-field]').data('field');f.isEmptyObject(this.filterColumnsPartial)&&(this.filterColumnsPartial={}),b?this.filterColumnsPartial[c]=b:delete this.filterColumnsPartial[c],this.searchText+='randomText',this.options.pageNumber=1,this.EnableControls(!1),this.onSearch(a),this.trigger('column-search',c,b)}}},{key:'clearFilterControl',value:function(){if(this.options.filterControl&&this.options.filterShowClear){var a=this,b=h.collectBootstrapCookies(),c=h.getCurrentHeader(a),d=c.closest('table'),e=c.find(h.getCurrentSearchControls(a)),i=a.$toolbar.find('.search input'),j=!1,k=0;if(f.each(a.options.valuesFilterControl,function(a,b){j=!!j||''!==b.value,b.value=''}),h.setValues(a),clearTimeout(k),k=setTimeout(function(){b&&0<b.length&&f.each(b,function(b,c){void 0!==a.deleteCookie&&a.deleteCookie(c)})},a.options.searchTimeOut),!j)return;if(0<e.length)this.filterColumnsPartial={},f(e[0]).trigger('INPUT'===e[0].tagName?'keyup':'change',{keyCode:13});else return;if(0<i.length&&a.resetSearch(),a.options.sortName!==d.data('sortName')||a.options.sortOrder!==d.data('sortOrder')){var l=c.find(g.sprintf('[data-field="%s"]',f(e[0]).closest('table').data('sortName')));0<l.length&&(a.onSort({type:'keypress',currentTarget:l}),f(l).find('.sortable').trigger('click'))}}}},{key:'triggerSearch',value:function(){var a=h.getCurrentHeader(this),b=h.getCurrentSearchControls(this);a.find(b).each(function(){var a=f(this);a.is('select')?a.change():a.keyup()})}},{key:'EnableControls',value:function(a){if(this.options.disableControlWhenSearch&&'server'===this.options.sidePagination){var b=h.getCurrentHeader(this),c=h.getCurrentSearchControls(this);a?b.find(c).removeProp('disabled'):b.find(c).prop('disabled','disabled')}}}]),j}(f.BootstrapTable)})(jQuery)});