/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTablePrint={exports:{}}.exports}})(this,function(){'use strict';(function(a){function b(a){return'<html><head><style type="text/css" media="print">  @page { size: auto;   margin: 25px 0 25px 0; }</style><style type="text/css" media="all">table{border-collapse: collapse; font-size: 12px; }\ntable, th, td {border: 1px solid grey}\nth, td {text-align: center; vertical-align: middle;}\np {font-weight: bold; margin-left:20px }\ntable { width:94%; margin-left:3%; margin-right:3%}\ndiv.bs-table-print { text-align:center;}\n</style></head><title>Print Table</title><body><p>Printed on: '+new Date+' </p><div class="bs-table-print">'+a+'</div></body></html>'}var c=a.fn.bootstrapTable.utils.sprintf;a.extend(a.fn.bootstrapTable.defaults,{showPrint:!1,printAsFilteredAndSortedOnUI:!0,printSortColumn:void 0,printSortOrder:'asc',printPageBuilder:function(a){return b(a)}}),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{printFilter:void 0,printIgnore:!1,printFormatter:void 0}),a.extend(a.fn.bootstrapTable.defaults.icons,{print:'glyphicon-print icon-share'});var d=a.fn.bootstrapTable.Constructor,e=d.prototype.initToolbar;d.prototype.initToolbar=function(){if(this.showToolbar=this.showToolbar||this.options.showPrint,e.apply(this,Array.prototype.slice.apply(arguments)),this.options.showPrint){var b=this,d=this.$toolbar.find('>.btn-group'),f=d.find('button.bs-print');f.length||(f=a(['<button class="bs-print btn btn-default'+c(' btn-%s"',this.options.iconSize)+' name="print" title="print" type="button">',c('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.print),'</button>'].join('')).appendTo(d),f.click(function(){function a(a,b,c){var d=a[c.field];return'function'==typeof c.printFormatter?c.printFormatter.apply(c,[d,a,b]):'undefined'==typeof d?'-':d}function d(b,d){for(var e,f=['<table><thead>'],g=0;g<d.length;g++){e=d[g],f.push('<tr>');for(var k=0;k<e.length;k++)e[k].printIgnore||f.push('<th',c(' rowspan="%s"',e[k].rowspan),c(' colspan="%s"',e[k].colspan),c('>%s</th>',e[k].title));f.push('</tr>')}f.push('</thead><tbody>');for(var h=0;h<b.length;h++){f.push('<tr>');for(var e,i=0;i<d.length;i++){e=d[i];for(var l=0;l<e.length;l++)!e[l].printIgnore&&e[l].field&&f.push('<td>',a(b[h],h,e[l]),'</td>')}f.push('</tr>')}return f.push('</tbody></table>'),f.join('')}function e(a,c,b){if(!c)return a;var d='asc'!=b;return d=-(+d||-1),a.sort(function(e,a){return d*e[c].localeCompare(a[c])})}function f(a,b){for(var c=0;c<b.length;++c)if(a[b[c].colName]!=b[c].value)return!1;return!0}function g(a,b){return a.filter(function(a){return f(a,b)})}function h(a){return a&&a[0]?a[0].filter(function(a){return a.printFilter}).map(function(a){return{colName:a.field,value:a.printFilter}}):[]}(function doPrint(a){a=g(a,h(b.options.columns)),a=e(a,b.options.printSortColumn,b.options.printSortOrder);var c=d(a,b.options.columns),f=window.open('');f.document.write(b.options.printPageBuilder.call(this,c)),f.print(),f.close()})(b.options.printAsFilteredAndSortedOnUI?b.getData():b.options.data.slice(0))}))}}})(jQuery)});