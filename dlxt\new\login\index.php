<?php
/*
本后台只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
----
*/


include '../common/main.php';
if(isset($_SESSION['adminLogin'])){
	if(isset($_SESSION['type']) && $_SESSION['type'] == 1){
		header('Location:./admin');
		exit;
	}else if(isset($_SESSION['type']) && $_SESSION['type'] == 2){
		header('Location:./agent');
		exit;
	}
}
//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
//版权信息
$banquan=$DB->getRow("select * from `config` where `keys`='banquan' limit 1");
//版权信息
$logoimg=$DB->getRow("select * from `config` where `keys`='logoimg' limit 1");
//版权信息
$bjimg=$DB->getRow("select * from `config` where `keys`='bjimg' limit 1");
?>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title>代理中心登陆 - <?php echo $title['values'];?></title>
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
<style>
.lyear-wrapper {
    position: relative;
}
.lyear-login {
    display: flex !important;
    min-height: 100vh;
    align-items: center !important;
    justify-content: center !important;
}
.lyear-login:after{
    content: '';
    min-height: inherit;
    font-size: 0;
}
.login-center {
    background: #fff;
    min-width: 29.25rem;
    padding: 2.14286em 3.57143em;
    border-radius: 3px;
    margin: 2.85714em;
}
.login-header {
    margin-bottom: 1.5rem !important;
}
.login-center .has-feedback.feedback-left .form-control {
    padding-left: 38px;
    padding-right: 12px;
}
.login-center .has-feedback.feedback-left .form-control-feedback {
    left: 0;
    right: auto;
    width: 38px;
    height: 38px;
    line-height: 38px;
    z-index: 4;
    color: #dcdcdc;
}
.login-center .has-feedback.feedback-left.row .form-control-feedback {
    left: 15px;
}
</style>
</head>
  
<body>
<div class="row lyear-wrapper" style="background-image: url(<?php echo $bjimg['values'];?>); background-size: cover;">
  <div class="lyear-login" style="opacity:0.94;">
    <div class="login-center">
      <div class="login-header text-center">
        <a href="/"> <img alt="light year admin" src="<?php echo $logoimg['values'];?>"> </a>
      </div>
      <form action="#!" method="post">
        <div class="form-group has-feedback feedback-left">
          <input type="text" value="" placeholder="请输入您的用户名" class="form-control" name="username" id="username" />
          <span class="mdi mdi-account form-control-feedback" aria-hidden="true"></span>
        </div>
        <div class="form-group has-feedback feedback-left">
          <input type="password" value="" placeholder="请输入密码" class="form-control" id="password" name="password" />
          <span class="mdi mdi-lock form-control-feedback" aria-hidden="true"></span>
        </div>
        <div class="form-group">
          <button class="btn btn-block btn-dark" type="button" id="submit">立即登录</button>
        </div>
      </form>
      <hr>
      <footer class="col-sm-12 text-center">
        <p class="m-b-0"><?php echo $banquan['values'];?></p>
      </footer>
    </div>
  </div>
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script src="/static/admin/js/bootstrap-notify.min.js"></script>
<script type="text/javascript" src="/static/admin/js/lightyear.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/js/jconfirm/jquery-confirm.min.js"></script>
<script type="text/javascript">
$(document).ready(function(){
  $("#submit").click(function(){
      var username = $("input[name='username']").val();
      var password = $("input[name='password']").val();
      var data = {username:username,password:password};
      var login = $("button[type='submit']");
      if(username.length < 1 || password.length < 1){
		  lightyear.notify('请确保账号密码都不为空！', 'danger', 3000);
          return false;
      }
      login.attr('disabled', 'true');
      lightyear.notify('正在登陆请稍后……', 'danger', 3000);
      $.ajax({
        type:'POST',
        url:'ajax.php?act=login',
        data: Object.assign(data),
        dataType:'json',
        success:function (data){
            if(data.code == 1){
              setTimeout(function (){
                  location.href = './admin'
              },1000);
			  lightyear.notify('登陆成功，即将进入后台~', 'success', 3000);
            }else if(data.code == 2){
              setTimeout(function (){
                  location.href = './agent'
              },1000);
			  lightyear.notify('登陆成功，即将进入后台~', 'success', 3000);
            }else{
              login.removeAttr('disabled');
			  lightyear.notify(data.msg, 'success', 3000);
              reset();
            }
          }
      });
      return false;
  });
});</script>
</body>
</html>