<?php
/**
 * 快速测试支付接口返回内容
 */

require_once("config.php");

// 测试参数
$params = array(
    'pid' => $epay_config['pid'],
    'type' => 'wxpay',
    'out_trade_no' => 'TEST_' . time(),
    'notify_url' => 'http://test.com/notify',
    'return_url' => 'http://test.com/return',
    'name' => '测试商品',
    'money' => '0.01'
);

// 计算签名
ksort($params);
$signstr = '';
foreach($params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$sign = md5($signstr);

$params['sign'] = $sign;
$params['sign_type'] = 'MD5';

// 请求API
$url = $epay_config['apiurl'] . 'mapi.php';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
curl_close($ch);

echo "<h2>微信支付API响应:</h2>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

$data = json_decode($response, true);
if ($data && isset($data['payurl'])) {
    echo "<h3>支付链接:</h3>";
    echo "<p>" . htmlspecialchars($data['payurl']) . "</p>";
    
    if (strpos($data['payurl'], 'weixin://') === 0) {
        echo "<p style='color:red;'>⚠️ 这是微信URL scheme - 这就是问题所在！</p>";
    }
}

// 测试支付宝
$params['type'] = 'alipay';
$params['out_trade_no'] = 'TEST_ALIPAY_' . time();

// 重新计算签名
unset($params['sign']);
ksort($params);
$signstr = '';
foreach($params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$sign = md5($signstr);
$params['sign'] = $sign;

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
curl_close($ch);

echo "<h2>支付宝API响应:</h2>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

$data = json_decode($response, true);
if ($data && isset($data['payurl'])) {
    echo "<h3>支付链接:</h3>";
    echo "<p>" . htmlspecialchars($data['payurl']) . "</p>";
    
    if (strpos($data['payurl'], 'alipays://') === 0) {
        echo "<p style='color:red;'>⚠️ 这是支付宝URL scheme - 这就是问题所在！</p>";
    }
}
?>
