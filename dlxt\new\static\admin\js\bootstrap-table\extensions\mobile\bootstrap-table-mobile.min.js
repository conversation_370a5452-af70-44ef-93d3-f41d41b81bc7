/**
  * bootstrap-table - An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).
  *
  * @version v1.14.2
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

(function(a,b){if('function'==typeof define&&define.amd)define([],b);else if('undefined'!=typeof exports)b();else{b(),a.bootstrapTableMobile={exports:{}}.exports}})(this,function(){'use strict';!function(a){var b=function(b,c){0<b.options.columnsHidden.length&&a.each(b.columns,function(a,d){-1!==b.options.columnsHidden.indexOf(d.field)&&d.visible!==c&&b.toggleColumn(b.fieldsColumnsIndex[d.field],c,!0)})},c=function(a){(a.options.height||a.options.showFooter)&&setTimeout(function(){a.resetView.call(a)},1)},d=function(a,b,d){a.options.minHeight?b<=a.options.minWidth&&d<=a.options.minHeight?e(a):b>a.options.minWidth&&d>a.options.minHeight&&f(a):b<=a.options.minWidth?e(a):b>a.options.minWidth&&f(a),c(a)},e=function(a){g(a,!1),b(a,!1)},f=function(a){g(a,!0),b(a,!0)},g=function(a,b){a.options.cardView=b,a.toggleView()},h=function(a,b){var c;return function(){var d=this,e=arguments;clearTimeout(c),c=setTimeout(function later(){c=null,a.apply(d,e)},b)}};a.extend(a.fn.bootstrapTable.defaults,{mobileResponsive:!1,minWidth:562,minHeight:void 0,heightThreshold:100,checkOnInit:!0,columnsHidden:[]});var i=a.fn.bootstrapTable.Constructor,j=i.prototype.init;i.prototype.init=function(){if((j.apply(this,Array.prototype.slice.apply(arguments)),!!this.options.mobileResponsive)&&this.options.minWidth){100>this.options.minWidth&&this.options.resizable&&(console.log('The minWidth when the resizable extension is active should be greater or equal than 100'),this.options.minWidth=100);var b=this,c={width:a(window).width(),height:a(window).height()};if(a(window).on('resize orientationchange',h(function(){var e=a(this).height(),f=a(this).width();(Math.abs(c.height-e)>b.options.heightThreshold||c.width!=f)&&(d(b,f,e),c={width:f,height:e})},200)),this.options.checkOnInit){var e=a(window).height(),f=a(window).width();d(this,f,e),c={width:f,height:e}}}}}(jQuery)});