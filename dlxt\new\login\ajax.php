<?php
/*
本后台只允许自行研究使用
切勿用于非法用途，否则后果自负
如用于非法用途使用，所产生的一切后果，与本人及社区无关
----
*/
include '../common/main.php';
$act=isset($get['act'])?daddslashes($get['act']):null;
switch($act){
    case 'login':
		$username = addslashes($post['username']);
		$password = addslashes($post['password']);
        if ($username=='' || $password=='') {
            exit('{"code":0,"msg":"请确保账号密码都不为空"}');
        }
		$adminData = $Admin->getAdmin($username);
        if(empty($adminData))exit('{"code":0,"msg":"此账户不存在"}');
        if($adminData['status'] != 1)exit('{"code":0,"msg":"此账户已被封禁"}');
		$salt = $Admin->salt($username,$password);
		if($salt != $adminData['salt'])exit('{"code":0,"msg":"验证失败"}');
		$pass = md5($password.$salt.$username);
		//var_dump($pass);exit();
        if($pass != $adminData['password'])exit('{"code":0,"msg":"密码错误"}');
        $loginSql = "UPDATE `admin` SET `ip` = '$ip',`city` = '$city' WHERE `username` = '$username'";
		$loginAdmin = $DB->exec($loginSql);
        $_SESSION['adminUser'] = $username;
        $_SESSION['adminPass'] = $pass;
		$session=md5($adminData['id'].$username.$adminData['id']);
		$token=authcode("{$username}\t{$session}", 'ENCODE', SYS_KEY);
		setcookie("admin_token", $token, time() + 7200);
        $DB->query("insert into `admin_log` (`username`,`info`,`data`,`ip`,`city`) values ('$username','登陆后台管理中心', NOW(), '$ip', '$city')");
		if($adminData['type'] == 1 || $adminData['type'] == 2){
			$_SESSION['type'] = $adminData['type'];
            exit('{"code":'.$adminData['type'].',"msg":"登录成功，请稍后..."}');
		}else{
            exit('{"code":0,"msg":"登录失败，请重试..."}');
		}
    break;
    case 'logout':
		setcookie("admin_token", "", time() - 7200); 
		unset($_SESSION['adminUser']);
		unset($_SESSION['adminPass']);
		unset($_SESSION['type']);
		unset($_SESSION['adminLogin']);
		header('Location:./');
		exit;
    break;
    default:
        exit('{"code":-4,"msg":"No Act"}');
    break;
}
?>