<?php
include('./auth.php');
if(isset($get['value']) && !empty($get['value'])) {
	if(isset($get['column']) && !empty($get['column'])) {
		if(isset($get['like']) && !empty($get['like'])) {
		$sql=" `{$get['column']}` like '%{$get['value']}%'";
		}else{
		$sql=" `{$get['column']}` = '{$get['value']}'";
		}
	}else{
	$sql=" 1";
	}
	$link='&like='.$get['value'].'&my=search&column='.$get['column'].'&value='.$get['value'];
}else{
	$sql=" 1";
}
$numrows=$DB->getColumn("SELECT count(*) from servers WHERE{$sql}");
?>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                	<th>ID</th>
                	<th>区组名称</th>
                	<th>大区名称</th>
                	<th>开服日期</th>
					<th>大区ID</th>
                	<th>大区IP</th>
                	<th>大区端口</th>
                	<th>区服状态</th>
                	<th>区服标志</th>
                	<th>GM端口</th>
                	<th>平台币充值比例</th>
                	<th>累计充值比例</th>
                	<th>充值赠送VIP经验比例</th>
                	<th>充值赠送仙玉比例</th>
                	<th>操作</th>
                </tr>
            </thead>
          	<tbody>
<?php
$pagesize=30;
$pages=ceil($numrows/$pagesize);
$page=isset($get['page'])?intval($get['page']):1;
$offset=$pagesize*($page - 1);

$rs=$DB->query("SELECT * FROM servers WHERE{$sql} order by id limit $offset,$pagesize");
while($res = $rs->fetch())
{
	switch ($res['deng']){
		case 1:
			$deng = '<span class="label label-dark">繁忙(黄色)</span>';
		break;
		case 2:
			$deng = '<span class="label label-danger">流畅(绿色)</span>';
		break;
		case 3:
			$deng = '<span class="label label-secondary">正常(棕色)</span>';
		break;
		default:
			$deng = '<span class="label label-default">未选择</span>';
	}
	switch ($res['biao']){
		case 0:
			$biao = '<span class="label label-warning">推荐</span>';
		break;
		case 1:
			$biao = '<span class="label label-info">新服</span>';
		break;
		case 2:
			$biao = '<span class="label label-success">推荐+新服</span>';
		break;
		case 3:
			$biao = '<span class="label label-primary">无标志</span>';
		break;
		default:
			$biao = '<span class="label label-default">未选择</span>';
	}
echo '<tr>
<td><b>'.$res['id'].'</b></td>
<td>'.$res['quzuname'].'</td>
<td>'.$res['name'].'</td>
<td>'.$res['info'].'开服</td>
<td>'.$res['serverid'].'</td>
<td>'.$res['ip'].'</td>
<td>'.$res['quport'].'</td>
<td>'.$deng.'</td>
<td>'.$biao.'</td>
<td>'.$res['port'].'</td>
<td><span class="label label-default"><b>1：'.$res['ptb'].'</b></span></td>
<td><span class="label label-default"><b>1：'.$res['charge'].'</b></span></td>
<td><span class="label label-default"><b>1：'.$res['vip'].'</b></span></td>
<td><span class="label label-default"><b>1：'.$res['xianyu'].'</b></span></td>
<td>
<a href="./serverset.php?act=edit&id='.$res['id'].'" class="btn btn-w-xs btn-warning">编辑</a>&nbsp;
<a href="javascript:deleteserver('.$res['id'].')" class="btn btn-w-xs btn-danger">删除</a>
</td>
</tr>';
}
?>
          </tbody>
        </table>
      </div>
<?php
echo'<div class="text-center"><ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$first.$link.'\')">首页</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$prev.$link.'\')">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<$page;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
echo '<li class="disabled"><a>'.$page.'</a></li>';
for ($i=$page+1;$i<=$end;$i++)
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$i.$link.'\')">'.$i .'</a></li>';
if ($page<$pages)
{
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$next.$link.'\')">&raquo;</a></li>';
echo '<li><a href="javascript:void(0)" onclick="listTable(\'page='.$last.$link.'\')">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo'</ul></div>';
