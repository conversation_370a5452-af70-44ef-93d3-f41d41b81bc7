<?php
/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-05-29 20:34:08
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-05-29 20:43:20
 * @FilePath: \dlxt\chongzhi\pay\verify_config.php
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 验证支付配置
include "config.php";

echo "<h2>支付配置验证</h2>";

// 验证商户信息
$api_url = $epay_config['apiurl'] . "api.php?act=query&pid=" . $epay_config['pid'] . "&key=" . $epay_config['key'];

echo "<h3>商户信息验证：</h3>";
echo "API地址: {$api_url}<br>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: {$http_code}<br>";
echo "API响应: {$response}<br>";

if($response) {
    $result = json_decode($response, true);
    if($result && isset($result['code'])) {
        if($result['code'] == 1) {
            echo "✅ 商户配置正确<br>";
            echo "商户ID: {$result['pid']}<br>";
            echo "商户状态: " . ($result['active'] == 1 ? '正常' : '封禁') . "<br>";
            echo "商户余额: {$result['money']}<br>";
            echo "订单总数: {$result['orders']}<br>";
        } else {
            echo "❌ 商户配置错误: " . (isset($result['msg']) ? $result['msg'] : '未知错误') . "<br>";
        }
    } else {
        echo "❌ API响应格式错误<br>";
    }
} else {
    echo "❌ 无法连接到支付API<br>";
}

// 测试签名算法
echo "<h3>签名算法测试：</h3>";
$test_params = array(
    'pid' => $epay_config['pid'],
    'type' => 'alipay',
    'out_trade_no' => 'TEST123456',
    'notify_url' => 'http://test.com/notify',
    'return_url' => 'http://test.com/return',
    'name' => '测试商品',
    'money' => '1.00'
);

require_once("lib/EpayCore.class.php");
$epay = new EpayCore($epay_config);

// 手动计算签名
ksort($test_params);
$signstr = '';
foreach($test_params as $k => $v){
    if($k != "sign" && $k != "sign_type" && $v!=''){
        $signstr .= $k.'='.$v.'&';
    }
}
$signstr = substr($signstr,0,-1);
$signstr .= $epay_config['key'];
$manual_sign = md5($signstr);

echo "测试参数: " . json_encode($test_params) . "<br>";
echo "签名字符串: {$signstr}<br>";
echo "手动签名: {$manual_sign}<br>";

// 使用类方法计算签名
$class_params = $test_params;
$class_sign = $epay->getSign($class_params);
echo "类方法签名: {$class_sign}<br>";

if($manual_sign === $class_sign) {
    echo "✅ 签名算法正确<br>";
} else {
    echo "❌ 签名算法错误<br>";
}

echo "<br><a href='test.php'>返回测试页面</a>";
?>
