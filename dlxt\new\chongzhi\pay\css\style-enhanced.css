body {
    font-family: 'Arial', sans-serif;
    background-color: #f0f0f0;
    color: #333;
    margin: 0;
    padding: 0;
}



.products {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}

.product-item {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    width: calc(50% - 20px);
}

.product-item label {
    cursor: pointer;
    display: block;
}

.product-title {
    font-weight: bold;
}

.product-description {
    display: block;
    margin-top: 5px;
}

.btn {
    width: auto;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
   
    color: white;
    cursor: pointer;
}

.btn:hover {
    background-color: #45a049;
}

.footer {
    margin-top: 20px;
    text-align: center;
}

.url {
    color: #06c;
    text-decoration: none;
}

.products {
    display: flex;
    flex-wrap: wrap;
    justify-content: center; /* 居中对齐，有助于处理最后一行 */
    gap: 20px;
}
.product-item {
    margin: 10px; /* 对每个项目应用相同的外边距 */
    /* 确保每个项目有固定宽度或最大宽度 */
}



.product-card {
    background-color: #fff;
    border: solid 1px #dbdbdb;
    border-radius: 20px;
    
    padding: 20px;
    width: 180px; /* 固定宽度 */
    height: 300px; /* 固定高度 */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 分配子元素间距 */
    transition: border 0.5s ease-in-out; /* 平滑过渡效果 */
}
.product-card input[type="radio"], .product-card input[type="checkbox"] {
    display: none; /* 隐藏所有单选按钮和复选框 */
}

.product-card:hover {
    border: 2px solid #007bff; /* 鼠标滑过时显示蓝色边框 */
}
.product-radio {
    display: none;
}

.product-card label {
    display: block;
    cursor: pointer;
    text-align: center;
}

.product-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-description {
    font-size: 14px;
    color: #666;
}
.product-card.selected {
    border-color: #007bff; /* 蓝色边框表示选中状态 */
    box-shadow: 0 0 8px rgba(0,123,255,0.5);
}
.serverlist {
    border: none; /* 移除边框 */
    outline: none; /* 移除点击（聚焦）时的边框 */
}
/*================*/
.text-center {
    width: 50%; /* 设置宽度为70% */
    margin: auto; /* 居中显示 */
    box-shadow: 0px 5px 40px 0px rgba(0, 0, 0, 0.06)!important; /* 添加轻微的盒子阴影 */
    padding: 20px; /* 添加一些内边距以避免内容紧贴容器边缘 */
    background-color: #fff; /* 背景色设置为白色或其他你希望的颜色 */
    border-radius: 8px; /* 若需要，添加边角圆润效果 */
}
/*nav*/
.fixed-nav {
    display: flex;
    align-items: center; /* 垂直居中 */
    justify-content: flex-end; /* 水平靠右 */
    height: 60px;
    position: fixed;
    top: 10px;
    left: 0; /* 覆盖整个宽度的左边起点 */
    right: 0; /* 覆盖整个宽度的右边起点 */
    width: 60%;
    max-width: 960px; /* 限制最大宽度 */
    margin: 0 auto; /* 保持自动居中 */
    background-color: rgba(255, 255, 255, 0.5); /* 半透明背景 */
    backdrop-filter: blur(10px); /* 背景模糊效果 */
    -webkit-backdrop-filter: blur(10px); /* Safari 浏览器兼容 */
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 100;
    padding: 10px 20px;
    border-radius: 40px; /* 圆角边框 */
    box-sizing: border-box; /* 确保宽度包括padding */
}

.account-info {
    display: flex; /* 使用 Flexbox */
    justify-content: flex-end; /* 内容靠右对齐 */
    align-items: center; /* 项目垂直居中 */
    width: 100%; /* 确保占满父容器的宽度 */
}
.account-info>input{
    margin-top: 5px;
}
input:focus {
    outline: none; /* 移除聚焦状态下的轮廓 */
}
.btnlist-1 {
    background-color: rgba(255, 255, 255, 0); /* 完全透明背景 */
    border: none; /* 去掉边框 */
    /* 添加其他必要的样式，例如内边距、字体大小等 */
}


.serverlist {
    max-width: 90%; 
    
}
/*==============角色卡片====================*/
.role-card {
    background-color: rgba(255, 255, 255, 0.8); /* 半透明背景，类似玻璃效果 */
    border: 1px solid #ff0000; /* 红色边框 */
    border-radius: 20px; /* 圆角边框 */
   box-shadow: 0px 0px 12px 6px rgb(177 0 0 / 10%);
    padding: 20px;
    margin: 10px auto 10px 0; /* 上下外边距为 10px，右侧外边距为自动，左侧为 0 */
    position: relative; /* 为了放置伪元素或图标 */
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* 左对齐子元素 */
    max-width: 400px; /* 或根据需要调整最大宽度 */
    box-sizing: border-box;
}


.role-card span {
    width: 100%; /* 使得 <span> 元素宽度与父元素相同 */
    margin-bottom: 10px; /* 在条目之间添加一些空间 */
}

.role-card span:last-child {
    margin-bottom: 0; /* 最后一个元素不需要底部外边距 */
}

.btnlist {
    border: none ; /* 去掉边框 */
    background: transparent; /* 透明背景 */
  
}

/* 以下是为了演示目的，假设你有图标类似 .icon 和一个验证标记类似 .checkmark */
.icon {
    position: absolute; /* 绝对定位 */
    top: 50%;
    left: 10px; /* 从左侧边缘的距离 */
    transform: translateY(-50%); /* 垂直居中 */
    width: 30px; /* 图标宽度 */
    height: 30px; /* 图标高度 */
    background-color: #ff0000; /* 图标背景颜色 */
    border-radius: 50%; /* 圆形图标 */
}

.checkmark {
    position: absolute; /* 绝对定位 */
    top: 50%;
    right: 10px; /* 从右侧边缘的距离 */
    transform: translateY(-50%); /* 垂直居中 */
    width: 20px; /* 验证标记宽度 */
    height: 20px; /* 验证标记高度 */
    background-color: #00ff00; /* 验证标记背景颜色 */
    border-radius: 50%; /* 圆形验证标记 */
}
/*=========================广告css---------*/
.scrolling-ads {
    margin-top: 20px;
    width: 50%; 
    overflow: hidden; 
    white-space: nowrap; 
    box-sizing: border-box;
    background-color: #f4f4f4;
    border-radius: 20px;
}

.ad {
    display: inline-block; 
    padding: 10px; 
    animation: scroll 12s linear infinite; 
}

@keyframes scroll {
    0% {
        transform: translateX(600%); 
    }
    100% {
        transform: translateX(-200%); 
    }
}


