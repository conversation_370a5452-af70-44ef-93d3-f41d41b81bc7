<?php
include('auth.php');
$act=isset($get['act'])?$get['act']:null;
?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<!--标签插件-->
<link rel="stylesheet" href="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.css">
<link href="/static/admin/css/style.min.css" rel="stylesheet">

<!-- 加载 Jquery -->
<script src="/static/admin/select/jquery-3.2.1.min.js"></script>
<!-- 加载 Select2 -->
<link href="/static/admin/select/select2.min.css" rel="stylesheet" />
<script src="/static/admin/select/select2.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<!-- 加载 下拉图片 -->
<link rel="stylesheet" type="text/css" href="/static/admin/image-picker/image-picker.css">
<script type="text/javascript" src="/static/admin/image-picker/image-picker.js"></script>
<style>
 .container {
  height: 60px;
  width: 90px;
  margin: 0 auto;
} 
 </style>
</head>
  
<body>
<div class="container-fluid p-t-15">
<?php
if($act=='addaward'){
?>
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return addaward(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				echo '<option value="ptb">平台币</option>';
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="" placeholder="请输入物品数量" />
            </div>
			<div class="form-group col-md-6">
				<label>奖励用途</label>
				<select name="types" class="form-control" id="types" >
				<option value="1">月卡</option>
				<option value="2">周卡</option>
				</select>
				<script>var selectorx = $('#types').select2(  );</script>
			</div>
			<div class="form-group col-md-6">
				<label>领奖时间</label>
				<small style="color:blue">周卡只能选择7天，否则奖励不生效</small>
				<select name="days" class="form-control" id="days">
				<option value="1">第1天/第1天</option>
				<option value="2">第2天/第2天</option>
				<option value="3">第3天/第3天</option>
				<option value="4">第4天/第4天</option>
				<option value="5">第5天/第5天</option>
				<option value="6">第6天/第6天</option>
				<option value="7">第7天/第7天</option>
				<option value="8">第8天/周卡禁选</option>
				<option value="9">第9天/周卡禁选</option>
				<option value="10">第10天/周卡禁选</option>
				<option value="11">第11天/周卡禁选</option>
				<option value="12">第12天/周卡禁选</option>
				<option value="13">第13天/周卡禁选</option>
				<option value="14">第14天/周卡禁选</option>
				<option value="15">第15天/周卡禁选</option>
				<option value="16">第16天/周卡禁选</option>
				<option value="17">第17天/周卡禁选</option>
				<option value="18">第18天/周卡禁选</option>
				<option value="19">第19天/周卡禁选</option>
				<option value="20">第20天/周卡禁选</option>
				<option value="21">第21天/周卡禁选</option>
				<option value="22">第22天/周卡禁选</option>
				<option value="23">第23天/周卡禁选</option>
				<option value="24">第24天/周卡禁选</option>
				<option value="25">第25天/周卡禁选</option>
				<option value="26">第26天/周卡禁选</option>
				<option value="27">第27天/周卡禁选</option>
				<option value="28">第28天/周卡禁选</option>
				<option value="29">第29天/周卡禁选</option>
				<option value="30">第30天/周卡禁选</option>
				</select>
				<script>var selectorx = $('#days').select2(  );</script>
			</div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'" >'.$res['value'].'</option>';
				
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认添加</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}else if($act=='editaward'){
	$id = intval($get['id']);
	$typesData=$DB->getRow("SELECT * FROM `vipitems` WHERE `id` ='" . $id . "' limit 1");
	if(!$typesData){echo "<script>layer.ready(function(){layer.msg('该奖品信息不存在', {icon: 2, time: 1500}, function(){window.location.href='javascript:history.go(-1)'});});</script>";exit();}
	
?> 
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">
          
          <form onsubmit="return editaward(this)" method="post" class="row">
			<div class="form-group col-md-6">
				<label>选择物品（仅支持物品和装备类型的道具）</label>
				<select name="selectitem" class="form-control select2" id="selectitem" >
				<?php
				$rs=$DB->query("SELECT * FROM `items` where `type`='1' || `type`='4' order by id");
				while($res = $rs->fetch())
				{
				if($typesData['itemid']== '88' ){
				echo '<option value="ptb" selected="selected">平台币</option>';
				}else{
				echo '<option value="ptb">平台币</option>';
				}
				if($typesData['itemid']== $res['itemid'] ){
				echo '<option value="'.$res['id'].'" selected="selected">'.$res['name'].'</option>';
				}else{
				echo '<option value="'.$res['id'].'">'.$res['name'].'</option>';
				}
				}
				?> 
				</select>
				<script>var selectorx = $('#selectitem').select2( {placeholder: '请选择'} );</script>
			</div>
            <div class="form-group col-md-6">
              <label>物品数量</label>
              <input type="number" class="form-control"  name="num" value="<?php echo $typesData['num'] ;?>" placeholder="请输入物品数量" />
              <input type="text" class="form-control"  name="id" value="<?php echo $typesData['id'] ;?>" style="display:none" placeholder="请输入分类名称" />
            </div>
			<div class="form-group col-md-6">
				<label>奖励用途</label>
				<select name="types" class="form-control" id="types" >
				<?php if($typesData['types']== 1 ){
				echo'	
				<option value="1" checked>月卡</option>
				<option value="2">周卡</option>';
				}else{
				echo'	
				<option value="2" checked>周卡</option>
				<option value="1">月卡</option>';
					
				}
				?>
				</select>
				<script>var selectorx = $('#types').select2(  );</script>
			</div>
			<div class="form-group col-md-6">
				<label>领奖时间</label>
				<small style="color:blue">周卡只能选择7天，否则奖励不生效</small>
				<select name="days" class="form-control" id="days">
				<option value="1" <?php if($typesData['days'] == '1'){ echo 'selected'; } ?> >第1天/第1天</option>
				<option value="2" <?php if($typesData['days'] == '2'){ echo 'selected'; } ?> >第2天/第2天</option>
				<option value="3" <?php if($typesData['days'] == '3'){ echo 'selected'; } ?> >第3天/第3天</option>
				<option value="4" <?php if($typesData['days'] == '4'){ echo 'selected'; } ?> >第4天/第4天</option>
				<option value="5" <?php if($typesData['days'] == '5'){ echo 'selected'; } ?> >第5天/第5天</option>
				<option value="6" <?php if($typesData['days'] == '6'){ echo 'selected'; } ?> >第6天/第6天</option>
				<option value="7" <?php if($typesData['days'] == '7'){ echo 'selected'; } ?> >第7天/第7天</option>
				<option value="8" <?php if($typesData['days'] == '8'){ echo 'selected'; } ?> >第8天/周卡禁选</option>
				<option value="9" <?php if($typesData['days'] == '9'){ echo 'selected'; } ?> >第9天/周卡禁选</option>
				<option value="10" <?php if($typesData['days'] == '10'){ echo 'selected'; } ?> >第10天/周卡禁选</option>
				<option value="11" <?php if($typesData['days'] == '11'){ echo 'selected'; } ?> >第11天/周卡禁选</option>
				<option value="12" <?php if($typesData['days'] == '12'){ echo 'selected'; } ?> >第12天/周卡禁选</option>
				<option value="13" <?php if($typesData['days'] == '13'){ echo 'selected'; } ?> >第13天/周卡禁选</option>
				<option value="14" <?php if($typesData['days'] == '14'){ echo 'selected'; } ?> >第14天/周卡禁选</option>
				<option value="15" <?php if($typesData['days'] == '15'){ echo 'selected'; } ?> >第15天/周卡禁选</option>
				<option value="16" <?php if($typesData['days'] == '16'){ echo 'selected'; } ?> >第16天/周卡禁选</option>
				<option value="17" <?php if($typesData['days'] == '17'){ echo 'selected'; } ?> >第17天/周卡禁选</option>
				<option value="18" <?php if($typesData['days'] == '18'){ echo 'selected'; } ?> >第18天/周卡禁选</option>
				<option value="19" <?php if($typesData['days'] == '19'){ echo 'selected'; } ?> >第19天/周卡禁选</option>
				<option value="20" <?php if($typesData['days'] == '20'){ echo 'selected'; } ?> >第20天/周卡禁选</option>
				<option value="21" <?php if($typesData['days'] == '21'){ echo 'selected'; } ?> >第21天/周卡禁选</option>
				<option value="22" <?php if($typesData['days'] == '22'){ echo 'selected'; } ?> >第22天/周卡禁选</option>
				<option value="23" <?php if($typesData['days'] == '23'){ echo 'selected'; } ?> >第23天/周卡禁选</option>
				<option value="24" <?php if($typesData['days'] == '24'){ echo 'selected'; } ?> >第24天/周卡禁选</option>
				<option value="25" <?php if($typesData['days'] == '25'){ echo 'selected'; } ?> >第25天/周卡禁选</option>
				<option value="26" <?php if($typesData['days'] == '26'){ echo 'selected'; } ?> >第26天/周卡禁选</option>
				<option value="27" <?php if($typesData['days'] == '27'){ echo 'selected'; } ?> >第27天/周卡禁选</option>
				<option value="28" <?php if($typesData['days'] == '28'){ echo 'selected'; } ?> >第28天/周卡禁选</option>
				<option value="29" <?php if($typesData['days'] == '29'){ echo 'selected'; } ?> >第29天/周卡禁选</option>
				<option value="30" <?php if($typesData['days'] == '30'){ echo 'selected'; } ?> >第30天/周卡禁选</option>
				</select>
				<script>var selectorx = $('#days').select2(  );</script>
			</div>
			<div class="form-group col-md-12"  >
				<label>选择图标</label>
				<select name="image" class="form-group image-picker show-html" style="height: 600px;">
				<?php
				$rs=$DB->query("SELECT * FROM `images` order by id");
				while($res = $rs->fetch())
				{
				if($typesData['image']== $res['value'] ){
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  selected="selected" >'.$res['value'].'</option>';
				}else{
					echo '<option  data-img-src="http://'.$_SERVER['HTTP_HOST'].$res['value'].'" data-img-class="container" value="'.$res['value'].'"  >'.$res['value'].'</option>';
				}
				}
				?> 
				</select>
				 <script type="text/javascript">
					$("select").imagepicker(
					);
				</script>
			</div>
            <div class="form-group col-md-12">
              <button type="submit" class="btn btn-primary ajax-post" target-form="add-form">确认修改</button>
              <button type="button" class="btn btn-default" onclick="javascript:history.back(-1);return false;">返 回</button>
            </div>
          </form>
 
        </div>
      </div>
    </div>
    
  </div>
<?php
}
?> 
</div>

<script src="/static/admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
<script src="/static/admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>


<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<!--标签插件-->
<script src="/static/admin/js/jquery-tags-input/jquery.tagsinput.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script>
function addaward(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=addaward',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
function editaward(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : './ajax.php?act=editaward',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        layer.alert(data.msg, {icon: 1,closeBtn: false}, function(){window.location.reload()});
	        //layer.alert(data.msg, {icon: 1,closeBtn: false});
	      }else{
	        layer.alert(data.msg, {icon: 2})
	      }
	    },
	    error:function(data){
	      layer.msg('服务器错误');
	      return false;
	    }
	  });
	  return false;
}
</script>
</body>
</html>