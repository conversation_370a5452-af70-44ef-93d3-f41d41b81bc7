<?php
// 测试回调功能
include "config.php";
require_once("lib/EpayCore.class.php");

echo "<h2>支付回调测试工具</h2>";

// 查询最新的未支付订单
$unpaidOrder = $DBDL->getRow("SELECT * FROM `pay_order` WHERE `status` = 0 ORDER BY id DESC LIMIT 1");

if($unpaidOrder) {
    echo "<h3>找到未支付订单：</h3>";
    echo "订单号: {$unpaidOrder['orderid']}<br>";
    echo "用户: {$unpaidOrder['user']}<br>";
    echo "金额: {$unpaidOrder['money']}<br>";
    echo "时间: {$unpaidOrder['date']} {$unpaidOrder['time']}<br>";
    
    // 模拟支付成功回调
    if(isset($_GET['simulate'])) {
        $out_trade_no = $unpaidOrder['orderid'];
        $trade_no = 'TEST' . time();
        $money = $unpaidOrder['money'];
        $name = $unpaidOrder['param'];
        
        // 构造回调参数
        $params = array(
            'pid' => $epay_config['pid'],
            'trade_no' => $trade_no,
            'out_trade_no' => $out_trade_no,
            'type' => 'alipay',
            'name' => $name,
            'money' => $money,
            'trade_status' => 'TRADE_SUCCESS',
            'param' => ''
        );
        
        // 生成签名
        $epay = new EpayCore($epay_config);
        $sign = $epay->getSign($params);
        
        $params['sign'] = $sign;
        $params['sign_type'] = 'MD5';
        
        // 构造回调URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $callback_url = $protocol."://".$host."/pay/notify_url.php?" . http_build_query($params);
        
        echo "<h3>模拟回调：</h3>";
        echo "回调URL: <a href='{$callback_url}' target='_blank'>{$callback_url}</a><br>";
        echo "签名结果: {$sign}<br>";
        
        // 直接调用回调
        echo "<h3>执行回调结果：</h3>";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $callback_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $result = curl_exec($ch);
        curl_close($ch);
        
        echo "回调返回: {$result}<br>";
        
        // 检查订单状态是否更新
        $updatedOrder = $DBDL->getRow("SELECT * FROM `pay_order` WHERE `orderid` = '{$out_trade_no}'");
        if($updatedOrder['status'] == 1) {
            echo "✅ 订单状态已更新为已支付<br>";
        } else {
            echo "❌ 订单状态未更新<br>";
        }
        
        // 检查是否生成了zhifu.txt文件
        if(file_exists('zhifu.txt')) {
            $content = file_get_contents('zhifu.txt');
            echo "✅ zhifu.txt文件已生成，内容: {$content}<br>";
        } else {
            echo "❌ zhifu.txt文件未生成<br>";
        }
    } else {
        echo "<br><a href='?simulate=1' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>模拟支付成功回调</a>";
    }
} else {
    echo "❌ 没有找到未支付的订单，请先创建一个测试订单。<br>";
    echo "<a href='test.php?test_pay=1'>创建测试订单</a>";
}

echo "<br><br><a href='test.php'>返回测试页面</a>";

// 显示zhifu.txt文件内容
if(file_exists('zhifu.txt')) {
    echo "<h3>zhifu.txt文件内容：</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents('zhifu.txt')) . "</pre>";
    echo "<a href='?clear_file=1' style='display: inline-block; padding: 5px 10px; background: #dc3545; color: white; text-decoration: none; border-radius: 3px; font-size: 12px;'>清空文件</a>";
}

// 清空文件
if(isset($_GET['clear_file'])) {
    file_put_contents('zhifu.txt', '');
    echo "<script>alert('文件已清空'); window.location.href='test_callback.php';</script>";
}
?>
