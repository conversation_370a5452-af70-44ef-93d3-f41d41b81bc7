<?php
include './common/main.php';

//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
//版权信息
$banquan=$DB->getRow("select * from `config` where `keys`='banquan' limit 1");
?>
<html lang="en">
<head>
	<title>用户登录 - <?php echo $title['values'];?></title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
<!--===============================================================================================-->	
	<link rel="icon" type="image/png" href="/static/login/images/icons/favicon.ico"/>
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/bootstrap/css/bootstrap.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/animate/animate.css">
<!--===============================================================================================-->	
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/css-hamburgers/hamburgers.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/vendor/select2/select2.min.css">
<!--===============================================================================================-->
	<link rel="stylesheet" type="text/css" href="/static/login/css/util.css">
	<link rel="stylesheet" type="text/css" href="/static/login/css/main.css">
<!--===============================================================================================-->
</head>
<body>
	
	<div class="limiter">
		<div class="container-login100">
			<div class="wrap-login100">
				<div class="login100-pic js-tilt" data-tilt>
					<img src="/static/login/images/img-01.png" alt="IMG">
				</div>
				<form class="login100-form validate-form" onsubmit="return login(this)" method="post" >
					<span class="login100-form-title">
						用户登录
					</span>

					<div class="wrap-input100 validate-input" data-validate = "请输入用户名">
						<input class="input100" type="text" name="username" placeholder="用户名">
						<span class="focus-input100"></span>
						<span class="symbol-input100">
							<i class="fa fa-user" aria-hidden="true"></i>
						</span>
					</div>

					<div class="wrap-input100 validate-input" data-validate = "请输入密码">
						<input class="input100" type="password" name="password" placeholder="密码">
						<span class="focus-input100"></span>
						<span class="symbol-input100">
							<i class="fa fa-lock" aria-hidden="true"></i>
						</span>
					</div>
					
					<div class="container-login100-form-btn">
						<button class="login100-form-btn" type="submit">
							登录
						</button>
					</div>

					<div class="text-center p-t-12">
						<a class="txt2" href="reg.php">
							没有账号？立即注册
						</a>
					</div>

					<div class="text-center p-t-12">
						<a class="txt2" href="index.php">
							返回首页
						</a>
					</div>

					<div class="text-center p-t-12">
						<a class="txt2" href="login/index.php">
							代理登录
						</a>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	

<link rel="stylesheet" type="text/css" href="/static/user/alert/sweetalert.css">
<script type="text/javascript" src="/static/login/vendor/jquery/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/static/user/alert/sweetalert-dev.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function login(obj){
	  var ii = layer.load(2, {shade:[0.1,'#fff']});
	  $.ajax({
	    type : 'POST',
	    url : 'ajax.php?act=login',
	    data : $(obj).serialize(),
	    dataType : 'json',
	    success : function(data) {
	      layer.close(ii);
	      if(data.code == 1){
	        swal("登录成功!", "此账户尚未绑定任何角色，请先进游戏绑定！", "success");
	        setTimeout(function(){
	          location.href = 'userinfo.php';
	        }, 2000);
	      }else if(data.code == 2){
	        swal("登录成功!", "登录成功，即将进入用户中心", "success");
	        setTimeout(function(){
	          location.href = 'userinfo.php';
	        }, 2000);
	      }else{
	        swal("登录失败!", data.msg, "error");
	      }
	    },
	    error:function(data){
	      layer.close(ii);
	      swal("请求失败!", "网络错误，请稍后重试", "error");
	    }
	  });
	  return false;
}

$(document).ready(function(){
    $('.validate-form .input100').each(function(){
        $(this).focus(function(){
           hideValidate(this);
        });
    });
});

function validate (input) {
    if($(input).attr('type') == 'text' || $(input).attr('name') == 'username') {
        if($(input).val().trim() == ''){
            return false;
        }
    }
    else if($(input).attr('type') == 'password') {
        if($(input).val().trim() == ''){
            return false;
        }
    }
}

function showValidate(input) {
    var thisAlert = $(input).parent();
    $(thisAlert).addClass('alert-validate');
}

function hideValidate(input) {
    var thisAlert = $(input).parent();
    $(thisAlert).removeClass('alert-validate');
}
</script>

<!--===============================================================================================-->
	<script src="/static/login/vendor/bootstrap/js/popper.js"></script>
	<script src="/static/login/vendor/bootstrap/js/bootstrap.min.js"></script>
<!--===============================================================================================-->
	<script src="/static/login/vendor/select2/select2.min.js"></script>
<!--===============================================================================================-->
	<script src="/static/login/vendor/tilt/tilt.jquery.min.js"></script>
	<script >
		$('.js-tilt').tilt({
			scale: 1.1
		})
	</script>
<!--===============================================================================================-->
	<script src="/static/login/js/main.js"></script>

</body>
</html>
