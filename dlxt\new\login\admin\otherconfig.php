<?php
include('./auth.php');
//网站信息
$title=$DB->getRow("select * from `config` where `keys`='title' limit 1");
$keywords=$DB->getRow("select * from `config` where `keys`='keywords' limit 1");
$description=$DB->getRow("select * from `config` where `keys`='description' limit 1");
//版权信息
$banquan=$DB->getRow("select * from `config` where `keys`='banquan' limit 1");
//版权信息
$logoimg=$DB->getRow("select * from `config` where `keys`='logoimg' limit 1");
//版权信息
$bjimg=$DB->getRow("select * from `config` where `keys`='bjimg' limit 1");
//网站信息
$anwzA=$DB->getRow("select * from `config` where `keys`='anwzA' limit 1");
//网站信息
$anljA=$DB->getRow("select * from `config` where `keys`='anljA' limit 1");
//网站信息
$anwzB=$DB->getRow("select * from `config` where `keys`='anwzB' limit 1");
//网站信息
$anljB=$DB->getRow("select * from `config` where `keys`='anljB' limit 1");
//网站信息
$anwzC=$DB->getRow("select * from `config` where `keys`='anwzC' limit 1");
//网站信息
$anljC=$DB->getRow("select * from `config` where `keys`='anljC' limit 1");
//网站信息
$wanjiaanwz=$DB->getRow("select * from `config` where `keys`='wanjiaanwz' limit 1");
//网站信息
$wanjiaanlj=$DB->getRow("select * from `config` where `keys`='wanjiaanlj' limit 1");
$kuaijimenua=$DB->getRow("select * from `config` where `keys`='kuaijimenua' limit 1");
$kuaijimenualj=$DB->getRow("select * from `config` where `keys`='kuaijimenualj' limit 1");
$kuaijimenub=$DB->getRow("select * from `config` where `keys`='kuaijimenub' limit 1");
$kuaijimenublj=$DB->getRow("select * from `config` where `keys`='kuaijimenublj' limit 1");
$kuaijimenuc=$DB->getRow("select * from `config` where `keys`='kuaijimenuc' limit 1");
$kuaijimenuclj=$DB->getRow("select * from `config` where `keys`='kuaijimenuclj' limit 1");
$kuaijimenud=$DB->getRow("select * from `config` where `keys`='kuaijimenud' limit 1");
$kuaijimenudlj=$DB->getRow("select * from `config` where `keys`='kuaijimenudlj' limit 1");
$kuaijimenue=$DB->getRow("select * from `config` where `keys`='kuaijimenue' limit 1");
$kuaijimenuelj=$DB->getRow("select * from `config` where `keys`='kuaijimenuelj' limit 1");
$kuaijiecaidan=$DB->getRow("select * from `config` where `keys`='kuaijiecaidan' limit 1");
$qqwxjump=$DB->getRow("select * from `config` where `keys`='qqwxjump' limit 1");


?>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title><?php echo $title['values'];?></title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="keywords" content="<?php echo $keywords['values'];?>">
<meta name="description" content="<?php echo $description['values'];?>">
<meta name="author" content="yinqi">
<link href="/static/admin/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/css/style.min.css" rel="stylesheet">
</head>
<body>
<div class="container-fluid p-t-15">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
		<h4>其他设置</h4>
		</div>
        <div class="card-body">
          <form onsubmit="return saveSetting(this)" method="post" name="edit-form" class="form-horizontal">
			<legend>网站信息设置</legend>
			<div class="form-group">
              <label class="col-xs-12">站点名称</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="title" rows="1" placeholder="请输入站点名称"><?php echo $title['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">站点关键词</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="keywords" rows="2" placeholder="请输入站点关键词"><?php echo $keywords['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">站点介绍</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="description" rows="3" placeholder="请输入站点介绍"><?php echo $description['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">版权设置</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="banquan" rows="2" placeholder="请输入版权内容（支持HTML标签）"><?php echo $banquan['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">LOGO设置</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="logoimg" rows="1" placeholder="请输入logo路径（支持超链接）"><?php echo $logoimg['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">登陆背景图设置</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="bjimg" rows="1" placeholder="请输入登陆背景图路径（支持超链接）"><?php echo $bjimg['values']; ?></textarea>
              </div>
            </div>
            <div class="form-group">
              <label class="col-xs-12">全站防红设置</label>
              <div class="col-xs-12">
                <label class="checkbox-inline" for="qqwxjump">
				  &nbsp;&nbsp;&nbsp;&nbsp;
                  <input type="checkbox"  name="qqwxjump" value="1" <?php echo ($qqwxjump['values']=='1'?'checked':''); ?> >
                  &nbsp;&nbsp;&nbsp;&nbsp;开启QQ微信跳转浏览器打勾，其他留空
                </label>
              </div>
            </div>
			<legend>首页设置</legend>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮文字一</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anwzA" rows="1" placeholder="自定义按钮文字一"><?php echo $anwzA['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮链接一</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anljA" rows="1" placeholder="自定义按钮链接一"><?php echo $anljA['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮文字二</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anwzB" rows="1" placeholder="自定义按钮文字二"><?php echo $anwzB['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮链接二</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anljB" rows="1" placeholder="自定义按钮链接二"><?php echo $anljB['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮文字三</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anwzC" rows="1" placeholder="自定义按钮文字三"><?php echo $anwzC['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">自定义按钮链接三</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="anljC" rows="1" placeholder="自定义按钮链接三"><?php echo $anljC['values']; ?></textarea>
              </div>
            </div>
			<legend>玩家页面头部自定义按钮设置</legend>
			<div class="form-group">
              <label class="col-xs-12">按钮文字</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="wanjiaanwz" rows="1" placeholder="自定义按钮文字"><?php echo $wanjiaanwz['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">跳转链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="wanjiaanlj" rows="1" placeholder="自定义按钮链接"><?php echo $wanjiaanlj['values']; ?></textarea>
              </div>
            </div>
            <div class="form-group">
              <label class="col-xs-12">快捷按钮总开关</label>
              <div class="col-xs-12">
                <label class="checkbox-inline" for="kuaijiecaidan">
				  &nbsp;&nbsp;&nbsp;&nbsp;
                  <input type="checkbox"  name="kuaijiecaidan" value="1" <?php echo ($kuaijiecaidan['values']=='1'?'checked':''); ?> >
                  &nbsp;&nbsp;&nbsp;&nbsp;开启打勾，关闭留空
                </label>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮一</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenua" rows="1" placeholder="自定义按钮文字"><?php echo $kuaijimenua['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮一链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenualj" rows="1" placeholder="请输入按钮链接"><?php echo $kuaijimenualj['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮二</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenub" rows="1" placeholder="请输入按钮文字"><?php echo $kuaijimenub['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮二链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenublj" rows="1" placeholder="请输入按钮链接"><?php echo $kuaijimenublj['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮三</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenuc" rows="1" placeholder="请输入按钮文字"><?php echo $kuaijimenuc['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮三链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenuclj" rows="1" placeholder="请输入按钮链接"><?php echo $kuaijimenuclj['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮四</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenud" rows="1" placeholder="请输入按钮文字"><?php echo $kuaijimenud['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮四链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenudlj" rows="1" placeholder="请输入按钮链接"><?php echo $kuaijimenudlj['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮五</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenue" rows="1" placeholder="请输入按钮文字"><?php echo $kuaijimenue['values']; ?></textarea>
              </div>
            </div>
			<div class="form-group">
              <label class="col-xs-12">快捷按钮五链接</label>
              <div class="col-xs-12">
                <textarea class="form-control" name="kuaijimenuelj" rows="1" placeholder="请输入按钮链接"><?php echo $kuaijimenuelj['values']; ?></textarea>
              </div>
            </div>
            <div class="form-group">
              <div class="col-xs-12">
                <button class="btn btn-primary" type="submit">提交</button>
              </div>
            </div>
          </form>
          
        </div>
      </div>
    </div>
    
  </div>
  
</div>
<script type="text/javascript" src="/static/admin/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/js/main.min.js"></script>
<script src="/static/admin/layer/layer.js"></script>
<script>
function saveSetting(obj){
  var ii = layer.load(2, {shade:[0.1,'#fff']});
  $.ajax({
    type : 'POST',
    url : './ajax.php?act=otherset',
    data : $(obj).serialize(),
    dataType : 'json',
    success : function(data) {
      layer.close(ii);
      if(data.code == 1){
        layer.alert(data.msg, {
          icon: 1,
          closeBtn: false
        }, function(){
          window.location.reload()
        });
      }else{
        layer.alert(data.msg, {icon: 2})
      }
    },
    error:function(data){
      layer.msg('服务器错误');
      return false;
    }
  });
  return false;
}
</script>
</body>
</html>